#*****************************************************************************
# C O P Y R I G H T
#-----------------------------------------------------------------------------
# Copyright (c) 2006-2012 by Robert <PERSON>.            All rights reserved.
#
# This file is property of Robert Bosch GmbH. Any unauthorised copy, use or 
# distribution is an offensive act against international law and may me 
# prosecuted under federal law. Its content is company confidential.
#-----------------------------------------------------------------------------
# D E S C R I P T I O N
#-----------------------------------------------------------------------------
#     $ProjectName: d:/MKS_Data/Projects/TST/PP_GEN6/PF_SIP6/CarMakerHIL/script/script_shared.pj $ 
#     $Source: rb_reportFunctions.tcl $ 
#     $Revision: 1.5 $ 
#     $Date: 2016/09/06 09:20:44CEST $  
#     $State: in_work $ 
#
#
#    Purpose:        Script to create test report for CM HIL TestRuns
#                    
#    Target system:   Windows XP/ Windows7
#    Compiler:        tcl   
#-----------------------------------------------------------------------------
# N O T E S
#-----------------------------------------------------------------------------
#     Notes:
#-----------------------------------------------------------------------------
# A U T H O R   I D E N T I T Y
#-----------------------------------------------------------------------------
#      Network account       Name                   Department
#      ----------------      --------------------   ---------------------------
#                            Stephan Stühmer        AE-BE/EPM5
#
#******************************************************************************

proc ExecuteTestManager { UseCANape CANapeProjectDir TestRunArray UserName MDFStorageDir} {
	global Qu
 
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
 # Begin script
 #--------------------------------------------------------------------------------------------------------------
	StopSim
	WaitForStatus idle
	set StartDateTime [clock format [clock seconds] -format {%Y-%m-%d_%H-%M-%S}]
	set ProjectFolder [pwd]
 #--------------------------------------------------------------------------------------------------------------
 # CANape
 #--------------------------------------------------------------------------------------------------------------
	set dir "../../../HIL-Automation/Data/TestRun/Script/tcom"
	#pkg_mkIndex $dir
	package ifneeded tcom 3.9 [list load [file join $dir tcom.dll]]
	package require tcom 
	#set MdfFolder [format "%s/SimOutput/rt1/CANapeMeasurement" $ProjectFolder]
	set MdfFolder [format "%s" $MDFStorageDir]
	set MeasurementSubFolder $StartDateTime
	if {$UseCANape == 1} {
		if {[info exists CANapeApp] == 1} {
			unset CANapeApp
		}
		Log "Creating COM-object canape.Application..."
		set CANapeApp [::tcom::ref createobject "canape.Application"]
		#if { [catch [set CANapeApp [::tcom::ref createobject "canape.Application"]]] } {
		#	Log "Error while creating COM-object canape.Application"
		#	#$CANapeApp QuitNonModal
		#} else {
		#	Log "Successfully created COM-object canape.Application"
		#}
		Log "Opening CANape project..."
		if { [catch [$CANapeApp Open $CANapeProjectDir 1]] } {
			Log "Error while opening CANape project in %s" $CANapeProjectDir
			set UseCANape 0
			set CANapeMeasurement 0
			$CANapeApp QuitNonModal
		} else {
			Log "Successfully set CANape project directory to %s" $CANapeProjectDir
			Log "Creating COM-object CANape Measurement..."
			if { [catch [set CANapeMeasurement [$CANapeApp Measurement]]] } {
				Log "Created COM-object CANape Measurement but caught error though. Proceeding..."
				#set UseCANape 0
				#set CANapeMeasurement 0
				#$CANapeApp QuitNonModal
			} else {
				Log "Successfully created COM-object CANape Measurement"
			}
		}
		
		
		#$CANapeApp Open $CANapeProjectDir 1
		
		
		
		#set MdfFolderRel2Prj
		
		
	} else {
		set CANapeMeasurement 0
	}
	Log "UseCANape = %d" $UseCANape
	#Log $CANapeMeasurement
 #--------------------------------------------------------------------------------------------------------------
 # XML: write header
 #--------------------------------------------------------------------------------------------------------------
	set dummy [file mkdir ../../../HIL-Automation/testReports]
	set ReportFileName	[format "report_%s" $StartDateTime]
	set ReportFileNameXml [format "../../../HIL-Automation/testReports/%s.xml" $ReportFileName]
	set openedGroups 0
	set ReportFileId [open $ReportFileNameXml "w"]
	fconfigure $ReportFileId -blocking 0
	puts $ReportFileId "<?xml version='1.0' encoding='ISO-8859-1' standalone='yes' ?>"
	set data [format "<testmodule starttime='%s' timestamp='0.000000'>" [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]]
	puts $ReportFileId $data
	puts $ReportFileId "<engineer>"
	puts $ReportFileId "\t<xinfo>"
	set user_name [file tail $UserName]
	set data [format "\t\t<name>%s</name>" $user_name]
	puts $ReportFileId $data
	puts $ReportFileId "\t\t<description></description>"
	puts $ReportFileId "\t</xinfo>"
	puts $ReportFileId "</engineer>"
	puts $ReportFileId "<testsetup>"
	puts $ReportFileId "\t<xinfo>"
	puts $ReportFileId "\t\t<name>Testtyp</name>"
	puts $ReportFileId "\t\t<description>HIL</description>"
	puts $ReportFileId "\t</xinfo>"
	puts $ReportFileId "</testsetup>"
	#
	set TestModuleStatus "pass"
	# open one group
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
 # TestRun Control
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
	set numGroups [expr [llength $TestRunArray]/2]
	set NumTotalRuns 0
	for {set runs 0} {$runs < $numGroups} {incr runs} {
		incr NumTotalRuns [lrange $TestRunArray [expr $runs*2+1] [expr $runs*2+1]]
	}
	set NumStartedRuns 0
	set groupCounter 0
	while {$groupCounter < $numGroups} {
		#-Group---------------------------------------
		set TestRunName [lrange $TestRunArray [expr $groupCounter*2] [expr $groupCounter*2]]
		set repetitions [lrange $TestRunArray [expr $groupCounter*2+1] [expr $groupCounter*2+1]]
		#
		puts $ReportFileId "\t<testgroup>"
		puts $ReportFileId [format "\t\t<title>%s</title>" $TestRunName]
		incr openedGroups
		set repetitionCounter 1
		set TestGroupStatus "pass"
		while {$repetitionCounter <= $repetitions} {
			incr NumStartedRuns
			Log "Start TestRun %d of %d in group %s." $repetitionCounter $repetitions $TestRunName
			Log "(%d/%d)" $NumStartedRuns $NumTotalRuns
			set loadTestRunResult [LoadTestRun $TestRunName]
			if {[string compare $loadTestRunResult ""] == 0} {
				# make variation
				set TestRunVariationSring [format "run no. %d" $repetitionCounter]
				
				if {$UseCANape == 1} {
					set DateTime [clock format [clock seconds] -format {%Y-%m-%d_%H-%M-%S}]
					set MdfName [format "%s/%s/%s_%s_%04d.mdf" $MdfFolder $MeasurementSubFolder $TestRunName $DateTime $repetitionCounter]
					if { [catch [$CANapeMeasurement -set MDFFilename $MdfName]] } {
						Log "Error while setting CANape MDF-FileName to %s" $MdfName
					} else {
						Log "Successfully set CANape MDF-FileName to %s" $MdfName
					}
				}
			
				set TestRunStatus [StartLoadedTestRunWithReport $ReportFileId $TestRunName $TestRunVariationSring $CANapeMeasurement $MdfFolder $MeasurementSubFolder]
				
				flush $ReportFileId
				
				if {$TestRunStatus == 0} {
					set TestRunStatusString "pass"
				} else {
					if  { $TestRunStatus > 0 } {
						set TestRunStatusString "warn"
						set TestGroupStatus "warn"
						set TestModuleStatus "warn"
					} else {
						set TestRunStatusString "fail"
						set TestGroupStatus "fail"
						set TestModuleStatus "fail"
					}
				}
				Log "Completed TestRun %d of %d: %s\n" $repetitionCounter $repetitions $TestRunStatusString
			} else {
				Log "Could not load this TestRun."
				set TestGroupStatus "fail"
				set TestModuleStatus "fail"
			}
			incr repetitionCounter
		}
		set dateTime [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]
		set data [format "\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $TestGroupStatus]
		puts $ReportFileId $data
		puts $ReportFileId "\t</testgroup>"
		incr openedGroups -1
		incr groupCounter
	}
#--------------------------------------------------------------------------------------------------------------
# XML: close TestGroups
#--------------------------------------------------------------------------------------------------------------
	for {set dummy 0} {$openedGroups > 0} {incr openedGroups -1} {
		puts $ReportFileId "\t</testgroup>"
	}
 #--------------------------------------------------------------------------------------------------------------
 # XML: close TestModule
 #--------------------------------------------------------------------------------------------------------------
	set dateTime [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]
	set data [format "\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $TestModuleStatus]
	puts $ReportFileId $data
	puts $ReportFileId "</testmodule>"
	close $ReportFileId
	#create HTML from XML via XSLT
	set ReportFileNameHtml [format "../../../HIL-Automation/testReports/%s.html" $ReportFileName]
	exec ../../../HIL-Automation/util/msxsl.exe $ReportFileNameXml ../../../HIL-Automation/util/onepage.xslt -o $ReportFileNameHtml
 #--------------------------------------------------------------------------------------------------------------
 # CANape
 #--------------------------------------------------------------------------------------------------------------
	if {$UseCANape == 1} {
		$CANapeApp QuitNonModal
	}
}

proc EvalTargetPos { reportFileFid } {
    set ReportFileId $reportFileFid
	global Qu
	QuantSubscribe {Car.Fr1.rz RB.TargetPos.Rating Car.BdySensor.RearAxisCenter.Pos_0.x Car.BdySensor.RearAxisCenter.Pos_0.y RB.ParkingSpace.Obj1Id RB.ParkingSpace.Obj2Id RB.ParkingSpace.Length RB.ParkingSpace.DriveBySpeed RB.ParkingSpace.LatRefPresent RB.ParkingSpace.Slope RB.ParkingSpace.Side RB.TargetPos.Collisions DASensor.DistFront.relvTgt.Id DASensor.DistRear.relvTgt.Id RB.TargetPos.NumberOfMoves RB.EgoVehicle.XPosFrPS RB.EgoVehicle.YPosFrPS RB.EgoVehicle.WFL.Dist RB.EgoVehicle.WFR.Dist RB.EgoVehicle.WRL.Dist RB.EgoVehicle.WRR.Dist RB.EgoVehicle.WheelCollision}

	namespace import ::tcl::mathfunc::*
	
	DVAWrite RB.TargetPos.TestNowFlag 1 1
	sleep 2
	
	#set data [format "\t\t\t<testgroup starttime='%f'>&#160;&#160;&#160;&#160;Check park position" $Qu(Time)]
	#puts $ReportFileId $data
	set overallStatus 0
	set error 0
	set warn 0
	
	set psDescInfoValName "Driver.ParkingSpace.nDefinedSpaces"
	set numDefinedSpaces [IFileRead TestRun $psDescInfoValName 0]
	
	set psNum -1
	
	
	#--------------------------------------------------------------------------------------------------------------------
	# evaluate target position
	#-----------------------------------------------------------------------------------------------
	set testCaseStatus "pass"
		#--------------------------------------------------------------------------------------------------------------------
		# check target position
		#--------------------------------------------------------------------------------------------------------------------
		set psCarCornersInside [expr [abs $Qu(RB.TargetPos.Rating)]]
		if {$psCarCornersInside == 5} {
			incr psCarCornersInside -1
		}
		set mustVal 4
		if {$psCarCornersInside >= $mustVal} {
			set testStepStatus "pass"
		} else {
			set testStepStatus "fail"
			set testCaseStatus "fail"
			incr overallStatus -1
		}
		set LogString [format "Check value condition: is car completely inside parking space? Corners of car in PS = %d; expected = 4" $psCarCornersInside]
		Log "%s: %s" $LogString $testStepStatus
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $testStepStatus $LogString]
		puts $ReportFileId $data
		#--------------------------------------------------------------------------------------------------------------------
		# check angle in target position
		#--------------------------------------------------------------------------------------------------------------------
		set carAngle $Qu(Car.Fr1.rz)
		set M_PI 3.14159265358979323846
		set ALLOWED_ANGLE_DEVIATION_DEG 3
		# if the car drives circles during the maneuver, the angle grows and grows
		# so 'unwind' the angle to a value between 0 and 2pi (360 deg)
		if {$carAngle > [expr 2*$M_PI]} {
			# unwind from positive values > 360deg
			while {$carAngle >= [expr 2*$M_PI]} {
				set carAngle [expr $carAngle - 2*$M_PI]
			}
		} else {
			if {$carAngle < 0} {
				# unwind from negative values
				while {$carAngle < 0} {
					set carAngle [expr $carAngle + 2*$M_PI]
				}
			}
			# else: the angle is already in the correct range between 0 and 360 deg
		}
		set dAngle [expr abs($carAngle - $Qu(RB.ParkingSpace.Slope))]
		if {$dAngle > $M_PI} {
			set dAngle [expr abs($dAngle - 2 * $M_PI)]
		}
		
		set dAngle [expr $dAngle * 180 / $M_PI]
		set carAngle [expr $carAngle * 180 / $M_PI]
		set psAngle [expr $Qu(RB.ParkingSpace.Slope) * 180 / $M_PI ]
		if { $dAngle < $ALLOWED_ANGLE_DEVIATION_DEG }	{
			Log "Angle is good. Must: %f° Is: %f°" $Qu(RB.ParkingSpace.Slope) $carAngle
			set testStepStatus "pass"
		} else {
			Log "Angle is bad. Must: %f° Is: %f°" $Qu(RB.ParkingSpace.Slope) $carAngle
			set testStepStatus "fail"
			set testCaseStatus "fail"
			incr overallStatus -1
		}
		
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: angle of car in parking-space = %f°; expected = %f°</teststep>" $Qu(Time) $testStepStatus $carAngle $psAngle]
		puts $ReportFileId $data
		#--------------------------------------------------------------------------------------------------------------------
		# check distance to curb-stone if one was detected
		#--------------------------------------------------------------------------------------------------------------------
		if {$Qu(RB.ParkingSpace.LatRefPresent) >= 1} {
			set testStepStatus "pass"
			set mustValMin 0.05
			set mustValMax 0.25
			if {$Qu(RB.ParkingSpace.Side) == 0} {
				set psMeasValF $Qu(RB.EgoVehicle.WFL.Dist)
				set psMeasValR $Qu(RB.EgoVehicle.WRL.Dist)
			} else {
				set psMeasValF $Qu(RB.EgoVehicle.WFR.Dist)
				set psMeasValR $Qu(RB.EgoVehicle.WRR.Dist)
			}
			if {$psMeasValF > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$psMeasValF < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Distance to curb stone: front wheel: %f m; expected: %f &lt; dist &lt; %f</teststep>" $Qu(Time) $testStepStatus $psMeasValF $mustValMin $mustValMax]
			puts $ReportFileId $data
			
			set testStepStatus "pass"
			if {$psMeasValR > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$psMeasValR < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Distance to curb stone: rear wheel: %f m; expected: %f &lt; dist &lt; %f</teststep>" $Qu(Time) $testStepStatus $psMeasValR $mustValMin $mustValMax]
			puts $ReportFileId $data
			
			set testStepStatus "pass"
			set resultWordCurb "no"
			if {$Qu(RB.EgoVehicle.WheelCollision) != 0} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				set resultWordCurb "yes"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Collision of a wheel with curb stone detected: %s</teststep>" $Qu(Time) $testStepStatus $resultWordCurb]
			puts $ReportFileId $data
		}
		#--------------------------------------------------------------------------------------------------------------------
		# Position relative to parking space origin
		#--------------------------------------------------------------------------------------------------------------------
		set testStepStatus "pass"
		if {$Qu(RB.EgoVehicle.XPosFrPS) < -12 || $Qu(RB.EgoVehicle.XPosFrPS) > 0} {
			set testStepStatus "fail"
			set testCaseStatus "fail"
			incr overallStatus -1
		}
		if {[expr [abs $Qu(RB.EgoVehicle.YPosFrPS)]] > 2 || [expr [abs $Qu(RB.EgoVehicle.YPosFrPS)]] < 0} {
			set testStepStatus "fail"
			set testCaseStatus "fail"
			incr overallStatus -1
		}
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: position relative to parking space origin: x = %fm (expected: 0m &gt; x &gt; -12m) ; y = %fm (expected: 0m &lt; abs(y) &lt; 2m)</teststep>" $Qu(Time) $testStepStatus $Qu(RB.EgoVehicle.XPosFrPS) $Qu(RB.EgoVehicle.YPosFrPS)]
		puts $ReportFileId $data

	#--------------------------------------------------------------------------------------------------------------------
	# check if collision happened
	#--------------------------------------------------------------------------------------------------------------------
		set testStepStatus "pass"
		if {$Qu(RB.TargetPos.Collisions) > 0} {
			set testStepStatus "fail"
			incr overallStatus -1
		}
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: collisions of the ego vehicle with objects = %d; expected = 0</teststep>" $Qu(Time) $testStepStatus $Qu(RB.TargetPos.Collisions)]
		puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------------
	# check number of moves
	#--------------------------------------------------------------------------------------------------------------------
		#if {$psCarCornersInside == 4} {
		#	if { $overallStatus == 0} {
		#		set data [format "\t\t\t<description>Test-result summary: parked with %d moves.</description>" $Qu(RB.TargetPos.NumberOfMoves)]
		#		puts $ReportFileId $data
		#	} else {
		#		set data [format "\t\t\t\t<description>Test-result summary: parked with errors.</description>"]
		#		puts $ReportFileId $data
		#	}
		#} else {
		#	set data [format "\t\t\t\t<description>Test-result summary: didn't park.</description>"]
		#	puts $ReportFileId $data
		#}
		set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Log value condition: number of moves = %d</teststep>" $Qu(Time) $Qu(RB.TargetPos.NumberOfMoves)]
		puts $ReportFileId $data
	#
	#
	#
	set evalTargetPosStatus "pass"
	if {$overallStatus == 0 && $warn > 0} {
		set overallStatus 1
		set evalTargetPosStatus "warn"
	}
	
	if {$overallStatus < 0 } {
		set evalTargetPosStatus "fail"
	}
	
	return $evalTargetPosStatus
}

proc EvalTargetPos_cPSC { reportFileFid } {
    set ReportFileId $reportFileFid
	global Qu
	QuantSubscribe { RB.EgoVehicle.DistRear RB.TargetPos.CPSCYawAngleRel2CPS RB.TargetPos.CPSCDist2FrontOrientationLine RB.ParkingSpace.Length RB.ParkingSpace.DriveBySpeed RB.ParkingSpace.Side RB.TargetPos.Collisions RB.TargetPos.NumberOfMoves RB.EgoVehicle.WFL.Dist RB.EgoVehicle.WFR.Dist RB.EgoVehicle.WRL.Dist RB.EgoVehicle.WRR.Dist RB.EgoVehicle.WheelCollision}
					
	namespace import ::tcl::mathfunc::*
	
	DVAWrite RB.TargetPos.TestNowFlag 1 1
	sleep 2
	
	#set data [format "\t\t\t<testgroup starttime='%f'>&#160;&#160;&#160;&#160;Check park position" $Qu(Time)]
	#puts $ReportFileId $data
	set overallStatus 0
	set error 0
	set warn 0
	
	set psDescInfoValName "Driver.ParkingSpace.nDefinedSpaces"
	set numDefinedSpaces [IFileRead TestRun $psDescInfoValName 0]
	
	set psNum -1
	
	
	#--------------------------------------------------------------------------------------------------------------------
	# evaluate target position
	#-----------------------------------------------------------------------------------------------
	set testCaseStatus "pass"
		#--------------------------------------------------------------------------------------------------------------------
		# check angle in target position
		#--------------------------------------------------------------------------------------------------------------------
		set mustValMin -2.0
		set mustValMax 2.0
		set angleMeasVal $Qu(RB.TargetPos.CPSCYawAngleRel2CPS)
		set testStepStatus "pass"
		if {$angleMeasVal > $mustValMax} {
			set testStepStatus "fail"
			set testCaseStatus "fail"
			incr overallStatus -1
		}
		if {$angleMeasVal < $mustValMin} {
			set testStepStatus "fail"
			set testCaseStatus "fail"
			incr overallStatus -1
		}
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: angle of car in parking-space = %f °; expected: %f° &lt; angle &lt; %f °</teststep>" $Qu(Time) $testStepStatus $angleMeasVal $mustValMin $mustValMax]
		puts $ReportFileId $data
		
		#--------------------------------------------------------------------------------------------------------------------
		# check distance to front-orientation line in target position
		#--------------------------------------------------------------------------------------------------------------------
		set mustValMin -0.2
		set mustValMax 0.2
		set distMeasVal $Qu(RB.TargetPos.CPSCDist2FrontOrientationLine)
		set testStepStatus "pass"
		
		if {$Qu(RB.EgoVehicle.DistRear) > 0.25 || $Qu(RB.EgoVehicle.DistRear) <= 0} {
			if {$distMeasVal > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$distMeasVal < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
		} else {
			set testStepStatus "na"
		}
	
		
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: distance to front-orientation line = %f m; expected: %f m &lt; dist &lt; %f m</teststep>" $Qu(Time) $testStepStatus $distMeasVal $mustValMin $mustValMax]
		puts $ReportFileId $data
		
		#--------------------------------------------------------------------------------------------------------------------
		# check distance to rear
		#--------------------------------------------------------------------------------------------------------------------
		set mustValMin 0.15
		set mustValMax 0.35
		set distMeasVal $Qu(RB.EgoVehicle.DistRear)
		set testStepStatus "pass"
		
		if {$Qu(RB.EgoVehicle.DistRear) > 0} {
			if {$distMeasVal > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$distMeasVal < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
		} else {
			set testStepStatus "na"
		}
	
		
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: distance at rear side = %f m; expected: %f m &lt; dist &lt; %f m</teststep>" $Qu(Time) $testStepStatus $distMeasVal $mustValMin $mustValMax]
		puts $ReportFileId $data
		
		#--------------------------------------------------------------------------------------------------------------------
		# check distance to cars at both sides
		#--------------------------------------------------------------------------------------------------------------------
			set mustValMin 0.50
			set mustValMax 0.90
			set psMeasValFL $Qu(RB.EgoVehicle.WFL.Dist)
			set psMeasValRL $Qu(RB.EgoVehicle.WRL.Dist)
			set psMeasValFR $Qu(RB.EgoVehicle.WFR.Dist)
			set psMeasValRR $Qu(RB.EgoVehicle.WRR.Dist)
			set testStepStatus "pass"
			if {$psMeasValFR > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$psMeasValFR < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Distance to the right: front wheel: %f m; expected: %f &lt; dist &lt; %f</teststep>" $Qu(Time) $testStepStatus $psMeasValFR $mustValMin $mustValMax]
			puts $ReportFileId $data
			
			set testStepStatus "pass"
			if {$psMeasValRR > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$psMeasValRR < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Distance to the right: rear wheel: %f m; expected: %f &lt; dist &lt; %f</teststep>" $Qu(Time) $testStepStatus $psMeasValRR $mustValMin $mustValMax]
			puts $ReportFileId $data
			
			set testStepStatus "pass"
			if {$psMeasValFL > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$psMeasValFL < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Distance to the left: front wheel: %f m; expected: %f &lt; dist &lt; %f</teststep>" $Qu(Time) $testStepStatus $psMeasValFL $mustValMin $mustValMax]
			puts $ReportFileId $data
			
			set testStepStatus "pass"
			if {$psMeasValRL > $mustValMax} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			if {$psMeasValRL < $mustValMin} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Distance to the left: rear wheel: %f m; expected: %f &lt; dist &lt; %f</teststep>" $Qu(Time) $testStepStatus $psMeasValRL $mustValMin $mustValMax]
			puts $ReportFileId $data
			
			set testStepStatus "pass"
			set resultWordCurb "no"
			if {$Qu(RB.EgoVehicle.WheelCollision) != 0} {
				set testStepStatus "fail"
				set testCaseStatus "fail"
				set resultWordCurb "yes"
				incr overallStatus -1
			}
			set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Collision with curb stone detected: %s</teststep>" $Qu(Time) $testStepStatus $resultWordCurb]
			puts $ReportFileId $data

	#--------------------------------------------------------------------------------------------------------------------
	# check if collision happened
	#--------------------------------------------------------------------------------------------------------------------
		set testStepStatus "pass"
		if {$Qu(RB.TargetPos.Collisions) > 0} {
			set testStepStatus "fail"
			incr overallStatus -1
		}
		set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: detected collisions = %d; expected = 0</teststep>" $Qu(Time) $testStepStatus $Qu(RB.TargetPos.Collisions)]
		puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------------
	# check number of moves
	#--------------------------------------------------------------------------------------------------------------------
		#if {$psCarCornersInside == 4} {
		#	if { $overallStatus == 0} {
		#		set data [format "\t\t\t<description>Test-result summary: parked with %d moves.</description>" $Qu(RB.TargetPos.NumberOfMoves)]
		#		puts $ReportFileId $data
		#	} else {
		#		set data [format "\t\t\t\t<description>Test-result summary: parked with errors.</description>"]
		#		puts $ReportFileId $data
		#	}
		#} else {
		#	set data [format "\t\t\t\t<description>Test-result summary: didn't park.</description>"]
		#	puts $ReportFileId $data
		#}
		set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Log value condition: number of moves = %d</teststep>" $Qu(Time) $Qu(RB.TargetPos.NumberOfMoves)]
		puts $ReportFileId $data
	#
	#
	#
	set evalTargetPosStatus "pass"
	if {$overallStatus == 0 && $warn > 0} {
		set overallStatus 1
		set evalTargetPosStatus "warn"
	}
	
	if {$overallStatus < 0 } {
		set evalTargetPosStatus "fail"
	}
	
	return $evalTargetPosStatus
}


proc CheckDesiredSceneAttributes { ReportFileId } {
	namespace import ::tcl::mathfunc::*
	#------------------------------------------------------------------------
	# check setpoint values
	#------------------------------------------------------------------------
	DVAWrite RB.TargetPos.TestNowFlag 1 1
	sleep 2
	
	global Qu
	QuantSubscribe { DASensor.DistRear.relvTgt.Id DASensor.DistFront.relvTgt.Id RB.ParkingSpace.Length RB.ParkingSpace.DriveBySpeed }
	
	set psDescInfoValName "Driver.ParkingSpace.nDefinedSpaces"
	set numDefinedSpaces [IFileRead TestRun $psDescInfoValName 0]
	
	set psNum -1
	
	set OverAllResult "pass"
	#------------------------------------------------------------------------
	# check if found parking space is defined in TestRun
	#------------------------------------------------------------------------
	for {set k 0} {$k < $numDefinedSpaces} {incr k 1} {
		set psName [format "Driver.ParkingSpace.%d" $k]
		set psDescInfoValName [format "%s.%s" $psName "Obj1Id"]
		set psDescVal [IFileRead TestRun $psDescInfoValName -1]		
		if {$psDescVal > -1} {
			#set LogString [format "ObjectId from TestRun = %d; Object ID from RearDist = %d" $psDescVal $Qu(DASensor.DistRear.relvTgt.Id)]
			#Log "%s" $LogString				
			if {$psDescVal == $Qu(DASensor.DistRear.relvTgt.Id)} {
				set psDescInfoValName [format "%s.%s" $psName "Obj2Id"]
				set psDescVal [IFileRead TestRun $psDescInfoValName -1]
				if {$psDescVal > -1} {
					#set LogString1 [format "ObjectId from TestRun = %d; Object ID from FrontDist = %d" $psDescVal $Qu(DASensor.DistFront.relvTgt.Id)]
					#Log "%s" $LogString1
					if {$psDescVal == $Qu(DASensor.DistFront.relvTgt.Id)} {
						set psNum $k
					}
				}
			}
		}
	}
	if {$psNum == 0} {
		set TestStepStatus "pass"
	} else {
		set TestStepStatus "fail"
		set OverAllResult "fail"
		incr warn
	}
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Parked in defined parking space number %d in TestRun info-file. Expected: ParkingSpace 0.</teststep>" $Qu(Time) $TestStepStatus $psNum]
	puts $ReportFileId $data
	
	set psName [format "Driver.ParkingSpace.%d" $psNum]
	#------------------------------------------------------------------------
	# check parking space length
	#------------------------------------------------------------------------
	set psDescValName "Length"
	set psMeasVal $Qu(RB.ParkingSpace.Length)
	set tolerance 1
	
	set psDescInfoValName [format "%s.%s" $psName $psDescValName]
	set psDescVal [IFileRead TestRun $psDescInfoValName -1]
	if {$psDescVal == -1} {
		set TestStepStatus "warn"
		if { $OverAllResult != "fail"} {
			set OverAllResult "warn"
		}
		incr warn
	} else {
		if {[expr abs($psMeasVal - $psDescVal)] < $tolerance} {
			set TestStepStatus "pass"
		} else {
			set TestStepStatus "fail"
			set OverAllResult "fail"
		}
	}
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: PS-%sm (measured by HIL): %fm; expected = %fm</teststep>" $Qu(Time) $TestStepStatus $psDescValName $psMeasVal $psDescVal]
	puts $ReportFileId $data
	#set data [format "\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: PS-%s (measured by ECU): feature not yet supported</teststep>" $Qu(Time) $psDescValName]
	#puts $ReportFileId $data
	#------------------------------------------------------------------------
	# check drive-by speed
	#------------------------------------------------------------------------
	set psDescValName "DriveBySpeed"
	set psMeasVal $Qu(RB.ParkingSpace.DriveBySpeed)
	set tolerance 2
	set psDescInfoValName [format "%s.%s" $psName $psDescValName]
	set psDescVal [IFileRead TestRun $psDescInfoValName -1]
	if {$psDescVal == -1} {
		set TestStepStatus "warn"
		if { $OverAllResult != "fail"} {
			set OverAllResult "warn"
		}
		incr warn
	} else {
		if {[expr abs($psMeasVal - $psDescVal)] < $tolerance} {
			set TestStepStatus "pass"
		} else {
			set TestStepStatus "fail"
			set OverAllResult "fail"
		}
	}
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;Check value condition: PS-%s = %fkm/h; expected = %fkm/h</teststep>" $Qu(Time) $TestStepStatus $psDescValName $psMeasVal $psDescVal]
	puts $ReportFileId $data

	return $OverAllResult
}



proc CheckFrontWheelAngle { ReportFileId } {
	#--------------------------------------------------------------------------------------------------------------------
	# check angle of the front wheels (Have the wheels turned straight forward in the end?)
	#--------------------------------------------------------------------------------------------------------------------
	global Qu
	QuantSubscribe { RB.EgoVehicle.WheelAngleLeft RB.EgoVehicle.WheelAngleRight } 
	set OverAllResult "pass"
	set WheelAngleLeft $Qu(RB.EgoVehicle.WheelAngleLeft)
	set maxValLeft 3
	if {[expr [abs $WheelAngleLeft]] <= $maxValLeft} {
		set TestStepStatus "pass"
	} else {
		set TestStepStatus "fail"
		set OverAllResult "fail"
	}
	set LogString [format "Check value condition: angle of wheel front left = %f; expected &lt;= %f°" $WheelAngleLeft $maxValLeft]
	Log "%s: %s" $LogString $TestStepStatus
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $TestStepStatus $LogString]
	puts $ReportFileId $data
	
	set WheelAngleRight $Qu(RB.EgoVehicle.WheelAngleRight)
	set maxValRight 3
	if {[expr [abs $WheelAngleRight]] <= $maxValRight} {
	} else {
		set TestStepStatus "fail"
		set OverAllResult "fail"
	}
	set LogString [format "Check value condition: angle of wheel front right = %f°; expected &lt;= %f°" $WheelAngleRight $maxValRight]
	Log "%s: %s" $LogString $TestStepStatus
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $TestStepStatus $LogString]
	puts $ReportFileId $data
	
	return $OverAllResult
}

proc CheckDistanceFrontRear { ReportFileId } {
	#--------------------------------------------------------------------------------------------------------------------
	# check the distance to the car in front and in the rear of us
	#--------------------------------------------------------------------------------------------------------------------
	global Qu
	QuantSubscribe {RB.EgoVehicle.DistFront RB.EgoVehicle.DistRear}
	set OverAllResult "pass"
	set DistFront [expr [abs $Qu(RB.EgoVehicle.DistFront)]]
	set minValFront 0.38
	set minValRear 0.38
	if {$DistFront > $minValFront} {
		set TestStepStatus "pass"
	} else {
		set TestStepStatus "fail"
		set OverAllResult "fail"
	}
	set LogString [format "Check value condition: distance front = %fm; expected &gt; %fm" $DistFront $minValFront]
	Log "%s: %s" $LogString $TestStepStatus
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $TestStepStatus $LogString]
	puts $ReportFileId $data
	
	set DistRear [expr [abs $Qu(RB.EgoVehicle.DistRear)]]
	set minVal 0.35
	if {$DistRear > $minValRear} {
		set TestStepStatus "pass"
	} else {
		set TestStepStatus "fail"
		set OverAllResult "fail"
	}
	set LogString [format "Check value condition: distance rear = %f; expected &gt; %fm" $DistRear $minValRear]
	Log "%s: %s" $LogString $TestStepStatus
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $TestStepStatus $LogString]
	puts $ReportFileId $data
	
	return $OverAllResult
}




proc StartLoadedTestRunWithReport { ReportFileId testRunName variationString CANapeMeasurement MdfFolder MeasurementSubFolder} {
	global Qu
	# TestRun parameters
	#set TestRunName "Testrun_automatic"
	set TestRunVariation $variationString
	set TestRunName $testRunName
	#set ReportFileId $reportFileFid
	set dateTime [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]
	#--------------------------------------------------------------------------------------------------------------
	# read maneuver info before starting the TestRun cause it takes quite a while
	#--------------------------------------------------------------------------------------------------------------
	set tempString "DrivMan.nDMan"
	set DMnDMan [IFileRead TestRun $tempString -1]
	array unset DrivMan*
	for {set k 0} {$k < $DMnDMan} {incr k 1} {
		set tempString [format "DrivMan.%d.Info" $k]
		set DrivMan($k,Info) [IFileRead TestRun $tempString "(no description)"]
		set tempString [format "DrivMan.%d.Label" $k]
		set DrivMan($k,Label) [IFileRead TestRun $tempString "(no label)"]
		set tempString [format "DrivMan.%d.Label" $k]
		set DrivMan($k,Label) [IFileRead TestRun $tempString "(no label)"]
		set tempString [format "DrivMan.%d.TimeLimit" $k]
		set DrivMan($k,TimeLimit) [IFileRead TestRun $tempString -1]
		set tempString [format "DrivMan.%d.DistLimit" $k]
		set DrivMan($k,DistLimit) [IFileRead TestRun $tempString -1]
		set tempString [format "DrivMan.%d.EndCondition" $k]
		set DrivMan($k,EndCondition) [IFileRead TestRun $tempString -1]
		set tempString [format "DrivMan.%d.Cmds" $k]
		set DrivMan($k,Cmds) [IFileRead TestRun $tempString ""]
	}
	#--------------------------------------------------------------------------------------------------------------------
	# start CANape measurement
	#--------------------------------------------------------------------------------------------------------------------
	
	if { $CANapeMeasurement != 0 } {
		Log "Starting CANape measurement..."
		if { [catch [$CANapeMeasurement Start]] } {
			Log "Error while starting CANape Measurement"
		} else {
			Log "Successfully started CANape Measurement"
		}
	}
	#--------------------------------------------------------------------------------------------------------------------
	# store erg-file when test failed
	#--------------------------------------------------------------------------------------------------------------------
	SaveMode save
	#SaveStart
	#--------------------------------------------------------------------------------------------------------------
	# start the TestRun
	#--------------------------------------------------------------------------------------------------------------
	QuantSubscribe {}
	StartSim
	#--------------------------------------------------------------------------------------------------------------
	# XML: open TestRun
	#--------------------------------------------------------------------------------------------------------------
	set data [format "\t\t<testgroup starttime='%s' timestamp='0.000000'>" [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]]
	puts $ReportFileId $data
	puts $ReportFileId [format "\t\t\t<title>%s - %s</title>" $TestRunName $TestRunVariation]
	if { $CANapeMeasurement != 0 } {
		Log "Reading back CANape MDF-FileName..."
		if { [catch [set MdfNameAbs [$CANapeMeasurement -get MDFFilename]]] } {
			Log "CANape MDF-FileName: %s" $MdfNameAbs
		} else {
			Log "Successfully read back CANape MDF-FileName: %s" $MdfNameAbs
		}
		set MdfNameRel2Prj [string range $MdfNameAbs [string first "SimOutput" $MdfNameAbs] end]
		set data [format "\t\t\t<externalref type='url' title='%s'>%s</externalref>" "CANape Measurement File MDF" $MdfNameRel2Prj]
		puts $ReportFileId $data
	}
	set data [format "\t\t\t<testcase starttime='%s' timestamp='0.000000'>" [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]]
	puts $ReportFileId $data
	puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
	set ManeuverMonitoring 1
	set ManeuverMonitoringStatus 0
	
	set OverallTestRunStatus 0
	set warn 0
	QuantSubscribe { Time DM.ManNo DM.ManStartTime DM.ManStartDist}
	WaitForStatus running
	Log [format "TestRun %s - %s started." $TestRunName $TestRunVariation]
	set lastManNo -1
	set lastManDist -1
	set lastManTime -1
	set lastManStartTime 0
	set lastManStartDist 0
	set TestFeature 0 
	#--------------------------------------------------------------------------------------------------------------
	# react on maneuver changes
	#--------------------------------------------------------------------------------------------------------------
	while {[SimStatus] >= 0} {
		if {$lastManNo != $Qu(DM.ManNo) && $Qu(DM.ManNo) >= 0} {
			set CurrentManNo $Qu(DM.ManNo)
			if {$lastManNo != -1} {
				if {$ManeuverMonitoring != 1} {
					puts $ReportFileId [format "\t\t\t<testcase starttime='%s' timestamp='%s'>" $dateTime $Qu(Time)]
					puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
					set ManeuverMonitoring 1
				}
				#--------------------------------------------------------------------------------------------------------------
				# determine reason for maneuver change
				#--------------------------------------------------------------------------------------------------------------
				QuantSubscribe { DM.ManStartTime DM.ManStartDist }
				set ManEndDueToDistOrTime 0
				#--------------------------------------------------------------------------------------------------------------
				# TimeLimit reached?
				#--------------------------------------------------------------------------------------------------------------
				if {$DrivMan($lastManNo,TimeLimit) != -1 } {	
					if {$Qu(DM.ManStartTime) >= [expr $DrivMan($lastManNo,TimeLimit) + $lastManStartTime]} {
						Log "ManChange due to Time"
						incr ManEndDueToDistOrTime
						set data [format "\t\t\t<teststep timestamp='%f' result='na'>ManChange due to TimeLimit: %.2fs</teststep>" $Qu(Time) $DrivMan($lastManNo,TimeLimit)]
						puts $ReportFileId $data
					}
				}
				#--------------------------------------------------------------------------------------------------------------
				# DistanceLimit reached?
				#--------------------------------------------------------------------------------------------------------------
				if {$DrivMan($lastManNo,DistLimit)!= -1 } {
					if { [expr $Qu(DM.ManStartDist)] >= [expr $DrivMan($lastManNo,DistLimit) + $lastManStartDist]} {
						Log "ManChange due to Distance"
						incr ManEndDueToDistOrTime
						set data [format "\t\t\t<teststep timestamp='%f' result='na'>ManChange due to DistanceLimit: %.2fm</teststep>" $Qu(Time) $DrivMan($lastManNo,DistLimit)]
						puts $ReportFileId $data
					}
				}
				#--------------------------------------------------------------------------------------------------------------
				# assume Condition met when not Time- and not DistanceLimit
				#--------------------------------------------------------------------------------------------------------------
				if {$DrivMan($lastManNo,EndCondition) != -1 && $ManEndDueToDistOrTime == 0} {
					Log "ManChange due to End Condition"
					#regsub -all {[>]} "&gt;" $ManEndCondition
					#regsub -all {[<]} "&lt;" $ManEndCondition
					set ManEndCondition [string map {"<" "&lt;" ">" "&gt;" "&" "&amp;"} $DrivMan($lastManNo,EndCondition)]
					set data [format "\t\t\t<teststep timestamp='%f' result='na'>ManChange due to End Condition: %s</teststep>" $Qu(Time) $ManEndCondition]
					puts $ReportFileId $data
				}
				#--------------------------------------------------------------------------------------------------------------
				# warn when not Time- and not DistanceLimit and no Condition is specified
				#--------------------------------------------------------------------------------------------------------------
				#if {$DrivMan($lastManNo,EndCondition) == -1 && $ManEndDueToDistOrTime == 0} {
					#set LogString "Unknown reason for maneuver change."
					#if {$DrivMan($lastManNo,TimeLimit) != -1 } {	
					#	set LogString [format "%s Time = %f; TimeLimit = %f" $LogString [expr $Qu(DM.ManStartTime) - $lastManStartTime] $DrivMan($lastManNo,TimeLimit)]
					#}
					#if {$DrivMan($lastManNo,DistLimit) != -1 } {	
					#	set LogString [format "%s Dist = %f; DistLimit = %f" $LogString [expr $Qu(DM.ManStartDist) - $lastManStartDist] $DrivMan($lastManNo,DistLimit)]
					#}
					#Log $LogString 
					#set data [format "\t\t\t<teststep timestamp='%f' result='warn'>%s</teststep>" $Qu(Time) $LogString]
					#puts $ReportFileId $data
				#}
				set lastManStartTime $Qu(DM.ManStartTime)
				set lastManStartDist $Qu(DM.ManStartDist)
			}
			#--------------------------------------------------------------------------------------------------------------
			# print maneuver info
			#--------------------------------------------------------------------------------------------------------------
			set LogString [format "ManNo %d: %s - %s" $Qu(DM.ManNo) $DrivMan($CurrentManNo,Label) $DrivMan($CurrentManNo,Info)]
			Log $LogString
			set data [format "\t\t\t<teststep timestamp='%f' result='na'>%s</teststep>" $Qu(Time) $LogString]
			puts $ReportFileId $data
			set lastManNo $CurrentManNo
			#--------------------------------------------------------------------------------------------------
			# parse TestEvents Keyword
			#--------------------------------------------------------------------------------------------------
			set ValidCmdNames [list "\{#TestEvent\}" "\{#TE\}" "\{#TEA\}" "\{#TestFeature\}" "\{#TF\}" "\{#TFA\}"]
			set FeatureCmds [list "\{#TestFeature\}" "\{#TF\}" "\{#TFA\}"]
			set AbortCmds [list "\{#TEA\}" "\{#TFA\}"]
			set TestFeature 0
			set numWordsInCmd [llength $DrivMan($CurrentManNo,Cmds)]
			for {set k 0} {$k < $numWordsInCmd} {incr k 1} {
				#get current commands of current mini maneuver
				set CmdName [lrange $DrivMan($CurrentManNo,Cmds) $k $k]
				# check if there is a Bosch test manager command within (e.g. #TestEvent)
				if { [lsearch -exact $ValidCmdNames $CmdName] >= 0 } {
					#is test event mapped to a feature? (e.g. #TestFeature)
					if {[lsearch -exact $FeatureCmds $CmdName] >= 0} {
						if {$ManeuverMonitoringStatus == 0} {
								set ManeuverMonitoringStatusString "pass"
						} else {
							if {$ManeuverMonitoringStatus < 0} {
								set ManeuverMonitoringStatusString "fail"
							} else {
								set ManeuverMonitoringStatusString "warn"
							}
						}
						if {$ManeuverMonitoring == 1} {
							#close testcase "maneuver monitoring" when necessary	
							puts $ReportFileId [format "\t\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s'/>" $dateTime $Qu(Time) $dateTime $Qu(Time) $ManeuverMonitoringStatusString]
							puts $ReportFileId "\t\t\t</testcase>"
							set ManeuverMonitoring 0
						}
						puts $ReportFileId [format "\t\t\t<testcase starttime='%s' timestamp='%s'>" $dateTime $Qu(Time)]
						set TestFeature 1
					} else {
						# no testcases/testfeature-commands in this maneuver
						if {$ManeuverMonitoring == 0} {
							set ManeuverMonitoringStatus 0
							puts $ReportFileId "\t\t\t<testcase>"
							puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
							set ManeuverMonitoring 1
						}
						set TestFeature 0
					}
					# shall CarMakerRun be aborted on test-failure?
					if {[lsearch -exact $AbortCmds $CmdName] >= 0} {
						set TestAbort 1
					} else {
						set TestAbort 0
					}
					set KeyWordIdx [expr $k + 1]
					set ArgIdx [expr $KeyWordIdx + 1]
					set KeyWord [lrange $DrivMan($CurrentManNo,Cmds) $KeyWordIdx $KeyWordIdx]
					switch $KeyWord {
						"QL" { set KeyWord "QuantLog" }
						"QCE" { set KeyWord "QuantCheckEqual" }
						"QCNE" { set KeyWord "QuantCheckNotEqual" }
						"QCEQ" { set KeyWord "QuantCheckEqualQuant" }
						"QCNEQ" { set KeyWord "QuantCheckNotEqualQuant" }
						"QCLT" { set KeyWord "QuantCheckLessThan" }
						"QCGT" { set KeyWord "QuantCheckGreaterThan" }	
						"QCR" { set KeyWord "QuantCheckRange" }			
						"QCRW" { set KeyWord "QuantCheckRangeWithWarning" }						
					}
					set KeyWordValid 1
					switch $KeyWord {
						"TargetPos" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Evaluate Target Position
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [EvalTargetPos $ReportFileId]
							incr ArgIdx -1
						}
						
						"TargetPos_cPSC" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Evaluate Target Position
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [EvalTargetPos_cPSC $ReportFileId]
							incr ArgIdx -1
						}
						
						"Scene" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Evaluate Target Position
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [CheckDesiredSceneAttributes $ReportFileId]
							incr ArgIdx -1
						}
						
						"FinishingStraightMove" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent FinishingStraightMove
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [CheckDistanceFrontRear $ReportFileId]
							incr ArgIdx -1
						}
						
						"FrontWheelAngle" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent FinishingStraightMove
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [CheckFrontWheelAngle $ReportFileId]
							incr ArgIdx -1
						}
					
						"Comment" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Comment
							#--------------------------------------------------------------------------------------------------
							set LogString [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
						}
						
						"QuantLog" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent IO_CAN.signal
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							# dummy start
							# no functional background, we just need to have some dummy operations before we access the subscribed quantity
							incr ArgIdx
							set dummy_joc $ArgIdx
							# dummy end
							set LogString [format "Log value condition: %s = %f" $QuantName $Qu($QuantName)]
							Log $LogString
							set KeyWordResult "na"
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckEqual" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) == $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; expected = %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckNotEqual" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) != $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; not allowed = %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckLessThan" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) < $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; must be less than %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckGreaterThan" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) > $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; must be greater than %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckRange" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceMinValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMaxValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) >= $ReferenceMinValue && $Qu($QuantName) <= $ReferenceMaxValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; must be in range %f &lt; x &lt; %f " $QuantName $Qu($QuantName) $ReferenceMinValue $ReferenceMaxValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckRangeWithWarning" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceMinValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMinValueWarn [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMaxValueWarn [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMaxValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) >= $ReferenceMinValue && $Qu($QuantName) < $ReferenceMinValueWarn} {
								set KeyWordResult "warn"
								incr warn
							} elseif {$Qu($QuantName) > $ReferenceMaxValueWarn && $Qu($QuantName) <= $ReferenceMaxValue} {
								set KeyWordResult "warn"
								incr warn
							} elseif {$Qu($QuantName) >= $ReferenceMinValueWarn && $Qu($QuantName) <= $ReferenceMaxValueWarn} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							if {$KeyWordResult == "fail"} {
								set LogString [format "Check value condition: %s = %f; must be in range %f &lt; x &lt; %f " $QuantName $Qu($QuantName) $ReferenceMinValue $ReferenceMaxValue]
							} else {
								set LogString [format "Check value condition: %s = %f; should be in range %f &lt; x &lt; %f " $QuantName $Qu($QuantName) $ReferenceMinValueWarn $ReferenceMaxValueWarn]
							}
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckEqualQuant" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity vs. Quantity
							#--------------------------------------------------------------------------------------------------
							set Quant1Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Quant2Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe { $Quant1Name $Quant2Name }
							if {$Qu($Quant1Name) == $Qu($Quant2Name)} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; expected = %f" $Quant1Name $Qu($Quant1Name) $Qu($Quant2Name)]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}

						
						"QuantCheckNotEqualQuant" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity vs. Quantity
							#--------------------------------------------------------------------------------------------------
							set Quant1Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Quant2Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe { $Quant1Name $Quant2Name }
							if {$Qu($Quant1Name) != $Qu($Quant2Name)} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; not allowed = %f" $Quant1Name $Qu($Quant1Name) $Qu($Quant2Name)]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"Blink" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Blink 
							#--------------------------------------------------------------------------------------------------
							set BlinkString [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {[string compare $BlinkString "Left"] == 0} {
								DVAWrite RB.indicator.en.r 0 1
								DVAWrite RB.indicator.en.l 1 1
							}
							if {[string compare $BlinkString "Right"] == 0} {
								DVAWrite RB.indicator.en.l 0 1
								DVAWrite RB.indicator.en.r 1 1
							}
							if {[string compare $BlinkString "Both"] == 0} {
								DVAWrite RB.indicator.en.l 1 1
								DVAWrite RB.indicator.en.r 1 1
							}
							if {[string compare $BlinkString "Off"] == 0} {
								DVAWrite RB.indicator.en.l 0 1
								DVAWrite RB.indicator.en.r 0 1
							}
							set LogString [format "Set Blinker: %s" $BlinkString]
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
						}
						
						"PushParkButton" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Push Park-Button 
							#--------------------------------------------------------------------------------------------------
							set PushTime [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							DVAWrite RB.ParkButton.ActualState 1 1
							DVAWrite RB.ParkButton.EndState 0 1
							DVAWrite RB.ParkButton.EndTimeMs $PushTime 1
							set LogString [format "Push ParkButton, hold for %d ms" $PushTime]
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
							incr ArgIdx -1
						}
						
						"ActivatePsxViaHmiMenu" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Activate PSX via HMI
							#--------------------------------------------------------------------------------------------------
							set PushTime [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							DVAWrite RB.ParkCmds.ActivatePsxViaHmiMenu 1 1
							set LogString "Activate PSX via HMI menu."
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
						}
						
						"DoorsLink" {
							set LinkTitle [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Link [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							set Link [string map {"&" "&amp;amp;"} $Link]
							Log "DoorsLink %s %s" $LinkTitle $Link
							set data [format "\t\t\t\t<externalref type='doors' title='%s'>%s</externalref>" $LinkTitle $Link]
							puts $ReportFileId $data
							set KeyWordResult "na"
							incr ArgIdx -1
						}
						
						"ALMLink" {
							set LinkTitle [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Link [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							set Link [string map {"&" "&amp;"} $Link]
							Log "ALMLink %s %s" $LinkTitle $Link
							set data [format "\t\t\t\t<externalref type='alm' title='%s'>%s</externalref>" $LinkTitle $Link]
							puts $ReportFileId $data
							set KeyWordResult "na"
							incr ArgIdx -1
						}
						
						"TriggerCANape" {
							Log "Triggering new CANape measurement..."
							if { $CANapeMeasurement != 0 } {
								$CANapeMeasurement Stop								
								set DateTime [clock format [clock seconds] -format {%Y-%m-%d_%H-%M-%S}]
								set MdfName [format "%s/%s/%s_%s.mdf" $MdfFolder $MeasurementSubFolder $TestRunName $DateTime]
								$CANapeMeasurement -set MDFFilename $MdfName
								$CANapeMeasurement Start								
							}
						}
						
						default {
							Log "Unknown TestEvent keyword: %s" [lrange $DrivMan($CurrentManNo,Cmds) $KeyWordIdx $KeyWordIdx] 
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordValid 0
						}
					}
					#--------------------------------------------------------------------------------------------------
					# was test event mapped to a feature?
					#--------------------------------------------------------------------------------------------------
					if {$TestFeature == 1} {
						#TestFeature
						if {$KeyWordValid == 1} {
							puts $ReportFileId [format "\t\t\t\t<title>%s %s</title>" "Test feature" $KeyWord]
							puts $ReportFileId [format "\t\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s'/>" $dateTime $Qu(Time) $dateTime $Qu(Time) $KeyWordResult]
							incr ArgIdx
							set DoorsLink [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							set DoorsLink [string map {"&" "&amp;amp;"} $DoorsLink]
							Log "%s" $DoorsLink
							
							if {[string first "doors" $DoorsLink] == 0} {
								puts $ReportFileId [format "\t\t\t\t<externalref type='doors' title='%s'>%s</externalref>" $KeyWord $DoorsLink]
								
							} elseif {[string first "https" $DoorsLink] == 0} {
								puts $ReportFileId [format "\t\t\t\t<externalref type='url' title='%s'>%s</externalref>" $KeyWord $DoorsLink]
							}
						} else {
							puts $ReportFileId [format "\t\t\t\t<title>Unknown KeyWord: %s %s</title>" $CmdName $KeyWord]
							puts $ReportFileId "\t\t\t\t<verdict result='na'/>"
						}
						puts $ReportFileId "\t\t\t</testcase>"
					} else {
						#TestEvent
						if {[string compare $KeyWordResult "fail"] == 0} {
							incr ManeuverMonitoringStatus -1
						}
					}
					if {[string compare $KeyWordResult "fail"] == 0} {
						incr OverallTestRunStatus -1
					} else {
						if { [string compare $KeyWordResult "warn"] == 0 } {
							incr warn
						}
					}
					if {$TestAbort == 1 && [string compare $KeyWordResult "fail"]==0} {
						StopSim
					}
				}
			}
		}
		sleep 1
	}
	if {$ManeuverMonitoring == 0} {
		# If a testfeature was processed in the last iteration
		# there is already a close tag for the test case		
		if {$TestFeature != 1} {
			puts $ReportFileId "\t\t\t</testcase>"
		}
		puts $ReportFileId "\t\t\t<testcase>"
		puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
		set ManeuverMonitoring 1
		set ManeuverMonitoringStatus 0
	}
	#--------------------------------------------------------------------------------------------------------------
	# determine reason for TestRunEnd
	#--------------------------------------------------------------------------------------------------------------
	set testStepStatus "pass"
	set tempString [format "DrivMan.%d.Label" $lastManNo]
	set lastManLabel [IFileRead TestRun $tempString "(no label)"]
	if {[string compare $lastManLabel "EndPP"] != 0} {
		set testStepStatus "fail"
		incr ManeuverMonitoringStatus -1
		incr OverallTestRunStatus -1
	}
	set LogString [format "TestRun ended in maneuver %d with label &apos;%s&apos;; expected label = &apos;EndPP&apos;" $lastManNo $lastManLabel]
	Log [format "%s : %s" $LogString $testStepStatus]
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>%s</teststep>" $Qu(Time) $testStepStatus $LogString]
	puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------------
	# check if collision happened
	#--------------------------------------------------------------------------------------------------------------------
	#QuantSubscribe RB.TargetPos.Collisions
	#set testStepStatus "pass"
	#if {$Qu(RB.TargetPos.Collisions) > 0} {
	#	set testStepStatus "fail"
	#	incr ManeuverMonitoringStatus -1
	#	incr OverallTestRunStatus -1
	#}
	#set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>Check value condition: detected collisions = %d; expected = 0</teststep>" $Qu(Time) $testStepStatus $Qu(RB.TargetPos.Collisions)]
	#puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------------
	# stop CANape measurement
	#--------------------------------------------------------------------------------------------------------------------
	if { $CANapeMeasurement != 0 } {
		Log "Stopping CANape measurement..."
		if { [catch [$CANapeMeasurement Stop]] } {
			Log "Error while stopping the CANape measurement"
		} else {
			Log "Successfully stopped the CANape measurement"
		}
	}
	#--------------------------------------------------------------------------------------------------------------------
	# manage erg-file (e.g. delete erg-file when test was successful, otherwise put link into report-file)
	#--------------------------------------------------------------------------------------------------------------------
	#SaveStop
	#if {$OverallTestRunStatus == 0} {
	#	file delete [GetLastResultFName]
	#	file delete [format "%s.info" [GetLastResultFName]]
	#} else {
	#	set data [format "\t\t\t\t<externalref type='url' title='Erg-file to re-render video'>%s</externalref>" [GetLastResultFName]]
	#	puts $ReportFileId $data
	#}
	#--------------------------------------------------------------------------------------------------------------------
	# check if an error with the test setup occurred
	#--------------------------------------------------------------------------------------------------------------------
	#QuantSubscribe RB.TargetPos.TestError
	#set testStepStatus "pass"
	#if {$Qu(RB.TargetPos.TestError) > 0} {
	#	set testStepStatus "fail"
	#	incr ManeuverMonitoringStatus -1
	#	incr OverallTestRunStatus -1
	#}
	#set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>Check value condition: errors with test-setup = %d; expected = 0</teststep>" $Qu(Time) $testStepStatus $Qu(RB.TargetPos.TestError)]
	puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------
	# XML: write result and end testcase
	#--------------------------------------------------------------------------------------------------------------
	set dateTime [clock format [clock seconds] -format {%Y-%m-%d %H:%M.%S}]
	if {$OverallTestRunStatus == 0 && $warn > 0} {
		set OverallTestRunStatus 1
		set OverallTestRunStatusString "warn"
	}
	if {$OverallTestRunStatus == 0 && $warn == 0} {
		set OverallTestRunStatusString "pass"
	}
	if {$OverallTestRunStatus < 0} {
			set OverallTestRunStatusString "fail"
	}
	
	if {$ManeuverMonitoringStatus >= 0} {
		if {$warn > 0} {
			set ManeuverMonitoringStatusString "warn"
		} else {
			set ManeuverMonitoringStatusString "pass"
		}
	} else {
		set ManeuverMonitoringStatusString "fail"
	}
	
	
	if {$ManeuverMonitoring == 1} {
		set data [format "\t\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $ManeuverMonitoringStatusString]
		puts $ReportFileId $data
		puts $ReportFileId "\t\t</testcase>"
	}
	
	#set data [format "\t\t\t\t<externalref type='url' title='Erg-file to re-render video'>%s</externalref>" [GetLastResultFName]]
	puts $ReportFileId $data
	
	puts $ReportFileId "\t\t</testgroup>"
	set LogString  [format "TestRun %s entered postprocessing." $TestRunName]
	Log $LogString
	flush $ReportFileId
	WaitForStatus idle
	set LogString  [format "TestRun %s ended." $TestRunName]
	Log $LogString
	#
	# END TestRun-specific
	#
	return $OverallTestRunStatus
}

#=================================================================================================
# Revision history
# $Log: rb_reportFunctions.tcl  $
# Revision 1.5 2016/09/06 09:20:44CEST Staack Jochen (PS-PE/ESW1) (sj82rt) 
# added a line in the scriptso that the puts command is not working as blocking for the file it writes to anymore
# Revision 1.4 2015/11/03 12:33:03MEZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# removed targetpos.testerror evaluation (outdated)
# Revision 1.3 2015/10/06 09:39:38MESZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# support of TF as test event to enable direct links between checks and RQM testcases
# Revision 1.2 2015/09/22 10:59:13MESZ FIXED-TERM Sarikurt Batikaan (CC-DA/EAU3) (SAB7LR) 
# added new keyword ALMLink
# Revision 1.1 2015/01/12 08:35:07MEZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# Initial revision
# Member added to project d:/MKS_Data/Projects/TST/PP_GEN6/PF_SIP6/Tools/CarMaker/PP6_Common/script/script.pj
# Revision 1.6 2014/08/15 08:34:32MESZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# update for TestEvent Quantlog for stability with ScriptControl
# Revision 1.5 2014/01/24 15:22:20MEZ Staack Jochen (AE-BE/EPM4) (sj82rt) 
# added input variable MDFStorageDir
# 
# Testmanager script has to take care about this by executing the test manager like this
# # Executing the test manager
# ExecuteTestManager $UseCANape $CANapeProjectDir $TestRunArray $UserName $MDFStorageDir
# 
# also at the top of the TestManager (below path to canape) you have to define the storage path like this:
#  # Path to CANape measurement storage folder
#  set MDFStorageDir "D:/CANapeMeasurement"
# Revision 1.4 2013/07/30 17:08:38MESZ Stuehmer Stephan (AE-BE/EPM1) (STT1LR) 
# - changed "error" message
# Revision 1.2 2013/07/30 15:44:12MESZ Stuehmer Stephan (AE-BE/EPM1) (STT1LR) 
# - added error handling for CANape communication
# Revision 1.1 2013/05/21 11:09:22MESZ Stuehmer Stephan (AE-BE/EPM1) (STT1LR) 
# Initial revision
# Member added to project d:/MKS_Data/Projects/Tools/PP-Tools/pp_CarMaker/common/script/common_script.pj
# Revision 1.11 2013/02/26 08:29:01MEZ Staack Jochen (AE-BE/EPM4) (sj82rt) 
# fixed bug with warnings handling
# Revision 1.10 2013/02/25 15:21:17MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# added evaluation of warnings in testreport
# Revision 1.9 2013/01/31 15:11:20MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# fixed a bug with a doors link reference
# Revision 1.8 2013/01/09 11:27:49MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# - added test event QCRW "QuantityCheckRangeWarning"
# syntax #TE QCRW <fail_low> <warn_low> <warn_high> <fail_high>
# Revision 1.7 2012/11/08 13:17:05MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# - added file counter in filename for mdf files 
# - removed automatic collision check
# Revision 1.6 2012/11/05 14:51:14MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# -removed DVAWrite RB.TargetPos.EnableTesting 1 1 (has to be set in project specific TestManager.tcl)
# Revision 1.4 2012/06/05 18:13:31MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# - Support for CANape improved
# Revision 1.3 2012/05/23 16:00:12MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# - Support for triggering CANape measurement in maneuver control: TE TriggerCANape
# Revision 1.2 2012/04/27 11:07:26MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# - BugFix: TestFeature verdict for "Scene" is set correctly if TestStep results contain "warn" and "fail"
# Revision 1.1 2012/04/27 10:40:14MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# Initial revision
# Member added to project d:/MKS_Data/Projects/Tools/internal-tools/it_EPS5-AuxiliaryTools/PP_HIL/HIL_and_ValueHIL/GM/K2xx/Data/TestRun/Script/testrun_script.pj
#======================================== end of file ============================================