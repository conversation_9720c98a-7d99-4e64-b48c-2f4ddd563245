from abc import abstractmethod
import os
import hashlib
import datetime
import traceback
import time

from pathlib import Path
from collections import OrderedDict
from enum import Enum

from src.config import conf
from src.orm.task_dao import TaskDAO
from src.utils.logger import log
from src.utils.file_helper import FileHelper, parse_testcase_maneuvers
from src.utils.ota_utils import OTAExecutor
from src.utils.ssh_helper import SSHClient
from src.utils.mtcd_service import MtcdService
from src.client.J6E_client import J6EClient
from src.client.doip_client import UDSClient
from src.client.carmaker_client import CarMakerClient
from src.client.programme_power_client import RemoteControlPower

FUNC_CATEGORY = {
    "driving":["ALC","ACC","TJA","AEX","RCTA","FCTA","HNOA","URBAN","ELK","LDW","RDP","HMA","TCA","TSR","EST"],
    "parking":["APA","MRA"],
    "hpa":["HPA"],
}

def hash_file(path, chunk_size=8192):
    hasher = hashlib.sha256()
    with open(path, 'rb') as f:
        while chunk := f.read(chunk_size):
            hasher.update(chunk)
    return hasher.hexdigest()

def compare_files_by_hash(file1, file2):
    return hash_file(file1) == hash_file(file2)

def normalize_result(result):
    """统一处理函数返回值，确保总是返回 (ret:bool, msg:str)"""
    if isinstance(result, tuple):
        if len(result) >= 2:
            return result[0], result[1]
        elif len(result) == 1:
            return result[0], ""
        else:
            return False, "Empty return tuple"
    else:
        return result, ""  # 单个 bool 值时

class StepStatus(Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    FINISHED = "FINISHED"
    FAILED = "FAILED"
    SKIPPED = "SKIPPED"

class BaseTestExecutor:
    def __init__(self, job_id):
        self.job_id = job_id
        self.current_phase = None
        self.status = StepStatus.PENDING
        self.phase_history = []
        self.fail_reason = ""
        self.result_summary = {}

        self.steps = OrderedDict()

        self._init_steps()

    def _init_steps(self):
        """由子类实现，每个步骤是可调用函数"""
        raise NotImplementedError("You must implement _init_steps in subclass")

    def run(self):
        """
        执行所有子步骤
            
        Returns:
            tuple: (success, message)
                - success: bool, 阶段是否成功完成
                - message: str, 执行结果描述
        """
        
        # 记录阶段开始时间
        start_time = datetime.datetime.now()
        
        for step_name, step_func in self.steps.items():
            log.info(f"Running Step: {step_name}")
            self.result_summary[step_name] = {"status": StepStatus.RUNNING.name}

            try:
                ret, msg = step_func()
                if ret:
                    self.result_summary[step_name] = {
                        "status": StepStatus.FINISHED.name, 
                        "message": msg
                    }
                    log.info(f"Step Completed: {step_name} - {msg}")
                else:
                    # 步骤失败，记录失败信息并立即返回
                    self.fail_reason = msg or f"{step_name}: Unknown failure"
                    self.result_summary[step_name] = {
                        "status": StepStatus.FAILED.name,
                        "message": self.fail_reason,
                    }
                    log.error(f"Step Failed: {step_name} -> {self.fail_reason}")
                    
                    # 计算阶段执行时间
                    duration = datetime.datetime.now() - start_time
                    log.error(f"{self.job_id} Failed after {duration}")
                    self.job_failure_update()
                    return False, f"{self.job_id} 在步骤 {step_name} 失败: {self.fail_reason}"
                    
            except Exception as e:
                # 异常情况，记录异常信息并立即返回
                self.fail_reason = str(e)
                self.result_summary[step_name] = {
                    "status": StepStatus.FAILED.name,
                    "message": self.fail_reason,
                }
                log.error(f"Step Exception: {step_name} -> {self.fail_reason}")
                traceback.print_exc()
                
                # 计算阶段执行时间
                duration = datetime.datetime.now() - start_time
                log.error(f"{self.job_id} Failed with exception after {duration}")
                
                self.job_failure_update()
                return False, f"{self.job_id} 在步骤 {step_name} 发生异常: {self.fail_reason}"

        # 所有步骤都成功完成
        duration = datetime.datetime.now() - start_time
        log.info(f"{self.job_id} Completed successfully in {duration}")
        return True, f"{self.job_id} 执行成功"

    def reset(self):
        """可选：失败后重新执行的机制（状态清空）"""
        self.fail_reason = ""
        self.result_summary = {}
        self.current_phase = None
        log.info("Resetting executor to initial state...")

    @abstractmethod
    def job_failure_update(self):
        pass

class HILTestBase(BaseTestExecutor):
    def __init__(self, db_cache_info):
        """
        Args:
            db_cache_info: 数据库读取的任务信息
        """
        self.conf = conf
        self.cache_data = db_cache_info
        self._ecu_client = None
        self.ota_executor = None
        self.uds_client = None
        self.carmaker_client = None
        self._hostpc_ssh_client = None
        self._mtcd_service = None

        self.need_ota_package = False
        self.carmaker_project_path = None
        self.report_path = None     #windows: xml路径
        self.func_name = None
        self.testrun_path = None
        self.testrun_name = None    # testrun名字
        self.rosbag_path = None
        self.local_xml_path = None  # linux: xml路径
        self.db_info = self._parse_db_info()

        super().__init__(db_cache_info.id)
    
    @property
    def hostpc_ssh_client(self):
        if self._hostpc_ssh_client is None:
            self._hostpc_ssh_client = SSHClient(
                host=self.conf.get_config_value("windows_pc_info")["ip"],
                username=self.conf.get_config_value("windows_pc_info")["username"],
                password=self.conf.get_config_value("windows_pc_info")["password"]
            )
        if not self._hostpc_ssh_client.is_connected():
            self._hostpc_ssh_client.connect()
        return self._hostpc_ssh_client
    
    @property
    def ecu_client(self):
        if self._ecu_client is None:
            self._ecu_client = J6EClient(
                host=self.conf.get_config_value("master_ecu_conf")["IP"],
                user=self.conf.get_config_value("master_ecu_conf")["username"],
                password=self.conf.get_config_value("master_ecu_conf")["password"]
            )
        if not self._ecu_client.is_connection_alive():
            self._ecu_client.connect()
        return self._ecu_client
        
    # @property
    # def carmaker_client(self):
    #     if self._carmaker_client is None:
    #         self._carmaker_client = CarMakerClient(
    #             machine_port_dict=self.conf.get_config_value("carmaker")["ports"]
    #         )
    #     if not self._carmaker_client.is_alive():
    #         self._carmaker_client.start_carmaker(self.hostpc_ssh_client, self.conf.get_config_value("carmaker")["task_name"])
    #     return self._carmaker_client

    @property
    def mtcd_service(self):
        if self._mtcd_service is None:
            self._mtcd_service = MtcdService()
        return self._mtcd_service
    
    def _init_carmaker_client(self):
        self.carmaker_client = CarMakerClient(machine_port_dict=self.conf.get_config_value("carmaker")["ports"])
        return True, ""

    def _init_programme_power_client(self):
        self.programme_power_client = RemoteControlPower()

    def _init_ota_executor(self):
        self.ota_executor = OTAExecutor(
            ota_package=self.db_info.get("ota_package"),
            mail_list=self.conf.get_config_value("ota_mail_list")
        )

    def _check_programme_power_status(self):
        """
        检查程控电源电流电压正常
        """
        votage = self.programme_power_client.read_voltage()
        current = self.programme_power_client.read_current()
        if votage > 10 and current > 3:
            return True, "programme power status is normal"
        else:
            return False, "programme power status is abnormal"
    
    def _check_esme_status(self):
        try:
            ret, err_msg = self.ecu_client.check_if_esme_forbidden()
            if ret:
                log.info("ESME is in forbidden mode")
                return True, "ESME is in forbidden mode"
        except Exception:
            pass
        log.info("ESME is in default mode. Closing auto-start...")
        self.ecu_client.change_ecu_start_mode()
        self.ecu_client.restart()
        return self._check_esme_status()

    def _check_carmaker_process(self):
        process_list = ["HIL.exe", "RTM.exe"]
        check_cmd = "tasklist | findstr {}"
        for process in process_list:
            stdout, stderr = self.hostpc_ssh_client.exec_command(check_cmd.format(process))

            if stdout and process in stdout:
                f"{process} is running"
                continue
            else:
                log.info(f"{process} is not running")
                return False, f"{process} is not running"
        return True, "carmaker process is running"

    
    def _check_testrun_valid(self):
        testrun_abs_path = Path(self.conf.get_config_value("testrun_folder_path") + self.db_info["testrun_path"]).resolve()
        
        # 给路径加引号，防止路径中含有空格
        check_cmd = f'if exist "{testrun_abs_path}" (echo exists) else (echo not exists)'
        
        stdout, stderr = self.hostpc_ssh_client.exec_command(check_cmd)
        output = stdout.strip().lower()

        if "exists" in output:
            return True, "testrun is valid"
        else:
            return False, f"testrun not found: {testrun_abs_path}"

    def _check_need_ota(self):
        """
        判断是否需要ota
        """
        if self.db_info.get("ota_package") is None:
            self.need_ota_package = False
            return True, "no ota package"

        ota_version = self.ota_executor.ota_asw_version()   # 类似 J6E-ASW-stable_D01_250710-202508040025CST
        ecu_version, _, _ = self.ecu_client.get_asw_bsw_mcu_version()  # 类似 "J6E-ASW-stable_D01_250710-202508050040CST-Jetour_D01_J6E-linux_arm.zip"
        if ota_version not in ecu_version: 
            self.need_ota_package = True
            log.info(f"ota version and ecu version is not consistent, need to download ota package")
            return True, "need to download ota package"
        else:
            self.need_ota_package = False
            return True, "ota version and ecu version is consistent"

    def pre_status_check(self):
        """
        做测试前检查工作：
            1、检查域控是否是 HiL 模式
            2、检查 getk 进程是否正常运行
            3、检查域控业务进程是否都正常启动
            4、停止 mtcclient
            5、重启 mtcd 服务
            6、配置 mtcclient
            7、启动 mtcclient
            8、检查 OTA 版本与域控是否一致
            9、检查 testrun 路径是否存在
            10、检查 CarMaker 进程是否存在

        Returns:
            bool: 检查是否通过
            str: 检查结果描述
        """
        check_list = [
            {
                "name":"域控HiL模式",
                "check_func":self.ecu_client.check_hil_mode, 
                "fix_func":[self.ecu_client.switch_to_hil_mode, self.ecu_client.restart]
            },
            {
                "name":"域控getk进程",
                "check_func":self.ecu_client.start_getk_process, 
                "fix_func":[]
            },
            {
                "name":"域控业务进程",
                "check_func":self.ecu_client.check_processes, 
                "fix_func":[]
            },
            {
                "name":"停止mtcclient",
                "check_func":self.mtcd_service.stop_mtcclient,
                "fix_func":[]
            },
            {
                "name":"重启mtcd服务",
                "check_func":self.mtcd_service.restart_mtcd_service,
                "fix_func":[]
            },
            {
                "name":"配置mtcclient",
                "check_func":self.mtcd_service.config_mtcclient,
                "fix_func":[]
            },
            {
                "name":"启动mtcclient",
                "check_func":self.mtcd_service.start_mtcclient,
                "fix_func":[]
            },
            {
                "name":"ota版本检查",
                "check_func":self._check_need_ota,
                "fix_func":[]
            },
            {
                "name":"testrun路径检查",
                "check_func":self._check_testrun_valid,
                "fix_func":[lambda: (False, "testrun invalid")]   # 没有testrun就测不了，直接失败
            },
            {
                "name":"CarMaker进程检查",
                "check_func":self._check_carmaker_process,
                "fix_func":[self._init_carmaker_client, self.start_carmaker]
            },
        ]

        try:
            for check_item in check_list:
                result = check_item["check_func"]()
                ret, msg = normalize_result(result)

                if not ret:
                    for fix_func in check_item["fix_func"]:
                        fix_result = fix_func()
                        ret, msg = normalize_result(fix_result)
                        if not ret:
                            return False, msg
                        
            log.info("初始环境检查通过")
            return True, "status check passed"
        except Exception as e:  
            log.error(f"status check failed: {e}")
            return False, str(e)       

    def _parse_db_info(self):
        """
        解析数据库中的测试用例信息，提取对测试流程有影响的关键参数
        
        Returns:
            dict: 包含解析后的测试配置信息，主要包含：
                - id: 测试记录ID
                - retry_count: 重试次数
                - record_type: 记录类型
                - ota_package: OTA包信息
        """
        try:
            # 从数据库记录中提取关键信息
            test_config = {
                'id': self.cache_data.id,  # 测试记录ID
                'function': self.cache_data.testcase.function,  # 功能
                'testrun_path':self.cache_data.testcase.testrun_path, # Testrun相对路径
                'retry_count': self.cache_data.retry_count,  # 重试次数
                'record_type': self.cache_data.record_type,  # 记录类型（影响测试流程）
                'ota_package': getattr(self.cache_data, 'ota_package', None),  # OTA包信息（可能不存在）
            }

            self.func_name = test_config['function']
            self.testrun_path = test_config['testrun_path']
            self.testrun_name = self.testrun_path.split("/")[-1]
            
            # 记录解析结果
            log.info(f"解析数据库信息完成: ID={test_config['id']}, "
                     f"function={self.func_name},"
                    f"testrun_path={test_config['testrun_path']}, "
                    f"类型={test_config['record_type']}, "
                    f"重试次数={test_config['retry_count']}, "
                    f"OTA包={test_config['ota_package']}")
            
            return test_config
            
        except Exception as e:
            log.error(f"解析数据库信息失败: {e}")
            raise Exception(f"数据库信息解析错误: {e}")
    
    def ota_software_update(self):
        """
        执行OTA软件更新
        
        Returns:
            tuple: (result, message)
                - result: bool, 执行是否成功
                - message: str, 执行结果描述
        """
        # 检查是否需要OTA更新
        if not self.need_ota_package:
            return True, "无需OTA更新"
        try:
            
            # 初始化OTA执行器
            if self.ota_executor is None:
                self._init_ota_executor()
            
            # 执行OTA更新
            ret, msg = self.ota_executor.run()
            if ret:
                self.need_ota_package = False  # 更新完成后重置标志
                return True, f"OTA软件更新成功: {msg}"
            else:
                return False, f"OTA软件更新失败: {msg}"
                
        except Exception as e:
            log.error(f"OTA软件更新过程中发生异常: {e}")
            return False, f"OTA软件更新过程中发生异常: {e}"

    def set_mtcd(self):
        '''
        准备mtcd服务：
        1、检查mtcd服务是否启动，没启动的话拉起服务
        2、查看白名单，根据testrun，决定是否切换白名单
        '''
        if not self.mtcd_service.check_mtcd_start_status():
            self.mtcd_service.start_mtcd_service()

        for category, func_list in FUNC_CATEGORY.items():
            if self.func_name.upper() in func_list:
                target_whitelist = f"{category}_whitelist"
                break
        
        target_whitelist_path = self.conf.get_config_value("mtcd_whitelist_config_paths")[target_whitelist]
        target_whitelist_abs_path = str(Path(__file__).resolve().parents[1] / target_whitelist_path)
        origin_whitelist_path = self.conf.get_config_value("mtcd_whitelist_config_paths")["output_path"]
        skip_set_whitelist = compare_files_by_hash(target_whitelist_abs_path, origin_whitelist_path)

        set_whiltelist_step = [
            {
                "name":"配置mtcd客户端",
                "check_func":lambda: self.mtcd_service.stop_mtcclient(), 
                "fix_func":[]
            },
            {
                "name":"设置白名单",
                "check_func":lambda: self.mtcd_service.set_user_config_to_mtcd(target_whitelist), 
                "fix_func":[]
            },
            {
                "name":"重启mtcd client",
                "check_func":lambda: self.mtcd_service.reload_mtcclient(), 
                "fix_func":[]
            },
            {
                "name":"重启mtcd服务",
                "check_func":lambda: self.mtcd_service.restart_mtcd_service(), 
                "fix_func":[]
            },
            {
                "name":"配置mtcd客户端",
                "check_func":lambda: self.mtcd_service.config_mtcclient(), 
                "fix_func":[]
            },
            {
                "name":"开启mtcd客户端",
                "check_func":lambda: self.mtcd_service.start_mtcclient(), 
                "fix_func":[]
            },
        ]

        if not skip_set_whitelist:
            for step in set_whiltelist_step:
                ret = step["check_func"]()
                if not ret:
                    for fix_func in step["fix_func"]:
                        ret, msg = fix_func()
                        if not ret:
                            return False, msg
            
        else:
            log.info(f"skip set whitelist: {target_whitelist_path}")
        
        return True, "set whitelist passed"
    
    def start_carmaker(self):
        """"
        启动carmaker hil和rtmaker进程
        """
        task_name_mapping = self.conf.get_config_value("carmaker")["task_name"]
        self.carmaker_client.start_carmaker(self.hostpc_ssh_client, task_name_mapping)

        return True, ""

    def _call_tcl_function(self, function_cmd: str):
        """
        调用tickle脚本: 先本地生成临时tickle脚本，拷贝到windows电脑，最后用tcp command执行
        Args:
            function_cmd: 要执行的tcl命令
        Returns:
            tuple: (result, message)
                - result: bool, 执行是否成功
                - message: str, 执行结果描述
        """
        local_tcl_path = Path(__file__).resolve().parent.parent / "files" / "execution.tcl"
        target_remote_tcl_folder_path = Path(self.conf.get_config_value("carmaker")["tickle_folder_path"])
        target_remote_exe_tcl_path = target_remote_tcl_folder_path / "execution.tcl"
        target_remote_lib_tcl_path = target_remote_tcl_folder_path / "rb_reportFunctionsModules.tcl"
        # 创建临时 Tcl 文件
        with open(local_tcl_path, 'w') as tcl_file:
            tcl_file.write(f"source {target_remote_lib_tcl_path}\n")
            tcl_file.write(f"{function_cmd}\n")

        # copy到windows电脑
        rst = FileHelper.copy_to_remote(
            remote_path=target_remote_tcl_folder_path,
            local_path=local_tcl_path,
            host=self.conf.get_config_value("windows_pc_info")["ip"],
            username=self.conf.get_config_value("windows_pc_info")["username"],
            password=self.conf.get_config_value("windows_pc_info")["password"]
        )

        if not rst:
            log.error(f"Failed to copy tickle file to windows pc, {function_cmd} executed failed")
            return False, f"Failed to copy tickle file to windows pc, {function_cmd} executed failed"

        # 执行tcl文件
        execute_cmd = f"RunScript {target_remote_exe_tcl_path} \r"
        self.carmaker_client.send_message("RT_Primary", execute_cmd)

        return True, ""

    # TODO 完善HNOA设置导航路径
    def _set_navigation_path(self):
        return True, ""

    def carmaker_env_setup(self):
        # 检查carmaker_client是否已初始化
        if self.carmaker_client is None:
            log.info("CarMaker client not initialized, initializing...")
            ret, msg = self._init_carmaker_client()
            if not ret:
                return False, f"Failed to initialize CarMaker client: {msg}"
        
        # 根据功能选择carmaker实时机工程
        for category, func_list in FUNC_CATEGORY.items():
            if self.func_name.upper() in func_list:
                func_type = category if category == "driving" else "parking"
        
        project_path = self.conf.get_config_value("carmaker")["project_path"][func_type]

        if self.carmaker_project_path != project_path:
            self.carmaker_client.start_all_applications(project_path=project_path, timeout=30)
            self.carmaker_project_path = project_path

        # 把tickle脚本拷贝到windows pc
        target_path = self.conf.get_config_value("carmaker")["tickle_folder_path"]
        tickle_file_path = Path(__file__).resolve().parent.parent / "files" / "rb_reportFunctionsModules.tcl"
        log.info(f"target_path:{target_path}\ntickle_file_path:{tickle_file_path}")
        folder_existed = FileHelper.exists_or_make_dir(self.hostpc_ssh_client, target_path)
        
        if folder_existed:
            FileHelper.copy_to_remote(
                remote_path=target_path,
                local_path=tickle_file_path,
                host=self.conf.get_config_value("windows_pc_info")["ip"],
                username=self.conf.get_config_value("windows_pc_info")["username"],
                password=self.conf.get_config_value("windows_pc_info")["password"]
            )
        else:
            return False, f"{target_path} doesnt exist on windows pc"

        # HNOA需要设置导航路径
        if self.func_name.upper() == "HNOA":
            rst, msg = self._set_navigation_path()
            if not rst:
                log.error(f"HNOA set navigation path failed: {msg}")
                return
        
        return True, ""

    def disconnect_hil_clients(self):
        """
        断开所有 client 连接
        """
        log.info("Disconnecting HIL clients...")
        try:
            if self.uds_client:
                self.uds_client.shutdown()
            if self.carmaker_client:
                self.carmaker_client.close()
            if self.ota_executor:
                self.ota_executor.close_ssh_client()
            if self.hostpc_ssh_client:
                self.hostpc_ssh_client.close()

            return True, "HIL clients disconnected"
        except Exception as e:
            log.warning(f"Error while disconnecting: {e}")
            return False, str(e)

    def postprocessing(self):
        """
        后处理模块（按照test_flow格式）：
        1. 检查进程 check_processes
        2. 检查coredump read_coredump
        3. 拷贝xml copy_from_remote
        4. 删除xml delete_remote_file
        5. 解析xml parse_testcase_maneuvers
        6. 上传log到网盘 move_to_cloud_storage
        """
        post_flow = [
            {
                "name": "check_processes",
                "execute_func": self._check_ecu_processes,
                "fix_func": []
            },
            {
                "name": "read_coredump",
                "execute_func": self._read_ecu_coredump,
                "fix_func": []
            },
            {
                "name": "copy_xml_from_remote",
                "execute_func": lambda: self._copy_xml_from_remote(self.report_path, self.rosbag_path),
                "fix_func": []
            },
            {
                "name": "delete_remote_xml",
                "execute_func": lambda: self._delete_remote_xml(self.report_path),
                "fix_func": []
            },
            {
                "name": "parse_testcase_maneuvers",
                "execute_func": lambda: self._parse_xml_report(self.local_xml_path),
                "fix_func": []
            },
            {
                "name": "move_to_cloud_storage",
                "execute_func": lambda: self._move_logs_to_cloud(self.rosbag_path),
                "fix_func": []
            },
        ]

        for step in post_flow:
            ret, msg = step["execute_func"]()
            if not ret:
                for fix_func in step["fix_func"]:
                    ret, msg = fix_func()
                    if not ret:
                        return False, msg
            time.sleep(1)

        return True, "postprocessing completed successfully"

    def _copy_xml_from_remote(self, report_path, local_dir):
        """1. 拷贝xml copy_from_remote"""
        try:
            log.debug(f"windows xml path: {report_path}")
            log.debug(f"linux xml dir: {local_dir}")
            local_dir = Path(local_dir)
            success = FileHelper.copy_from_remote(
                remote_path=report_path,
                local_path=str(local_dir),
                host=self.conf.get_config_value("windows_pc_info")["ip"],
                username=self.conf.get_config_value("windows_pc_info")["username"],
                password=self.conf.get_config_value("windows_pc_info")["password"],
            )

            if success:
                self.local_xml_path = local_dir / Path(report_path).name
                log.debug(f"copy xml from {report_path} to {self.local_xml_path} success")
                return True, f"XML copied to {self.local_xml_path}"
            else:
                log.debug(f"copy xml from {report_path} to {self.local_xml_path} failed")
                return False, f"Failed to copy XML from {report_path}"
        except Exception as e:
            return False, str(e)

    def _delete_remote_xml(self, report_path):
        """2. 删除xml delete_remote_file"""
        try:
            log.debug(f"delete xml from {report_path}")
            success = FileHelper.delete_remote_file(
                remote_path=report_path,
                host=self.conf.get_config_value("windows_pc_info")["ip"],
                username=self.conf.get_config_value("windows_pc_info")["username"],
                password=self.conf.get_config_value("windows_pc_info")["password"],
            )

            if success:
                log.debug(f"delete xml from {report_path} success")
                return True, f"Remote XML deleted: {report_path}"
            else:
                log.debug(f"delete xml from {report_path} failed")
                return False, f"Failed to delete remote XML: {report_path}"
        except Exception as e:
            return False, str(e)

    def _parse_xml_report(self, local_xml_path):
        """3. 解析xml parse_testcase_maneuvers"""
        try:
            log.debug(f"parse xml report: {local_xml_path}")
            result = parse_testcase_maneuvers(str(local_xml_path))
            self.result_summary["report"] = result
            log.debug(f"parse xml report success: {result}")
            return True, f"XML parsed successfully, found {result.get('total_maneuvers', 0)} maneuvers"
        except Exception as e:
            return False, str(e)

    def _check_ecu_processes(self):
        """4. 检查进程 check_processes"""
        try:
            ok, running, missing = self.ecu_client.check_processes()
            self.result_summary["ecu_process"] = {
                "ok": ok,
                "running": running,
                "missing": missing,
            }
            log.debug(f"check ecu processes missing: {self.result_summary["ecu_process"]["missing"]}")
            return True, f"Process check completed: {len(running)} running, {len(missing)} missing"
        except Exception as e:
            return False, str(e)

    def _read_ecu_coredump(self):
        """5. 检查coredump read_coredump"""
        try:
            coredump_files = self.ecu_client.read_coredump()
            self.result_summary["coredumps"] = coredump_files
            log.debug(f"read ecu coredump success: {self.result_summary["coredumps"]}")
            return True, f"Found {len(coredump_files)} coredump files"
        except Exception as e:
            return False, str(e)

    def _move_logs_to_cloud(self, rosbag_path):
        """6. 上传log到网盘 move_to_cloud_storage"""
        try:
            log.debug(f"move logs to cloud storage: {rosbag_path}")
            success = self.mtcd_service.move_to_cloud_storage(rosbag_path)
            if success:
                log.debug(f"move logs to cloud storage success: {rosbag_path}")
                return True, f"Logs moved to cloud storage: {rosbag_path}"
            else:
                log.debug(f"move logs to cloud storage failed: {rosbag_path}")
                return False, f"Failed to move logs to cloud storage: {rosbag_path}"
        except Exception as e:
            return False, str(e)

    def start_recording(self):
        """
        开始录制包装方法

        Returns:
            Tuple[bool, str]: (操作是否成功, 录制路径或错误信息)
        """
        try:
            ret, rosbag_path = self.mtcd_service.start_recording()
            if ret:
                self.rosbag_path = rosbag_path  # 保存录制路径供后续使用
                log.debug(f"start_recording success: {rosbag_path}")
                return True, rosbag_path
            else:
                log.debug(f"start_recording failed: {rosbag_path}")
                return False, rosbag_path
        except Exception as e:
            return False, str(e)

    def stop_recording(self, custom_name=None):
        """
        停止录制包装方法

        Args:
            custom_name: 可选的自定义名称

        Returns:
            Tuple[bool, str]: (操作是否成功, 最终路径或错误信息)
        """
        try:
            if custom_name and hasattr(self, 'rosbag_path') and self.rosbag_path:
                # 如果有自定义名称和录制路径，使用带重命名的停止录制
                ret, final_path = self.mtcd_service.stop_recording(self.rosbag_path, custom_name)
            else:
                # 否则使用基本的停止录制
                ret, message = self.mtcd_service.stop_recording()
                final_path = message

            if ret:
                self.rosbag_path = final_path  # 更新录制路径
                log.debug(f"stop_recording success: {final_path}")
                return True, final_path
            else:
                log.debug(f"stop_recording failed: {final_path}")
                return False, final_path
        except Exception as e:
            return False, str(e)
