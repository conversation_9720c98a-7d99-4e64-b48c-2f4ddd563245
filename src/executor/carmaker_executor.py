import os
import time
import datetime
from utils.logger import log

class CarMakerTestExecutor:
    """用于管理基于 CarMakerClient 的测试用例执行。"""

    def __init__(self, carmaker_client):
        self.cmk = carmaker_client

    def run_test_sequence(self, testrun_name: str, tester_name: str = "Tester", report_dir: str = "../TestReports") -> None:
        """执行完整的 CarMaker 测试流程，包括选择并运行一个 TestRun。

        Args:
            testrun_name: 比如 "HighwayE2E_01"
            tester_name: 测试者姓名（替代 hilSettings.testInfo.tester）
            report_dir: 日志报告目录
        """
        report_prefix = "report_" + testrun_name.split("/")[-1]
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d_%H-%M-%S')
        report_name = f"{report_prefix}_{timestamp}"
        report_path = os.path.join(report_dir, report_name)

        os.makedirs(report_dir, exist_ok=True)

        log.info("开始执行 CarMaker testrun: %s", testrun_name)

        # Step 1: 写入测试组 XML Header
        self._run_tcl_script(f'WriteXmlHeader "{report_name}" {tester_name}')

        # Step 2: 写入 TestGroup Header
        self._run_tcl_script(f'WriteNewTestGroupXML "{report_name}" "{testrun_name}"')

        # Step 3: 写入 Precondition
        self._run_tcl_script(f'WriteTestRunPreconditionReport "{report_name}" "1" "{testrun_name}"')

        # Step 4: 执行 Testrun
        self._run_tcl_script(
            f'LoadAndRunTestRun "{report_name}" "1" "1" "{testrun_name}" "1" "1"'
        )

        # Step 5: 等待运行结束
        self._wait_until_sim_done()

        # Step 6: 写入 Postcondition
        self._run_tcl_script(f'WriteTestRunPostconditionReport "{report_name}"')

        # Step 7: 关闭 TestGroup 与 Movie
        self._run_tcl_script(f'CloseTestGroupXML "{report_name}"')
        self._run_tcl_script(f'CloseTestModuleXML "{report_name}"')

        self.cmk.send_message("RT_Primary", "Movie quit\r")
        log.info("Testrun '%s' 完成，退出 Movie", testrun_name)

    def _run_tcl_script(self, command: str):
        """将 TCL 函数写入 rb_reportFunctionsModules.tcl 中定义的脚本并发送执行。"""
        script_dir = os.path.abspath(os.path.join(os.path.dirname(__file__), "../../Test_Report_Scripts"))
        tcl_path = os.path.join(script_dir, "TrScript_TEMP.tcl")
        tcl_source = f"source {script_dir.replace('\\', '/')}/rb_reportFunctionsModules.tcl"

        os.makedirs(script_dir, exist_ok=True)
        with open(tcl_path, "w", encoding="utf-8") as f:
            f.write(tcl_source + "\n")
            f.write(command)

        cmd = f"RunScript {tcl_path.replace('\\', '/')} \r"
        self.cmk.send_message("RT_Primary", cmd)
        time.sleep(1)
        self.cmk.send_message("RT_Primary", "SimStatus \r")
        response = self.cmk.get_response("RT_Primary")
        log.debug("[RT_Primary] SimStatus after script: %s", response)

    def _wait_until_sim_done(self, timeout=180):
        """等待仿真运行结束（SimStatus 不再是 -2）。"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            self.cmk.send_message("RT_Primary", "SimStatus \r")
            resp = self.cmk.get_response("RT_Primary")
            log.info("[RT_Primary] SimStatus: %s", resp)
            if b'-2' not in resp:
                return
            time.sleep(1)
        raise TimeoutError("仿真运行超时未结束。")
