import json
import time
import re
from pathlib import Path
from collections import OrderedDict

from src.executor import <PERSON><PERSON><PERSON>estBase
from src.utils.logger import log
from src.orm.task_dao import TaskDAO
from src.utils.file_helper import FileHelper, parse_testcase_maneuvers

class SysReqTestExecutor(HILTestBase):
    def __init__(self, db_cache_info):
        super().__init__(db_cache_info)
        self.dao = TaskDAO()

    def _init_steps(self):

        prepare_steps = OrderedDict([
            # ("check_environment", self.check_environment),
            ("pre_status_check", self.pre_status_check),
            ("ota_software_update", self.ota_software_update),
            ("set_mtcd", self.set_mtcd),
        ])

        test_steps = OrderedDict([
            ("carmaker_env_setup", self.carmaker_env_setup),
            ("sys_req_test", self.sys_req_test)
        ])

        summary_steps = OrderedDict([
            ("postprocessing", self.postprocess)
        ])

        for steps in [prepare_steps, test_steps, summary_steps]:
            self.steps.update(steps)

    def sys_req_test(self):
        """
        Testrun 需求测试主流程：
        1、tickle: pre condition
        2、tickle: load and run testrun
        3、tickle: post condition
        """
        target_path = self.conf.get_config_value("carmaker")["tickle_folder_path"]
        self.report_path = Path(target_path) / f"report_{self.testrun_path.split('/')[-1]}_{self.job_id}.xml"  # xml报告路径
        log.info(f"test report path: {self.report_path}")

        recording_flow = [
            {
                "name": "开启录包",
                "execute_func": self.mtcd_service.start_recording,
                "fix_func": []
            },
            {
                "name": "停止录包",
                "execute_func": self.mtcd_service.stop_recording,
                "fix_func": []
            }
        ]

        test_flow = [
            {
                "name": "create xml file",
                "execute_func": lambda: FileHelper.create_remote_file(self.hostpc_ssh_client, self.report_path),
                "fix_func": []
            },
            {
                "name": "pretest",
                "execute_func": self._call_tcl_function,
                "function_cmd": f"WriteTestRunPreconditionReport \"{self.report_path}\" \"{self.testrun_path}\"",
                "fix_func": []
            },
            {
                "name": "load_and_run_testrun",
                "execute_func": self._call_tcl_function,
                "function_cmd": f"LoadAndRunTestRun \"{self.report_path}\" \"{self.testrun_path}\"",
                "fix_func": []
            },
            {
                "name": "wait_for_sim_start",
                "execute_func": lambda: self.wait_for_sim_start(self.hostpc_ssh_client, self.report_path),
                "fix_func": []
            },
            {
                "name": "wait_for_sim_finish",
                "execute_func": self.wait_for_sim_finish,
                "fix_func": []
            },
            {
                "name": "posttest",
                "execute_func": self._call_tcl_function,
                "function_cmd": f"WriteTestRunPostconditionReport \"{self.report_path}\"",
                "fix_func": []
            },
        ]

        if self.db_info.get("record_type", "None") != "None":
            # 找到wait_for_sim_start的位置，在其后面插入开始录包
            wait_for_sim_start_index = next(i for i, item in enumerate(test_flow) if item["name"] == "wait_for_sim_start")
            test_flow.insert(wait_for_sim_start_index + 1, recording_flow[0])  # Insert start recording after wait_for_sim_start

            # 找到wait_for_sim_finish的位置，在其后面插入停止录包
            wait_for_sim_finish_index = next(i for i, item in enumerate(test_flow) if item["name"] == "wait_for_sim_finish")
            test_flow.insert(wait_for_sim_finish_index + 1, recording_flow[1])  # Insert stop recording after wait_for_sim_finish

        for step in test_flow:
            function_cmd = step.get("function_cmd")
            if function_cmd:
                step["execute_func"](function_cmd)
            else:
                if step["name"] == "开启录包":
                    ret, self.rosbag_path = step["execute_func"]()
                else:
                    ret, msg = step["execute_func"]()
                if not ret:
                    for fix_func in step["fix_func"]:
                        ret, msg = fix_func()
                        if not ret:
                            return False, msg
            time.sleep(5)
        return True, "sys_req_test passed"

    def job_failure_update(self):
        self.dao.update_task_by_id(self.job_id, status="failed", result_summary=json.dumps(self.result_summary))

    def wait_for_sim_start(self, hostpc_ssh_client=None, report_path=None):
        """
        等待仿真开始：通过解析远程XML文件simstatus标签中SimulationStatus =后的数字判断仿真状态。
        >= 0: Current maneuver number, simulation is running
        -1: Preprocessing
        -2: Idle
        -3: Postprocessing
        -4: Model Check
        -5: Driver Adaption
        -6: FATAL ERROR / Emergency Mode
        -7: Waiting for License
        -8: Simulation paused
        -10: Starting application
        -11: Simulink initialization

        Args:
            hostpc_ssh_client: SSH客户端，默认使用self.hostpc_ssh_client
            report_path: XML报告路径，默认使用self.report_path

        Returns:
            bool: 仿真是否已开始
            str: 状态描述
        """
        # 使用传入的参数或默认值
        ssh_client = hostpc_ssh_client or self.hostpc_ssh_client
        xml_path = report_path or self.report_path

        start_time = time.time()
        timeout = 180  # 3分钟超时
        sim_status_value = {'status': None}
        stop_flag = {'stop': False}

        log.info(f"等待仿真开始... 通过解析XML: {xml_path}")

        def on_element(elem, event):
            if elem.tag.lower() == "simstatus":
                text = elem.text or ""
                match = re.search(r"SimulationStatus\s*=\s*(-?\d+)", text)
                if match:
                    status = int(match.group(1))
                    log.info(f"解析到仿真状态: {status} (原始内容: {text})")
                    sim_status_value['status'] = status
                    # 只在 >=0 或 -6 时提前终止，其它状态继续等待
                    if status >= 0 or status == -6:
                        stop_flag['stop'] = True

        def stop_condition():
            # 只要检测到SimulationStatus中需要的内容就立即停止
            if stop_flag['stop']:
                return True
            if time.time() - start_time > timeout:
                log.error(f"等待仿真开始超时 ({timeout}秒)")
                return True
            return False

        # 调用FileHelper的stream_parse_remote_xml
        FileHelper.stream_parse_remote_xml(
            ssh_client=ssh_client,
            remote_path=xml_path,
            on_element=on_element,
            element_tag="simstatus",
            from_beginning=False,
            stop_condition=stop_condition,
            timeout_sec=timeout,
            encoding="utf-8",
            poll_interval_sec=0.5,
            remote_os="windows"
        )

        status = sim_status_value['status']
        if status is not None:
            if status >= 0:
                log.info(f"仿真已开始运行，当前状态: {status} (Current maneuver number)")
                return True, f"仿真已开始，状态: {status}"
    def postprocess(self):
        """
        后处理模块（按步骤样式执行）：
        1. 拷贝xml copy_from_remote
        2. 删除xml delete_remote_file
        3. 解析xml parse_testcase_maneuvers
        4. 检查进程 check_processes
        5. 检查coredump read_coredump
        6. 上传log到网盘 move_to_cloud_storage
        """
        try:
            # 1. 拷贝xml到本地
            remote_xml = str(self.report_path)
            local_dir = Path(self.conf.get_config_value("local_tmp_report_dir"))
            local_dir.mkdir(parents=True, exist_ok=True)
            local_xml = local_dir / Path(remote_xml).name
            copy_ok = FileHelper.copy_from_remote(
                remote_path=remote_xml,
                local_path=str(local_dir),
                host=self.conf.get_config_value("windows_pc_info")["ip"],
                username=self.conf.get_config_value("windows_pc_info")["username"],
                password=self.conf.get_config_value("windows_pc_info")["password"],
            )
            if not copy_ok:
                return False, f"copy xml failed: {remote_xml} -> {local_xml}"

            # 2. 删除远端xml
            del_ok = FileHelper.delete_remote_file(
                remote_path=remote_xml,
                host=self.conf.get_config_value("windows_pc_info")["ip"],
                username=self.conf.get_config_value("windows_pc_info")["username"],
                password=self.conf.get_config_value("windows_pc_info")["password"],
            )
            if not del_ok:
                log.warning(f"delete remote xml failed: {remote_xml}")

            # 3. 解析xml，提取maneuver结果
            result = parse_testcase_maneuvers(str(local_xml))
            self.result_summary["report"] = result

            # 4. 检查域控进程
            proc_ok, running, missing = self.ecu_client.check_processes()
            self.result_summary["ecu_process"] = {
                "ok": proc_ok,
                "running": running,
                "missing": missing,
            }

            # 5. 检查coredump
            coredumps = self.ecu_client.read_coredump()
            self.result_summary["coredumps"] = coredumps

            # 6. 上传log到网盘（如果有录包路径，则将录包目录移动到云盘）
            move_ok = True
            if self.rosbag_path:
                move_ok = self.mtcd_service.move_to_cloud_storage(self.rosbag_path)
            self.result_summary["move_to_cloud_storage"] = move_ok

            return True, "postprocessing finished"
        except Exception as e:
            log.error(f"postprocessing failed: {e}")
            return False, str(e)

            elif status == -1:
                log.info("仿真状态: Preprocessing")
                return False, "仿真状态: Preprocessing (-1)"
            elif status == -2:
                log.info("仿真状态: Idle")
                return False, "仿真状态: Idle (-2)"
            elif status == -3:
                log.info("仿真状态: Postprocessing")
                return False, "仿真状态: Postprocessing (-3)"
            elif status == -4:
                log.info("仿真状态: Model Check")
                return False, "仿真状态: Model Check (-4)"
            elif status == -5:
                log.info("仿真状态: Driver Adaption")
                return False, "仿真状态: Driver Adaption (-5)"
            elif status == -6:
                log.error("仿真状态: FATAL ERROR / Emergency Mode")
                return False, "仿真出现致命错误或紧急模式 (-6)"
            elif status == -7:
                log.info("仿真状态: Waiting for License")
                return False, "仿真状态: Waiting for License (-7)"
            elif status == -8:
                log.info("仿真状态: Simulation paused")
                return False, "仿真状态: Simulation paused (-8)"
            elif status == -10:
                log.info("仿真状态: Starting application")
                return False, "仿真状态: Starting application (-10)"
            elif status == -11:
                log.info("仿真状态: Simulink initialization")
                return False, "仿真状态: Simulink initialization (-11)"
            else:
                log.info(f"仿真状态: 未知状态 {status}")
                return False, f"仿真未开始，当前状态: {status}"
        else:
            return False, "未能解析到仿真状态或超时"

    def wait_for_sim_finish(self):
        """
        根据与Primary的状态查询，来等待测试运行结束
        """
        start_time = time.time()
        response = b'-2'
        while (b'-2' in response) and (time.time() - start_time < 60):
            time.sleep(1)
            self.carmaker_client.send_message ("RT_Primary", "SimStatus \r")
            response = self.carmaker_client.get_response("RT_Primary")

        log.info("maybe init gpu sensor is done, please check RT1 log for more details")

        log.info("testrun will be executed !")
        while (b'-2' not in response):
            self.carmaker_client.send_message ("RT_Primary", "SimStatus \r")
            response = self.carmaker_client.get_response("RT_Primary")
            time.sleep(1)

        log.info("After Testrun, now SimState is {}".format(str(response)))
        return True, ""


if __name__ == "__main__":
    db_cache_info = TaskDAO().get_task_by_id(1)

    # 清除环境
    from src.client.carmaker_client import CarMakerClient
    from src.utils.ssh_helper import SSHClient
    from src.config import conf

    ssh_client = SSHClient(
        conf.get_config_value("windows_pc_info")["ip"],
        conf.get_config_value("windows_pc_info")["username"],
        conf.get_config_value("windows_pc_info")["password"]
    )
    ssh_client.connect()
    CarMakerClient.kill_processes(ssh_client)
    ssh_client.disconnect()

    while True:

        executor = SysReqTestExecutor(db_cache_info)
        executor.run()