from typing import Callable, Optional
import os
import shutil
import yaml
import json
import zipfile
import tarfile
import sys
import stat
import time
import psutil
import subprocess
from src.utils.logger import log
import xml.etree.ElementTree as ET
import threading
import select
import io
import re

class FileHelper:

    @staticmethod
    def stream_parse_remote_xml(
        ssh_client,
        remote_path: str,
        on_element: Callable[[object, str], None],
        element_tag: Optional[str] = None,
        from_beginning: bool = False,
        stop_condition: Optional[Callable[[], bool]] = None,
        timeout_sec: Optional[int] = None,
        encoding: str = "utf-8",
        poll_interval_sec: float = 0.2,
        remote_os: str = "auto",
    ) -> bool:
        """
        统一的远端 XML 实时解析入口（Windows/Linux）。
        - 当 remote_os='windows' 时，调用 ssh_client.stream_parse_remote_xml_windows
        - 当 remote_os='linux' 时，调用 ssh_client.stream_parse_remote_xml
        - 当 remote_os='auto' 时，依据路径特征或远端命令自动判断
        
        Args:
            ssh_client: 已连接的 SSHClient 实例（src.utils.ssh_helper.SSHClient）
            remote_path: 远端 XML 文件路径
            on_element: 解析到完整元素时的回调
            element_tag: 仅回调指定标签；None 表示所有
            from_beginning: True 从头解析；False 跟随新增
            stop_condition: 返回 True 则停止
            timeout_sec: 总超时；None 不限
            encoding: 文本编码
            poll_interval_sec: 轮询间隔
            remote_os: 'windows' | 'linux' | 'auto'
        """
        def _is_windows_path(path: str) -> bool:
            # 简单依据盘符或反斜杠判断
            if len(path) >= 2 and path[1] == ':' and path[0].isalpha():
                return True
            if '\\' in path:
                return True
            return False

        os_choice = remote_os.lower().strip()
        if os_choice not in {"windows", "linux", "auto"}:
            os_choice = "auto"

        if os_choice == "auto":
            # 先根据路径特征判断
            if _is_windows_path(remote_path):
                os_choice = "windows"
            else:
                # 尝试通过 uname 判断
                try:
                    stdout, _ = ssh_client.exec_command("uname")
                    if stdout and "linux" in stdout.lower():
                        os_choice = "linux"
                    else:
                        # 再尝试 Windows 识别
                        stdout2, _ = ssh_client.exec_command("cmd /c ver")
                        if stdout2:
                            os_choice = "windows"
                        else:
                            # 默认按 linux 处理
                            os_choice = "linux"
                except Exception:
                    os_choice = "linux"

        log.info(f"Remote XML streaming parse using OS='{os_choice}' for path: {remote_path}")

        if os_choice == "windows":
            # 直接实现流式解析，不调用不存在的方法
            ps_cmd = f'powershell -Command "Get-Content -Wait -Encoding UTF8 \"{remote_path}\""'
            log.info(f"[stream_parse_remote_xml] Windows PowerShell命令: {ps_cmd}")
            try:
                # 启动命令
                if hasattr(ssh_client, 'ssh_client') and ssh_client.ssh_client:
                    transport = ssh_client.ssh_client.get_transport()
                    channel = transport.open_session()
                    channel.exec_command(ps_cmd)
                else:
                    log.error("ssh_client.ssh_client 未初始化")
                    return False

                parser = ET.XMLPullParser(["start", "end"])
                buffer = ""
                start_time = time.time()
                last_data_time = time.time()
                encoding_used = encoding or "utf-8"
                # 处理超时
                while True:
                    if timeout_sec and (time.time() - start_time > timeout_sec):
                        log.error(f"stream_parse_remote_xml 超时({timeout_sec}s)")
                        break
                    if stop_condition and stop_condition():
                        log.info("stream_parse_remote_xml: stop_condition触发，提前终止")
                        break
                    # 检查数据可读
                    if channel.recv_ready():
                        data = channel.recv(4096)
                        if not data:
                            time.sleep(poll_interval_sec)
                            continue
                        try:
                            text = data.decode(encoding_used, errors="ignore")
                        except Exception as e:
                            log.warning(f"decode error: {e}")
                            text = data.decode("utf-8", errors="ignore")
                        buffer += text
                        last_data_time = time.time()
                        # 直接送入parser
                        parser.feed(text)
                        for event, elem in parser.read_events():
                            # 只回调指定标签
                            if element_tag is None or elem.tag.lower() == element_tag.lower():
                                on_element(elem, event)
                                # 清理已处理元素，防止内存泄漏
                                elem.clear()
                    else:
                        # 没有新数据，短暂sleep
                        time.sleep(poll_interval_sec)
                        # 如果长时间无新数据也break
                        if timeout_sec and (time.time() - last_data_time > timeout_sec):
                            log.error(f"stream_parse_remote_xml 长时间无新数据，超时退出")
                            break
                channel.close()
                return True
            except Exception as e:
                log.error(f"stream_parse_remote_xml windows实现异常: {e}")
                return False
        else:
            if not hasattr(ssh_client, "stream_parse_remote_xml"):
                log.error("SSHClient 缺少 stream_parse_remote_xml 方法")
                return False
            return ssh_client.stream_parse_remote_xml(
                remote_path=remote_path,
                on_element=on_element,
                element_tag=element_tag,
                from_beginning=from_beginning,
                stop_condition=stop_condition,
                timeout_sec=timeout_sec,
                encoding=encoding,
                poll_interval_sec=poll_interval_sec,
            )

    @staticmethod
    def copy_dir(src_dir, dst_dir, keep_ori_dir: bool = True):
        try:
            if (
                src_dir
                and os.path.exists(src_dir)
                and dst_dir
                and os.path.exists(dst_dir)
            ):
                if keep_ori_dir:
                    dst_dir = os.path.join(dst_dir, os.path.basename(src_dir))
                shutil.copytree(
                    src_dir, dst_dir, dirs_exist_ok=True, copy_function=shutil.copy2
                )
                return True
        except Exception as e:
            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"[{current_file}][{current_line}].exception:{str(e)}")
        return False

    @staticmethod
    def copy_file(src, dst):
        try:
            if src and os.path.exists(src):
                shutil.copy2(src, dst)
                return True
        except Exception as e:
            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"[{current_file}][{current_line}].exception:{str(e)}")
        return False

    @staticmethod
    def move_file(src, dst):
        if not os.path.exists(src):
            log.error(f"Source file does not exist: {src}")
            return False

        dst_dir = os.path.dirname(dst)
        if not os.path.exists(dst_dir):
            try:
                os.makedirs(dst_dir)
                log.info(f"Created destination directory: {dst_dir}")
            except OSError as e:
                log.error(f"Failed to create destination directory {dst_dir}: {e}")
                return False

        try:
            shutil.move(src, dst)
            log.info(f"Moved file from {src} to {dst}")
            return True
        except Exception as e:
            log.error(f"Failed to move file from {src} to {dst}: {e}")
            return False

    @staticmethod
    def del_file(file_path):
        import subprocess
        from src.config import conf
        user_pass = conf.get_config_value("ipc_info").get("password", "ENGVV202410@")
        cmd_rm = f"echo {user_pass} | sudo -S rm -rf {file_path}"
        result = subprocess.run(cmd_rm, shell=True, executable="/bin/bash")
        if result.returncode != 0:
            log.error(f"sudo rm -rf {file_path} failed.")
            return False
        log.info(f"rm {file_path} succeeded")
        return True

    @staticmethod
    def make_directory(path):
        dir = os.path.abspath(path)
        if os.path.exists(dir):
            return True
        try:
            os.makedirs(dir, exist_ok=True)
            os.chmod(
                dir,
                stat.S_IRUSR
                | stat.S_IWUSR
                | stat.S_IXUSR
                | stat.S_IRGRP
                | stat.S_IWGRP
                | stat.S_IXGRP
                | stat.S_IROTH
                | stat.S_IWOTH
                | stat.S_IXOTH,
            )
        except Exception as e:
            import subprocess
            from src.config import conf

            user_pass = conf.get_config_value("ipc_info").get("password", "ENGVV202410@")
            log.warning(f"make dirs got exception:{str(e)}, try sudo")
            cmd_mkdir = (
                f"echo {user_pass} | sudo -S mkdir -p {dir} && sudo chmod a+w {dir}"
            )
            result = subprocess.run(cmd_mkdir, shell=True, executable="/bin/bash")
            if result.returncode != 0:
                log.error(f"sudo mkdir failed.")
                return False
        return True

    @staticmethod
    def del_make_dir(path):
        import os

        dir = os.path.abspath(path)
        if os.path.exists(dir):
            try:
                shutil.rmtree(dir)
            except Exception as e:
                ret = FileHelper.del_file(dir)
                if not ret:
                    log.warning(f"cannot delete {dir}, try sudo")
                    return ""
        try:
            os.makedirs(dir, exist_ok=True)
            os.chmod(
                dir,
                stat.S_IRUSR
                | stat.S_IWUSR
                | stat.S_IXUSR
                | stat.S_IRGRP
                | stat.S_IWGRP
                | stat.S_IXGRP
                | stat.S_IROTH
                | stat.S_IWOTH
                | stat.S_IXOTH,
            )
        except Exception as e:
            import subprocess
            from src.config import conf

            user_pass = conf.get_config_value("ipc_info").get("password", "ENGVV202410@")
            log.warning(f"make dirs got exception:{str(e)}, try sudo")
            cmd_mkdir = (
                f"echo {user_pass} | sudo -S mkdir -p {dir} && sudo chmod a+w {dir}"
            )
            result = subprocess.run(cmd_mkdir, shell=True, executable="/bin/bash")
            if result.returncode != 0:
                log.error(f"sudo mkdir failed.")
                return ""
        return dir

    @staticmethod
    def write_json(file_path, data):

        abs_path = os.path.abspath(file_path)
        log.info(f"Writing data to {abs_path}")

        os.makedirs(os.path.dirname(abs_path), exist_ok=True)
        try:
            with open(abs_path, "w") as f:
                json.dump(data, f, indent=4)
            log.debug("Data successfully written to JSON file.")

        except Exception as e:
            log.error(f"An unexpected error occurred while writing JSON: {e}")
            return False
        return True

    @staticmethod
    def load_yaml(yaml_file):
        if not os.path.exists(yaml_file):
            log.error(f"the target yaml {yaml_file} not exists.")
            return {}
        results = {}
        with open(yaml_file, "r") as f:
            try:
                datas = yaml.safe_load(f)
            except Exception as e:
                log.error(f"got exception while loading {yaml_file}. {str(e)}")
                pass
            else:
                if datas:
                    results = datas
        return results

    @staticmethod
    def write_yaml(yaml_file, yaml_data, indent=None):
        try:
            with open(yaml_file, "w", encoding="utf-8") as f:
                yaml.dump(yaml_data, f, indent=indent)
        except Exception as e:
            log.error(f"An unexpected error occurred while writing YAML: {str(e)}")
            return False
        return True

    @staticmethod
    def zip(path, zipname):
        abs_path = os.path.abspath(path)
        if not os.path.exists(abs_path):
            log.error(f"{abs_path} not exist.ignore zip")
            return
        try:
            with zipfile.ZipFile(zipname, "w", zipfile.ZIP_DEFLATED) as zipf:
                for root, dirs, files in os.walk(abs_path):
                    for file in files:
                        filename = os.path.join(root, file)
                        # print(f'compressing {filename}')
                        zipf.write(filename, os.path.relpath(filename, abs_path))
        except Exception as e:
            import os, sys

            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"[{current_file}][{current_line}].exception:{str(e)}")
            import subprocess

            subprocess.call(["sudo", "zip", "-r", path, zipname])

    @staticmethod
    def unzip(zip_file, dir_path):
        if not os.path.exists(os.path.abspath(zip_file)):
            log.error(f"{zip_file} not exist.")
            return
        dir_abs_path = os.path.abspath(dir_path)
        file_name = os.path.splitext(os.path.basename(zip_file))[0]
        unzip_path = os.path.join(dir_abs_path, file_name)
        if not os.path.exists(unzip_path):
            os.makedirs(unzip_path, exist_ok=True)
        log.info(f"unzip to {unzip_path}")
        with zipfile.ZipFile(zip_file, "r") as zip_ref:
            zip_ref.extractall(unzip_path)

        return unzip_path

    @staticmethod
    def compress_and_delete_folder(folder_path, output_path):
        with tarfile.open(output_path, "w:gz") as tar:
            tar.add(folder_path, arcname=os.path.basename(folder_path))
        # shutil.rmtree(folder_path)
        FileHelper.del_dir(folder_path)

    @staticmethod
    def decompress_file(file_path, dest_dir, part_file=""):
        if not os.path.exists(os.path.abspath(file_path)):
            log.error(f"{file_path} not exist.")
            return False
        dest_dir = os.path.abspath(dest_dir)
        file_name = os.path.basename(file_path)
        if not os.path.exists(dest_dir):
            os.makedirs(dest_dir, exist_ok=True)
        log.info(f"unzip to {dest_dir}")

        extract_commands = {
            ".zip": f"unzip {file_path} {part_file} -d {dest_dir}",
            ".tar": f"tar -xvf {file_path} {part_file} -C {dest_dir}",
            ".tar.gz": f"tar -xzvf {file_path} {part_file} -C {dest_dir}",
            ".tgz": f"tar -xzvf {file_path} {part_file} -C {dest_dir}",
            ".rar": f"unrar x {file_path} {part_file} {dest_dir}",
        }

        cmd_unzip_file = ""
        for file_fmt in extract_commands:
            if file_name.endswith(file_fmt):
                cmd_unzip_file = extract_commands[file_fmt]
                break
        if not cmd_unzip_file:
            log.error(f"the file format is not supported - {file_name}")
            return False

        result = subprocess.run(
            cmd_unzip_file,
            shell=True,
            executable="/bin/bash",
            capture_output=True,
            text=True,
        )
        if result.returncode != 0:
            log.error(
                f"Execute {cmd_unzip_file} failed! Error message: {result.stderr}"
            )
            return False

        return True

    @staticmethod
    def load_json_files(directory, prefix="", suffix=".json"):
        file_paths = []
        for root, dirs, files in os.walk(directory):
            for file in files:
                if file.startswith(prefix) and file.endswith(suffix):
                    file_path = os.path.join(root, file)
                    file_paths.append(file_path)
        return file_paths

    @staticmethod
    def find_file(filename, search_path):
        result = None
        for root, dir, files in os.walk(search_path):
            if filename in files:
                result = os.path.join(root, filename)
                break
        return result

    @staticmethod
    def del_dir(path):
        ret = FileHelper.del_file(path)
        return ret

    @staticmethod
    def copy_from_remote(
        remote_path,
        local_path,
        host="**********",
        username="root",
        password="",
        port=22,
    ):
        try:
            command = f"sshpass -p '{password}' scp -r -P {port} -o 'StrictHostKeyChecking no' {username}@{host}:{remote_path} {local_path}"

            log.info(f"Executing command: {command}")
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True
            )
            stdout = result.stdout
            stderr = result.stderr
            if stderr:
                log.error(f"copy file from {host} failed with error: {stderr}")
            result = True if not stderr else False
            return result
        except Exception as ex:
            import os, sys

            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"ERROR.[{current_file}][{current_line}].exception:{str(ex)}")
            return False

    @staticmethod
    def copy_to_remote(
        remote_path,
        local_path,
        host="**********",
        username="root",
        password="",
        port=22,
    ):
        try:
            command = f"sshpass -p '{password}' scp -r -P {port} -o 'StrictHostKeyChecking no' {local_path} {username}@{host}:{remote_path}"
            log.info(f"Executing command: {command}")
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True
            )
            stdout = result.stdout
            stderr = result.stderr
            if stderr:
                log.error(f"copy file to {host} failed with error: {stderr}")
            result = True if not stderr else False
            return result
        except Exception as ex:
            import os, sys

            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"ERROR.[{current_file}][{current_line}].exception:{str(ex)}")
            return False
        
    @staticmethod
    def exists_or_make_dir(ssh_client, path):
        """
        通过ssh_client连接上windows，并查看路径是否存在，如果不存在，则创建路径文件夹
        """
        """
        通过 ssh_client 连接到远程 Windows，检查路径是否存在，不存在则创建。

        Args:
            ssh_client (paramiko.SSHClient): 已连接的 SSH 客户端
            path (str): 要检查或创建的路径（Windows 路径，需用双斜杠或转义）

        Returns:
            bool: True 表示路径存在或创建成功，False 表示失败
        """
        try:
            # 1. 检查路径是否存在
            check_cmd = f'if exist "{path}" (echo exists) else (echo not exists)'
            stdout, stderr = ssh_client.exec_command(check_cmd)

            if "exists" == stdout.lower():
                return True
            elif "not exists" == stdout.lower():
                # 2. 创建路径
                log.info(f"mkdir folder - {path}")
                mkdir_cmd = f'mkdir "{path}"'
                stdout, stderr = ssh_client.exec_command(mkdir_cmd)
                return True  # 假设创建成功
            else:
                # 未知响应
                return False
        except Exception as e:
            print(f"Error in exists_or_make_dir: {e}")
            return False
        
    @staticmethod
    def create_remote_file(ssh_client, remote_path_with_filename: str) -> bool:
        """
        在 Windows 远程主机上创建一个空文件。
        Args:
            ssh_client (paramiko.SSHClient): 已连接的 SSH 客户端
            remote_path_with_filename (str): 远程完整路径 + 文件名（例如：C:\\Logs\\OTA\\test.txt）
        Returns:
            bool: 创建成功返回 True，失败返回 False
        """
        try:
            # Windows cmd 创建空文件的方式（使用 type nul）
            command = f'type nul > "{remote_path_with_filename}"'
            stdout, stderr = ssh_client.exec_command(command)

            # 检查 stderr 输出
            if stderr:
                log.info(f"windows {remote_path_with_filename} 创建失败: {stderr}")
                return False, f"windows {remote_path_with_filename} 创建失败: {stderr}"

            log.info(f"成功创建远程空文件: {remote_path_with_filename}")
            return True, f"成功创建远程空文件: {remote_path_with_filename}"

        except Exception as e:
            log.error(f"创建远程空文件异常: {e}")
            return False, f"创建远程空文件异常: {e}"

    
    @staticmethod
    def delete_remote_file(
            remote_path,
            host="**********",
            username="Administrator",
            password="",
            port=22,
        ):
        """
        删除远端 Windows 系统上的文件或文件夹
        Args:
            remote_path (str): 远端文件或目录路径，使用 Windows 格式，例如 C:\\path\\to\\file.txt
            host (str): 主机IP
            username (str): 用户名
            password (str): 密码
            port (int): SSH端口
        Returns:
            bool: 删除是否成功
        """
        try:
            # 如果路径里有空格，需要加引号
            win_path = f'"{remote_path}"'

            # 判断是文件还是目录并删除
            command = (
                f"sshpass -p '{password}' ssh -p {port} -o 'StrictHostKeyChecking no' "
                f"{username}@{host} "
                f"powershell -Command \"if (Test-Path {win_path}) "
                f"{{ if ((Get-Item {win_path}).PSIsContainer) "
                f"{{ Remove-Item {win_path} -Recurse -Force }} "
                f"else {{ Remove-Item {win_path} -Force }} }}\""
            )

            log.info(f"Executing command: {command}")
            result = subprocess.run(
                command, shell=True, capture_output=True, text=True
            )
            stdout = result.stdout.strip()
            stderr = result.stderr.strip()
            if stderr:
                log.error(f"Delete file on {host} failed: {stderr}")
            else:
                log.info(f"Delete result: {stdout or 'Success'}")
            return True if not stderr else False

        except Exception as ex:
            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"ERROR.[{current_file}][{current_line}].exception:{str(ex)}")
            return False

def purge_history_data(path, diff_time):
    """According to diff_time delete related history data"""

    def remove_file_detail(file_path, diff_time):
        mtime = time.ctime(os.stat(file_path).st_mtime)  # time of last modification
        timestring = time.strptime(mtime, "%a %b %d %H:%M:%S %Y")
        timemiao = time.mktime(timestring)
        now = time.time()
        diff = now - timemiao
        if diff > diff_time:
            try:
                if (
                    os.path.exists(file_path)
                    and "holtest/calib" not in str(file_path).lower()
                ):
                    if os.path.isfile(file_path):
                        os.remove(file_path)
                    elif os.path.isdir(file_path):
                        shutil.rmtree(file_path)
                    log.info(f"delete history path = {file_path}")
            except Exception as e:
                log.error(f"delete history path = {file_path} failed")

    log.info(
        f"delete history data: path={path}, history diff_time = {diff_time} seconds"
    )
    if os.path.exists(path):
        for root, _, files in os.walk(path):
            for file in files:
                file_abs_path = os.path.join(root, file)
                remove_file_detail(file_abs_path, diff_time)

        for root, dirs, _ in os.walk(path):
            for dir in dirs:
                folder_abs_path = os.path.join(root, dir)
                remove_file_detail(folder_abs_path, diff_time)
    return True


def check_disk_usage(path: str):
    """check usage of specified directory"""
    usage = psutil.disk_usage(path)
    log.info(
        f"Total space: {usage.total / 1000000000} GB, free space {usage.free / 1000000000} GB, used percent: {usage.percent}%"
    )


def parse_testcase_maneuvers(xml_input: str) -> dict:
    """
    解析XML文件中的testcase部分，提取每个ManNo的信息、结果和结束条件，并返回数据字典。
    
    Args:
        xml_input: XML文件路径或XML内容字符串
        
    Returns:
        dict: 包含解析结果的数据字典，格式如：
            {
                "maneuvers": [
                    {
                        "man_no": 0,
                        "title": "Init_global_Variables - set direct variables to defined values",
                        "result": "",
                        'value_condition': "",
                        "end_condition": "ManChange due to TimeLimit: 1.00s"
                    },
                    ...
                ],
                "verdict": "pass",
                "total_maneuvers": 16
            }
    """
    import xml.etree.ElementTree as ET
    import re
    import os
    
    try:
        # 判断输入是文件路径还是XML内容
        if os.path.isfile(xml_input):
            # 输入是文件路径，读取文件内容
            with open(xml_input, 'r', encoding='utf-8') as f:
                xml_content = f.read()
        else:
            # 输入是XML内容字符串
            xml_content = xml_input
        
        # 解析XML内容
        root = ET.fromstring(xml_content)
        
        # 查找testcase元素
        testcase = root.find('.//testcase')
        if testcase is None:
            return {"maneuvers": [], "verdict": "", "total_maneuvers": 0}
        
        maneuvers = []
        current_man = None
        verdict_result = ""
        
        # 遍历testcase下的所有元素
        for elem in testcase:
            if elem.tag == 'teststep':
                # 提取ManNo信息
                text = elem.text or ""
                man_match = re.search(r'ManNo\s+(\d+):\s*(.+)', text)
                
                if man_match:
                    # 如果有之前的man，先保存
                    if current_man:
                        maneuvers.append(current_man)
                    
                    # 创建新的man
                    man_no = int(man_match.group(1))
                    title = man_match.group(2).strip()
                    
                    current_man = {
                        'man_no': man_no,
                        'title': title,
                        'result': '',
                        'value_condition': '',
                        'end_condition': ''
                    }
                
                # 检查是否有result属性
                if elem.get('result') and elem.get('result') != 'na':
                    if current_man:
                        current_man['result'] = elem.get('result')
                        current_man['value_condition'] = text.strip()
                
                # 检查是否是结束条件
                if 'ManChange due to' in text:
                    if current_man:
                        current_man['end_condition'] = text.strip()
            
            elif elem.tag == 'verdict':
                # 处理verdict
                if current_man:
                    # 保存最后一个man
                    maneuvers.append(current_man)
                    current_man = None
                
                # 获取verdict结果
                verdict_result = elem.get('result', '')
        
        # 确保最后一个man被保存
        if current_man:
            maneuvers.append(current_man)
        
        # 构建返回的数据字典
        result_dict = {
            "maneuvers": maneuvers,
            "verdict": verdict_result,
            "total_maneuvers": len(maneuvers)
        }
        
        return result_dict
        
    except ET.ParseError as e:
        print(f"XML解析错误: {e}")
        return {"maneuvers": [], "verdict": "", "total_maneuvers": 0}
    except FileNotFoundError:
        print(f"文件未找到: {xml_input}")
        return {"maneuvers": [], "verdict": "", "total_maneuvers": 0}
    except Exception as e:
        print(f"解析过程中发生错误: {e}")
        return {"maneuvers": [], "verdict": "", "total_maneuvers": 0}
    


if __name__ == "__main__":
    
    from src.utils.ssh_helper import SSHClient
    from src.config import conf

    ssh_client = SSHClient(
        conf.get_config_value("windows_pc_info")["ip"],
        conf.get_config_value("windows_pc_info")["username"],
        conf.get_config_value("windows_pc_info")["password"]
    )
    ssh_client.connect()
    # target_path = conf.get_config_value("carmaker")["tickle_folder_path"]

    # # import pdb; pdb.set_trace()
    # folder_existed = FileHelper.exists_or_make_dir(ssh_client, target_path)

    # #copy testreoprt to local
    # FileHelper.copy_from_remote(
    #     remote_path="C:/Users/<USER>/AppData/Local/HiLAutomation/report_ACC_SetSpeed_1.xml",
    #     local_path="/tmp/HIL/soc/auto/20250811/",
    #     host="************",
    #     username="admin",
    #     password="abc.123",
    #     port=22,
    # )
    
    
    result = parse_testcase_maneuvers("/tmp/HIL/soc/auto/20250811/report_ACC_SetSpeed_1.xml")
    # 访问具体数据
    for maneuver in result["maneuvers"]:
        print(f"ManNo {maneuver['man_no']}: {maneuver['title']}")
        print(f"结果: {maneuver['result']}")
        print(f"判断条件: {maneuver['value_condition']}")
        print(f"结束条件: {maneuver['end_condition']}")

    print(f"总maneuver数量: {result['total_maneuvers']}")
    print(f"最终结果: {result['verdict']}")

    FileHelper.delete_remote_file("C:/Users/<USER>/AppData/Local/HiLAutomation/1.xml")
    ssh_client.disconnect()