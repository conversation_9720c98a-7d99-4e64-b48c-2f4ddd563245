import sys
import datetime
from pathlib import Path
from loguru import logger

# 清除默认 handler
logger.remove()

# 构建日志目录: 相对 utils 文件夹，向上两级
BASE_DIR = Path(__file__).resolve().parent.parent.parent  # ../../
LOG_DIR = BASE_DIR / "test_reports" / "automation_log"

# 创建目录
LOG_DIR.mkdir(parents=True, exist_ok=True)

# 日志文件名带时间戳
timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
log_file_path = LOG_DIR / f"{timestamp}.log"

# 控制台日志输出
logger.add(
    sys.stdout,
    level="INFO",
    format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan> - <level>{message}</level>",
)

# 文件日志输出
logger.add(
    log_file_path,
    level="DEBUG",
    rotation="50 MB",
    retention="7 days",
    encoding="utf-8",
    enqueue=True,
)

# 暴露 log 对象供其他模块使用
log = logger