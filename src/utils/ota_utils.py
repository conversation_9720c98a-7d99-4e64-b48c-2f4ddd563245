import os
import smtplib
import time
import argparse
import subprocess
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import Header

from src.utils.logger import log
from src.config import conf
from src.utils.ssh_helper import SSHClient


class OTAExecutor:
    def __init__(self, ota_package: str, asw_id: str, sample_type: str, branch: str, mail_list: str):
        self.ota_package = ota_package
        self.asw_id = asw_id
        self.sample_type = sample_type
        self.branch = branch
        self.mail_list = mail_list

        self.ipc_info =  conf.get_config_value("ipc_info")
        self.ipc_path = self.ipc_info["ota_package_path"]

        self.get_ssh_client()

    def get_ssh_client(self):
        
        master_ecu_conf = conf.get_config_value("master_ecu_conf")

        self._ssh_client = SSHClient(
            host=master_ecu_conf["IP"],
            username=master_ecu_conf["username"],
            password=master_ecu_conf["password"],
        )
        self._ssh_client.connect()
        self._ssh_client.check_conection_and_retry()

    def close_ssh_client(self):
        self._ssh_client.disconnect()

    def log_inputs(self):
        log.info(f"OTA Package: {self.ota_package}")
        log.info(f"Sample Type: {self.sample_type}")
        log.info(f"Branch: {self.branch}")
        log.info(f"Mail List: {self.mail_list}")

    def download_file(self, url, dest_path, password):
        cmd = f'wget --user=env1szh --password={password} --no-check-certificate "{url}" -O "{dest_path}"'
        return os.system(cmd) == 0
    
    def jfrog_download(self, jfrog_url, local_file_path, max_retries=3):
        ret = False
        error_msg = None
        command = [
            "wget",
            "--user",
            self.ipc_info["username"],
            "--password",
            self.ipc_info["password"],
            "--no-check-certificate",
            "-O",
            local_file_path,
            jfrog_url,
        ]

        attempt = 0
        while attempt < max_retries:
            attempt += 1
            try:
                subpro = subprocess.run(
                    command,
                    check=True,
                    timeout=1000,
                    stderr=subprocess.PIPE,
                    stdout=subprocess.PIPE,
                )
                if subpro.returncode == 0:
                    if os.path.getsize(local_file_path) > 0:
                        ret = True
                        break  # 下载成功，退出循环
                    else:
                        error_msg = f"Download package {jfrog_url} from JFrog failed: file size is zero"
                        log.error(error_msg)
                else:
                    error_msg = f"Download package {jfrog_url} from JFrog failed: {subpro.stderr}, {subpro.stdout}"
                    log.error(error_msg)
            except Exception as exception:
                error_msg = (
                    f"Exception occurred during download attempt {attempt}: {exception}"
                )
                log.error(error_msg)

            if attempt < max_retries:
                log.info(f"Retrying download... Attempt {attempt}/{max_retries}")
                time.sleep(3)  # 等待3秒再重试
            else:
                log.error(f"Failed to download after {max_retries} attempts")

        return ret, error_msg

    def download_ota_package(self):
        sw_tag = self.ota_package.split("/")[-3]
        zip_url = f"{self.ota_package}{sw_tag}-jetour_d01_linux-zip.zip"
        rsa_url = f"{self.ota_package}{sw_tag}-jetour_d01_linux-zip_update.rsa"

        log.info(f"Downloading OTA zip: {zip_url}")
        log.info(f"Downloading RSA: {rsa_url}")

        package_ret, msg = self.jfrog_download(zip_url, f"{self.ipc_path}/update.zip")
        if not package_ret:
            return (
                False,
                f"Downloading ota package: {self.ipc_path}/update.zip failed with msg {msg} ",
            )
        
        rsa_ret, msg = self.jfrog_download(rsa_url, f"{self.ipc_path}/update.rsa")
        if not rsa_ret:
            return (
                False,
                f"Downloading ota package: {self.ipc_path}/update.rsa failed with msg {msg} ",
            )

        return True, ""

    def init_dut_workspace(self):
        status = self._ssh_client.check_conection_and_retry()
        if not status:
            log.error("ECU SSH connection is not available ...")
            return False, "SSH connection failed"

        # 删除指令
        clear_cmd = "rm -rf /ota/*.zip /ota/output /ota/output_stripped /ota/asw"
        stdout, stderr = self._ssh_client.exec_command(clear_cmd)

        # 检查是否清除干净
        check_cmd = "ls /ota"
        stdout, stderr = self._ssh_client.exec_command(check_cmd)

        # 判断是否还有残留文件
        remaining_files = stdout.strip().splitlines()
        error_output = stderr.strip()

        if error_output:
            log.error("Error while checking /ota directory: %s", error_output)
            return False, f"Failed to check /ota directory: {error_output}"

        # 如果还有未删除的目标项
        expected_keywords = ["output", "output_stripped", "asw"]
        leftovers = [f for f in remaining_files if f.endswith(".zip") or f in expected_keywords]

        if leftovers:
            msg = f"Not all files were deleted: {leftovers}"
            log.error(msg)
            return False, msg

        return True, ""

    def flash_ota(self):
        res = False
        flash_cmd = (
            "nohup bash -c 'export LD_LIBRARY_PATH=/opt/app/swupdate/;"
            "/opt/app/swupdate/Exe_LiteApp_Demo -m trigger' > /ota/trigger.log 2>&1 &"
        )
        query_cmd = (
            "export LD_LIBRARY_PATH=/opt/app/swupdate/ && "
            "/opt/app/swupdate/Exe_LiteApp_Demo -m getresult && cat /ota/bsw/log/result.json"
        )

        # 把OTA包拷贝到域控
        local_package_path = f"{self.ipc_path}/update.*"
        target_file_path = "/ota"
        master_ecu_ip = conf.get_config_value("master_ecu_conf")["IP"]
        command = f"sshpass -p '{self.ipc_info["password"]}' scp -P 22 -o 'StrictHostKeyChecking no' {local_package_path} root@{master_ecu_ip}:{target_file_path} "
        subpro = subprocess.run(
            command,
            shell=True,
            stderr=subprocess.PIPE,
            stdout=subprocess.PIPE,
            timeout=1800,
        )
        ret = subpro.returncode
        if ret:
            log.error(
                "package copy to ecu failed: %s, %s",
                local_package_path,
                subpro.stderr,
            )
            return (
                False,
                f"local package :{local_package_path} copy to remote ecu failed",
            )

        # 启动 flash 指令
        stdout, stderr = self._ssh_client.exec_command(flash_cmd)
        stdout, stderr = stdout.strip(), stderr.strip()

        if stderr:
            log.error(f"Flash command execution error: {stderr}")
            return False, f"Flash command execution error: {stderr}"

        log.info("Flashing OTA...")

        # 等待 OTA 完成
        t0 = time.time()
        while time.time() - t0 < 1800:
            try:
                stdout, stderr = self._ssh_client.exec_command(query_cmd)
                stdout, stderr = stdout.strip(), stderr.strip()

                if stderr:
                    log.warning(f"Query command stderr: {stderr}")

                log.info(f"Query result: {stdout}")
                if stdout:
                    last = stdout.strip().splitlines()[-1]
                    if "fail" in last:
                        log.error("OTA Flash reported failure.")
                        break
                    if "success" in last or "idle" in last:
                        time.sleep(180)
                        log.info("OTA Flash Success")
                        return True, ""
            except Exception as e:
                log.warning(f"Query exception: {e}")
            time.sleep(120)

        log.error("OTA timeout after 30 minutes")
        return res, "OTA timeout after 30 minutes"

    def send_mail(self, result: str):
        smtp = smtplib.SMTP("rb-smtp-auth.rbesz01.com", 25)
        smtp.starttls()
        smtp.login("apac/env1szh", self.ipc_info["password"])

        msg = MIMEMultipart()
        msg["From"] = "<EMAIL>"
        msg["To"] = self.mail_list
        msg["Subject"] = Header(f"J6E-HIL2-SmokingTest: OTA {result}", "utf-8")
        msg.attach(MIMEText(
            f'<h2>J6E-HIL2-SmokingTest: <span style="color: red;">OTA {result}</span></h2>',
            "html", "utf-8"
        ))
        smtp.sendmail(msg["From"], self.mail_list.split(","), msg.as_string())
        smtp.quit()
        log.info("Email sent")

    def run(self):
        self.log_inputs()
        try:
            ret, _ = self.download_ota_package()
            if not ret:
                log.error("download ota package failed")
                raise Exception("OTA Deploy Failed")
            
            ret, msg = self.init_dut_workspace()
            if not ret:
                log.error(msg)
                raise Exception("OTA Deploy Failed")
            
            ret, msg = self.flash_ota()
            if not ret:
                log.error(msg)
                raise Exception("OTA Deploy Failed")
            
            log.info("OTA Update Passed")
        except Exception as e:
            log.info(f"Deploy Error: {e}")
            self.send_mail("FAIL")
            raise
        finally:
            log.info("Deploy Finished")
            time.sleep(1)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("-p", "--ota_package", default="https://.../variant/")
    parser.add_argument("-a", "--asw_internal_build_id", default="J6E-ASW-develop-202410020000CST")
    parser.add_argument("-t", "--sample_type", default="B0")
    parser.add_argument("-b", "--branch", default="develop")
    parser.add_argument("-m", "--mail_list", default="<EMAIL>")
    args = parser.parse_args()

    executor = OTAExecutor(
        ota_package=args.ota_package,
        asw_id=args.asw_internal_build_id,
        sample_type=args.sample_type,
        branch=args.branch,
        mail_list=args.mail_list,
    )
    executor.run()


if __name__ == "__main__":
    main()