import time
import paramiko

from src.utils.logger import log


class SSHClient:
    def __init__(self, host, username, password):
        self.host = host
        self.username = username
        self.password = password
        self.ssh_client = None

    def connect(self, retry_max=3):
        cnt = 0
        while cnt < retry_max:
            self._connect()
            if self.is_connected():
                return True
            cnt += 1
            log.warning(f"retry to connect {self.host} after 2s")
            time.sleep(2)
        return False

    def _connect(self):
        try:
            self.ssh_client = paramiko.SSHClient()
            self.ssh_client.set_missing_host_key_policy(paramiko.AutoAddPolicy())
            self.ssh_client.connect(
                self.host,
                username=self.username,
                password=self.password,
                timeout=60,
                banner_timeout=60,
                auth_timeout=60,
                look_for_keys=False,
                allow_agent=False,
            )
        except Exception as ex:
            log.error(str(ex))
            self.ssh_client = None

    def exec_command(self, command, timeout=300):
        try:
            if not self.ssh_client or not self.is_connected():
                log.error(f"ERROR.ssh_client not init,ignore exec_command {command}")
                return "cmd_error", ""
            stdin, stdout, stderr = self.ssh_client.exec_command(
                command, timeout=timeout
            )
            log.debug(f"command:{command}")

            # 尝试解码（先utf-8，失败再用gbk）
            stdout_data = stdout.read()
            stderr_data = stderr.read()
            try:
                stdout = stdout_data.decode('utf-8').strip()
            except UnicodeDecodeError:
                stdout = stdout_data.decode('gbk', errors='replace').strip()

            try:
                stderr = stderr_data.decode('utf-8').strip()
            except UnicodeDecodeError:
                stderr = stderr_data.decode('gbk', errors='replace').strip()
            log.debug(f"stdout:{stdout}, stderr:{stderr}")
            return stdout, stderr

        except Exception as ex:
            import os, sys

            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"ERROR.[{current_file}][{current_line}].exception:{ex}")
            return "cmd_error", ""

    def check_dir(self, directory_path, need_mkdir=True):
        if not self.ssh_client or not self.is_connected():
            log.error(
                f"ERROR.ssh_client not init,ignore create_dir {directory_path}"
            )
            return
        check_command = f"[ -d {directory_path} ] && echo 'exists'"
        stdin, stdout, stderr = self.ssh_client.exec_command(check_command)

        output = stdout.read().decode().strip()
        if output == "exists":
            return True

        if need_mkdir:
            create_command = f"mkdir -p {directory_path}"
            stdin, stdout, stderr = self.ssh_client.exec_command(create_command)
            exit_status = stdout.channel.recv_exit_status()
            if exit_status == 0:
                return True
            else:
                log.error(f"ERROR.mkdir on ECU failed. {stderr}")
        return False

    def create_dir(self, directory_path):
        if not self.ssh_client or not self.is_connected():
            log.error(
                f"ERROR.ssh_client not init,ignore create_dir {directory_path}"
            )
            return
        check_command = f"[ -d {directory_path} ] && echo 'exists'"
        stdin, stdout, stderr = self.ssh_client.exec_command(check_command)

        output = stdout.read().decode().strip()
        if output == "exists":
            delete_command = f"rm -rf {directory_path}"
            create_command = f"mkdir {directory_path}"
            stdin, stdout, stderr = self.ssh_client.exec_command(delete_command)
            delete_status = stdout.channel.recv_exit_status()
            stdin, stdout, stderr = self.ssh_client.exec_command(create_command)
            create_status = stdout.channel.recv_exit_status()
            if create_status == 0 and delete_status == 0:
                return True
            return False
        else:
            create_command = f"mkdir {directory_path}"
            stdin, stdout, stderr = self.ssh_client.exec_command(create_command)
            exit_status = stdout.channel.recv_exit_status()
            if exit_status == 0:
                return True
            return False

    def is_file_exists(self, file_path):
        if not self.ssh_client or not self.is_connected():
            log.error(f"ERROR.ssh_client not init,ignore is_file_exists {file_path}")
            return
        stdin, stdout, stderr = self.ssh_client.exec_command(
            f'test -e {file_path} && echo "File exists" || echo "File does not exist"'
        )
        output = stdout.read().decode().strip()
        if output == "File exists":
            return True
        else:
            return False

    def exec_command_no_response(self, command):
        try:
            if not self.ssh_client or not self.is_connected():
                log.error(
                    f"ERROR.ssh_client not init,ignore exec_command_no_response {command}"
                )
                return
            stdin, stdout, stderr = self.ssh_client.exec_command(command)
        except Exception as ex:
            import os, sys

            current_file = os.path.abspath(__file__)
            current_line = sys._getframe().f_lineno
            log.error(f"ERROR.[{current_file}][{current_line}].exception:{str(ex)}")

    def is_connected(self):
        if self.ssh_client:
            transport = self.ssh_client.get_transport()
            if transport and transport.is_active():
                return True
        return False

    def get_process_status(self, command="ps -e"):
        if not self.ssh_client or not self.is_connected():
            log.error(
                f"ERROR.ssh_client not init,ignore get_process_status {command}"
            )
            return
        processes = []
        stdout, stderr = self.exec_command(command)
        for line in stdout.splitlines()[1:]:
            fields = line.split()
            process = {"pid": int(fields[0]), "name": fields[3]}
            processes.append(process)
        processes = [process.get("name") for process in processes]
        return processes

    def get_process_info(self, process_list, command="ps -e"):
        process_info_dict = {}
        if not self.ssh_client or not self.is_connected():
            print(f"ERROR.ssh_client not init,ignore get_process_status {command}")
            return process_info_dict
        stdout, stderr = self.exec_command(command)
        for line in stdout.splitlines()[1:]:
            fields = line.split()
            process = {"pid": int(fields[0]), "name": fields[3]}
            if fields[3] in process_list:
                process_info_dict.update({fields[0]: fields[3]})
        return process_info_dict

    def disconnect(self):
        if self.ssh_client and self.is_connected():
            self.ssh_client.close()

    def scopy_file(self, remote_path, local_path):
        try:
            if self.ssh_client or not self.is_connected():
                sftp = self.ssh_client.open_sftp()
                sftp.get(remote_path, local_path)
        except Exception as ex:
            print("copy_file", str(ex))
        finally:
            sftp.close()

    def check_conection_and_retry(self):
        if self.is_connected():
            return True
        else:
            return self.connect()


if __name__ == "__main__":
    import time

    ssh_client = SSHClient("**********", "root", "root")
    ssh_client.connect()
    std_out, std_error = ssh_client.exec_command("ls /")
    print(f"std_out:{std_out},std_error:{std_error}")
    # print(ssh_client.create_dir('/log/holtest'))
    # ssh_client.check_dir('/cache/update/hpa/lidar_map')
    # master_process = ["SensorAbstraction_activity","ViperDriving_activity","Planning_activity","ApaPlanning_activity"]
    # print(ssh_client.get_process_info(master_process))
