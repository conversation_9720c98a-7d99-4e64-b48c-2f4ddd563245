import os
import re
import time
import subprocess
import json
from typing import <PERSON>ple
from src.utils.logger import log
from src.config import conf

# 直接集成load_json

def load_json(path):
    with open(path, 'r', encoding='utf-8') as f:
        return json.load(f)


class MtcdService(object):
    def __init__(self):
        self.password = conf.get_config_value("ipc_info")["password"]

        mtd_cli_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "config",
            "mtcd",
            "mtcd_cli.json",
        )

        mtd_config_path = os.path.join(
            os.path.dirname(os.path.dirname(__file__)),
            "config",
            "mtcd",
            "driving_mtcd_config.json",
        )
        self.mtcd_cli = load_json(mtd_cli_path)
        self.mtcd_config = mtd_config_path

    def set_user_config_to_mtcd(self, scenario: str) -> bool:
        """
        设置用户自定义的MTCD配置

        Args:
            scenario: 场景类型 ("driving_whitelist", "hpa_whitelist", "apa_whitelist")

        Returns:
            bool: 配置设置是否成功
        """
        try:
            # 从配置文件获取MTCD白名单配置路径
            mtcd_whitelist_config_paths = conf.get_config_value("mtcd_whitelist_config_paths")
            if not mtcd_whitelist_config_paths:
                log.error("MTCD whitelist config paths not found in configuration")
                return False

            # 检查场景是否支持
            if scenario not in mtcd_whitelist_config_paths:
                log.error(f"Unsupported scenario: {scenario}. Supported: {list(mtcd_whitelist_config_paths.keys())}")
                return False

            # 获取对应场景的配置文件相对路径
            config_file_relative_path = mtcd_whitelist_config_paths[scenario]

            # 构建完整的配置文件路径
            module_path = os.path.abspath(__file__)
            project_root = os.path.dirname(os.path.dirname(module_path))
            config_file_path = os.path.join(project_root, config_file_relative_path)

            # 从同一配置项中获取输出路径
            output_path = mtcd_whitelist_config_paths.get("output_path")
            if not output_path:
                log.error("MTCD output path not found in configuration")
                return False

            # 复制配置到系统位置
            cmd = f"echo {self.password} | sudo -S cp {config_file_path} {output_path}"
            log.info(f"Copying MTCD config '{scenario}' ({config_file_relative_path}) to: {output_path}")

            result = subprocess.run(
                cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if result.returncode != 0:
                log.error(f"Failed to copy config: {result.stderr}")
                return False

            # 验证配置文件是否成功复制
            if not os.path.exists(output_path):
                log.error(f"Configuration file not found after copy operation: {output_path}")
                return False

            log.info(f"MTCD configuration '{scenario}' set successfully")
            return True

        except FileNotFoundError:
            log.error(f"Configuration file not found: {config_file_relative_path}")
            return False
        except Exception as e:
            log.error(f"Failed to set MTCD configuration: {e}")
            return False

    def start_mtcd_service(self) -> bool:
        """
        启动MTCD服务

        Returns:
            bool: True表示启动成功，False表示启动失败
        """
        try:
            # 首先检查服务是否已经在运行
            if self.check_mtcd_start_status():
                log.info("MTCD service is already running")
                return True

            # 从mtcd_cli.json获取启动命令配置
            mtcd_start_config = self.mtcd_cli.get("mtcd_service_start")
            if not mtcd_start_config:
                log.error("mtcd_service_start configuration not found in mtcd_cli.json")
                return False

            cmd_line = mtcd_start_config.get("cmd_line")
            if not cmd_line:
                log.error("cmd_line not found in mtcd_service_start configuration")
                return False

            # 构建完整的启动命令
            start_cmd = f'echo {self.password} | sudo -S {cmd_line}'
            log.info(f"Starting MTCD service with command: {start_cmd}")

            # 执行启动命令
            result = subprocess.run(
                start_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 检查命令执行结果
            if result.returncode != 0:
                log.error(f"Failed to execute start command. Return code: {result.returncode}")
                log.error(f"Error output: {result.stderr}")
                return False

            # 等待服务启动
            log.info("Waiting for MTCD service to start...")
            time.sleep(2)  # 增加等待时间确保服务完全启动

            # 验证服务是否成功启动
            max_retries = 3
            for attempt in range(max_retries):
                if self.check_mtcd_start_status():
                    log.info("MTCD service started successfully")
                    return True

                if attempt < max_retries - 1:
                    log.warning(f"Service not ready yet, retrying... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(1)

            log.error("MTCD service failed to start within expected time")
            return False

        except KeyError as e:
            log.error(f"Configuration key not found: {e}")
            return False
        except Exception as e:
            log.error(f"Failed to start MTCD service: {e}")
            return False

    def pkill_mtcd_service(self) -> bool:
        """
        强制终止MTCD服务进程

        使用pkill命令强制终止所有mtcd相关进程。
        这是一个紧急停止方法，当正常停止失败时使用。

        Returns:
            bool: True表示强制终止成功，False表示操作失败
        """
        try:
            # 首先检查是否有mtcd进程在运行
            check_cmd = "pgrep mtcd"
            check_result = subprocess.run(
                check_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if check_result.returncode != 0:
                log.info("No MTCD processes found running")
                return True

            # 显示将要终止的进程
            running_pids = check_result.stdout.strip().split('\n')
            log.info(f"Found MTCD processes with PIDs: {', '.join(running_pids)}")

            # 执行强制终止命令
            pkill_cmd = f"echo {self.password} | sudo -S pkill -9 mtcd"
            log.warning(f"Force killing MTCD processes with command: pkill -9 mtcd")

            result = subprocess.run(
                pkill_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 检查命令执行结果
            if result.returncode != 0 and result.returncode != 1:  # pkill返回1表示没找到进程，这是正常的
                log.error(f"Failed to execute pkill command. Return code: {result.returncode}")
                log.error(f"Error output: {result.stderr}")
                return False

            # 等待进程完全终止
            time.sleep(1)

            # 验证进程是否已被终止
            verify_result = subprocess.run(
                check_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if verify_result.returncode == 0:
                remaining_pids = verify_result.stdout.strip().split('\n')
                log.warning(f"Some MTCD processes still running: {', '.join(remaining_pids)}")

                # 尝试更强力的终止方式
                log.warning("Attempting more aggressive termination...")
                kill_cmd = f"echo {self.password} | sudo -S kill -9 {' '.join(remaining_pids)}"
                subprocess.run(kill_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                time.sleep(1)

                # 最终验证
                final_check = subprocess.run(check_cmd, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
                if final_check.returncode == 0:
                    log.error("Failed to terminate all MTCD processes")
                    return False

            log.info("All MTCD processes have been successfully terminated")
            return True

        except Exception as e:
            log.error(f"Failed to force kill MTCD service: {e}")
            return False

    def stop_mtcd_service(self) -> bool:
        """
        停止MTCD服务

        Returns:
            bool: True表示停止成功，False表示停止失败
        """
        try:
            # 首先检查服务是否已经停止
            if self.check_mtcd_stop_status():
                log.info("MTCD service is already stopped")
                return True

            # 从mtcd_cli.json获取停止命令配置
            mtcd_stop_config = self.mtcd_cli.get("mtcd_service_stop")
            if not mtcd_stop_config:
                log.error("mtcd_service_stop configuration not found in mtcd_cli.json")
                return False

            cmd_line = mtcd_stop_config.get("cmd_line")
            if not cmd_line:
                log.error("cmd_line not found in mtcd_service_stop configuration")
                return False

            # 构建完整的停止命令
            stop_cmd = f'echo {self.password} | sudo -S {cmd_line}'
            log.info(f"Stopping MTCD service with command: {stop_cmd}")

            # 执行停止命令
            result = subprocess.run(
                stop_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 检查命令执行结果
            if result.returncode != 0:
                log.error(f"Failed to execute stop command. Return code: {result.returncode}")
                log.error(f"Error output: {result.stderr}")
                return False

            # 等待服务停止
            log.info("Waiting for MTCD service to stop...")
            time.sleep(2)  # 增加等待时间确保服务完全停止

            # 验证服务是否成功停止
            max_retries = 3
            for attempt in range(max_retries):
                if self.check_mtcd_stop_status():
                    log.info("MTCD service stopped successfully")
                    return True

                if attempt < max_retries - 1:
                    log.warning(f"Service still running, retrying... (attempt {attempt + 1}/{max_retries})")
                    time.sleep(1)

            log.error("MTCD service failed to stop within expected time")
            return False

        except KeyError as e:
            log.error(f"Configuration key not found: {e}")
            return False
        except Exception as e:
            log.error(f"Failed to stop MTCD service: {e}")
            return False

    def config_mtcclient(self) -> bool:
        """
        配置MTCD客户端

        执行mtcclient config命令来应用配置文件。
        使用正则表达式匹配输出来判断是否成功。

        Returns:
            bool: True表示配置成功，False表示配置失败
        """
        try:
            # 从mtcd_cli.json获取mtcclient_config配置
            mtcclient_config_config = self.mtcd_cli.get("mtcclient_config")
            if not mtcclient_config_config:
                log.error("mtcclient_config configuration not found in mtcd_cli.json")
                return False

            cmd_line = mtcclient_config_config.get("cmd_line")
            regx_pattern = mtcclient_config_config.get("regx_pattern")

            if not cmd_line or not regx_pattern:
                log.error("cmd_line or regx_pattern not found in mtcclient_config configuration")
                return False

            log.info(f"Configuring MTCD client with command: {cmd_line}")

            # 执行配置命令
            result = subprocess.run(
                cmd_line,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30  # 设置30秒超时
            )

            # 获取命令输出
            output = result.stdout + result.stderr
            log.debug(f"Command output: {output}")

            # 检查命令执行结果
            if result.returncode != 0:
                log.error(f"mtcclient config command failed. Return code: {result.returncode}")
                log.error(f"Error output: {result.stderr}")
                return False

            # 使用正则表达式匹配输出
            if re.search(regx_pattern, output):
                log.info("MTCD client configuration applied successfully")
                return True
            else:
                log.error("MTCD client config failed - success pattern not found in output")
                log.error(f"Expected pattern: {regx_pattern}")
                log.error(f"Actual output: {output}")
                return False

        except subprocess.TimeoutExpired:
            log.error("MTCD client config command timed out after 30 seconds")
            return False
        except KeyError as e:
            log.error(f"Configuration key not found: {e}")
            return False
        except Exception as e:
            log.error(f"Failed to configure MTCD client: {e}")
            return False

    def start_mtcclient(self) -> Tuple[bool, str]:
        """
        启动MTCD客户端

        按顺序执行以下操作：
        1. 重新加载配置 (mtcclient reload)
        2. 应用配置 (mtcclient config)
        3. 启动客户端 (mtcclient start)

        Returns:
            Tuple[bool, str]: (成功状态, 错误信息或成功信息)
        """
        try:
            # 步骤1: 重新加载配置
            log.info("Step 1: Reloading MTCD client configuration...")

            # 获取reload配置
            mtcclient_reload_config = self.mtcd_cli.get("mtcclient_reload")
            if not mtcclient_reload_config:
                return False, "mtcclient_reload configuration not found in mtcd_cli.json"

            reload_cmd = mtcclient_reload_config.get("cmd_line")
            reload_pattern = mtcclient_reload_config.get("regx_pattern")

            if not reload_cmd:
                return False, "cmd_line not found in mtcclient_reload configuration"

            log.debug(f"Executing reload with command: {reload_cmd}")

            # 执行reload命令
            reload_result = subprocess.run(
                reload_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )

            if reload_result.returncode != 0:
                error_msg = f"Reload command failed with return code {reload_result.returncode}: {reload_result.stderr}"
                log.error(error_msg)
                return False, error_msg

            # 验证reload输出
            if reload_pattern:
                reload_output = reload_result.stdout + reload_result.stderr
                if not re.search(reload_pattern, reload_output):
                    error_msg = f"Reload success pattern not found in output. Expected: {reload_pattern}"
                    log.error(error_msg)
                    return False, error_msg

            log.debug("Successfully completed reload configuration")

            # 步骤2: 应用配置
            log.info("Step 2: Applying MTCD client configuration...")

            # 获取config配置
            mtcclient_config_config = self.mtcd_cli.get("mtcclient_config")
            if not mtcclient_config_config:
                return False, "mtcclient_config configuration not found in mtcd_cli.json"

            config_cmd = mtcclient_config_config.get("cmd_line")
            config_pattern = mtcclient_config_config.get("regx_pattern")

            if not config_cmd:
                return False, "cmd_line not found in mtcclient_config configuration"

            log.debug(f"Executing config with command: {config_cmd}")

            # 执行config命令
            config_result = subprocess.run(
                config_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )

            if config_result.returncode != 0:
                error_msg = f"Config command failed with return code {config_result.returncode}: {config_result.stderr}"
                log.error(error_msg)
                return False, error_msg

            # 验证config输出
            if config_pattern:
                config_output = config_result.stdout + config_result.stderr
                if not re.search(config_pattern, config_output):
                    error_msg = f"Config success pattern not found in output. Expected: {config_pattern}"
                    log.error(error_msg)
                    return False, error_msg

            log.debug("Successfully completed apply configuration")

            # 步骤3: 启动客户端
            log.info("Step 3: Starting MTCD client...")

            # 获取start配置
            mtcclient_start_config = self.mtcd_cli.get("mtcclient_start")
            if not mtcclient_start_config:
                return False, "mtcclient_start configuration not found in mtcd_cli.json"

            start_cmd = mtcclient_start_config.get("cmd_line")
            start_pattern = mtcclient_start_config.get("regx_pattern")

            if not start_cmd:
                return False, "cmd_line not found in mtcclient_start configuration"

            log.debug(f"Executing start with command: {start_cmd}")

            # 执行start命令
            start_result = subprocess.run(
                start_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )

            if start_result.returncode != 0:
                error_msg = f"Start command failed with return code {start_result.returncode}: {start_result.stderr}"
                log.error(error_msg)
                return False, error_msg

            # 验证start输出
            if start_pattern:
                start_output = start_result.stdout + start_result.stderr
                if not re.search(start_pattern, start_output):
                    error_msg = f"Start success pattern not found in output. Expected: {start_pattern}"
                    log.error(error_msg)
                    return False, error_msg

            log.debug("Successfully completed start client")

            log.info("MTCD client started successfully")
            return True, "MTCD client started successfully"

        except subprocess.TimeoutExpired:
            error_msg = "MTCD client command timed out after 30 seconds"
            log.error(error_msg)
            return False, error_msg
        except KeyError as e:
            error_msg = f"Configuration key not found: {e}"
            log.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Failed to start MTCD client: {e}"
            log.error(error_msg)
            return False, error_msg

    def stop_mtcclient(self) -> bool:
        """
        停止MTCD客户端

        执行mtcclient stop命令来停止客户端。
        使用正则表达式匹配输出来判断是否成功。

        Returns:
            bool: True表示停止成功，False表示停止失败
        """
        try:
            # 从mtcd_cli.json获取mtcclient_stop配置
            mtcclient_stop_config = self.mtcd_cli.get("mtcclient_stop")
            if not mtcclient_stop_config:
                log.error("mtcclient_stop configuration not found in mtcd_cli.json")
                return False

            cmd_line = mtcclient_stop_config.get("cmd_line")
            regx_pattern = mtcclient_stop_config.get("regx_pattern")

            if not cmd_line:
                log.error("cmd_line not found in mtcclient_stop configuration")
                return False

            log.info(f"Stopping MTCD client with command: {cmd_line}")

            # 执行停止命令
            result = subprocess.run(
                cmd_line,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30  # 设置30秒超时
            )

            # 获取命令输出
            output = result.stdout + result.stderr
            log.debug(f"Command output: {output}")

            # 检查命令执行结果
            if result.returncode != 0:
                log.error(f"mtcclient stop command failed. Return code: {result.returncode}")
                log.error(f"Error output: {result.stderr}")
                return False

            # 使用正则表达式匹配输出
            if regx_pattern:
                if re.search(regx_pattern, output):
                    log.info("MTCD client stopped successfully")
                    return True
                else:
                    log.error("MTCD client stop failed - success pattern not found in output")
                    log.error(f"Expected pattern: {regx_pattern}")
                    log.error(f"Actual output: {output}")
                    return False
            else:
                # 如果没有配置正则模式，仅基于返回码判断
                log.info("MTCD client stopped successfully (no pattern verification)")
                return True

        except subprocess.TimeoutExpired:
            log.error("MTCD client stop command timed out after 30 seconds")
            return False
        except KeyError as e:
            log.error(f"Configuration key not found: {e}")
            return False
        except Exception as e:
            log.error(f"Failed to stop MTCD client: {e}")
            return False

    def start_recording(self) -> Tuple[bool, str]:
        """
        开始MTCD记录

        执行开始记录命令，从命令输出中提取数据目录路径。

        Returns:
            Tuple[bool, str]: (成功状态, 输出目录路径或错误信息)
        """
        try:
            # 开始记录
            log.info("Starting MTCD recording...")

            # 从mtcd_cli.json获取start_recording配置
            mtcclient_start_recording_config = self.mtcd_cli.get("mtcclient_start_recording")
            if not mtcclient_start_recording_config:
                error_msg = "mtcclient_start_recording configuration not found in mtcd_cli.json"
                log.error(error_msg)
                return False, error_msg

            cmd_line = mtcclient_start_recording_config.get("cmd_line")
            regx_pattern = mtcclient_start_recording_config.get("regx_pattern")

            if not cmd_line:
                error_msg = "cmd_line not found in mtcclient_start_recording configuration"
                log.error(error_msg)
                return False, error_msg

            log.debug(f"Executing start recording with command: {cmd_line}")

            # 执行开始记录命令
            result = subprocess.run(
                cmd_line,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30
            )

            # 检查命令执行结果
            if result.returncode != 0:
                error_msg = f"Start recording command failed with return code {result.returncode}: {result.stderr}"
                log.error(error_msg)
                return False, error_msg

            # 获取命令输出
            output = result.stdout
            log.debug(f"Start recording command output: {output}")

            # 验证成功模式（如果配置了）
            if regx_pattern:
                if not re.search(regx_pattern, output):
                    error_msg = f"Start recording success pattern not found in output. Expected: {regx_pattern}"
                    log.error(error_msg)
                    log.error(f"Actual output: {output}")
                    return False, error_msg

            # 提取数据目录路径
            output_dir = ""
            datadir_match = re.search(r'dataDir":"(.*?)"', output)
            if datadir_match:
                output_dir = datadir_match.group(1)
                log.info(f"Successfully started recording, output directory: {output_dir}")
            else:
                error_msg = f"Failed to extract dataDir from output: {output}"
                log.error(error_msg)
                return False, error_msg

            return True, output_dir

        except subprocess.TimeoutExpired:
            error_msg = "Start recording command timed out after 30 seconds"
            log.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Failed to start recording: {e}"
            log.error(error_msg)
            return False, error_msg

    def stop_recording(self, target_path: str = None, custom_name: str = None) -> Tuple[bool, str]:
        """
        停止MTCD记录

        执行mtcclient stop-recording命令来停止记录。
        使用正则表达式匹配输出来判断是否成功。
        可选择性地重命名指定路径的最后一个文件夹和其中的.bag文件。

        Args:
            target_path: 可选参数，要重命名的目录路径，默认为None
            custom_name: 可选参数，新的文件夹和.bag文件名称，默认为None

        Returns:
            Tuple[bool, str]: (成功状态, 修改后的路径或成功信息)
        """
        try:
            # 从mtcd_cli.json获取mtcclient_stop_recording配置
            mtcclient_stop_recording_config = self.mtcd_cli.get("mtcclient_stop_recording")
            if not mtcclient_stop_recording_config:
                error_msg = "mtcclient_stop_recording configuration not found in mtcd_cli.json"
                log.error(error_msg)
                return False, error_msg

            cmd_line = mtcclient_stop_recording_config.get("cmd_line")
            regx_pattern = mtcclient_stop_recording_config.get("regx_pattern")

            if not cmd_line:
                error_msg = "cmd_line not found in mtcclient_stop_recording configuration"
                log.error(error_msg)
                return False, error_msg

            log.info("Stopping MTCD recording...")
            log.debug(f"Executing stop recording with command: {cmd_line}")

            # 执行停止记录命令
            result = subprocess.run(
                cmd_line,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=30  # 设置30秒超时
            )

            # 获取命令输出
            output = result.stdout + result.stderr
            log.debug(f"Stop recording command output: {output}")

            # 检查命令执行结果
            if result.returncode != 0:
                error_msg = f"Stop recording command failed with return code {result.returncode}: {result.stderr}"
                log.error(error_msg)
                return False, error_msg

            # 使用正则表达式匹配输出
            if regx_pattern:
                if not re.search(regx_pattern, output):
                    error_msg = f"Stop recording success pattern not found in output. Expected: {regx_pattern}"
                    log.error(error_msg)
                    log.error(f"Actual output: {output}")
                    return False, error_msg

            # 命令执行成功，记录日志
            log.info("MTCD recording stopped successfully")

            # 如果提供了路径和名称参数，执行重命名操作
            if target_path is not None and custom_name is not None:
                try:
                    # 检查目标路径是否存在
                    if not os.path.exists(target_path):
                        log.error(f"Target path does not exist: {target_path}")
                        return True, target_path

                    if not os.path.isdir(target_path):
                        log.error(f"Target path is not a directory: {target_path}")
                        return True, target_path

                    # 构建新的目录路径
                    parent_dir = os.path.dirname(target_path)
                    new_dir_path = os.path.join(parent_dir, custom_name)

                    log.info(f"Renaming directory: {target_path} -> {new_dir_path}")

                    # 重命名目录
                    try:
                        os.rename(target_path, new_dir_path)
                        log.info(f"Successfully renamed directory to: {new_dir_path}")
                    except OSError as e:
                        log.error(f"Failed to rename directory: {e}")
                        return True, target_path

                    # 查找并重命名.bag文件
                    try:
                        bag_files = [f for f in os.listdir(new_dir_path) if f.lower().endswith('.bag')]

                        if bag_files:
                            if len(bag_files) == 1:
                                old_bag_file = bag_files[0]
                                new_bag_name = f"{custom_name}.bag"

                                old_bag_path = os.path.join(new_dir_path, old_bag_file)
                                new_bag_path = os.path.join(new_dir_path, new_bag_name)

                                # 只有在名称不同时才重命名
                                if old_bag_file != new_bag_name:
                                    os.rename(old_bag_path, new_bag_path)
                                    log.info(f"Successfully renamed bag file: {old_bag_file} -> {new_bag_name}")
                                else:
                                    log.info(f"Bag file already has the target name: {old_bag_file}")
                            else:
                                log.warning(f"Found {len(bag_files)} .bag files, expected exactly 1: {bag_files}")
                        else:
                            log.warning("No .bag files found in directory")

                    except Exception as e:
                        log.warning(f"Failed to rename .bag file: {e}")
                        # 即使.bag文件重命名失败，目录重命名成功了，仍然返回成功

                    log.info(f"Rename operation completed, final path: {new_dir_path}")
                    return True, new_dir_path

                except Exception as e:
                    log.error(f"Failed to perform rename operation: {e}")
                    return True, target_path
            else:
                # 没有重命名操作，返回成功信息
                success_msg = "MTCD recording stopped successfully"
                return True, success_msg

        except subprocess.TimeoutExpired:
            error_msg = "Stop recording command timed out after 30 seconds"
            log.error(error_msg)
            return False, error_msg
        except KeyError as e:
            error_msg = f"Configuration key not found: {e}"
            log.error(error_msg)
            return False, error_msg
        except Exception as e:
            error_msg = f"Failed to stop recording: {e}"
            log.error(error_msg)
            return False, error_msg

    def check_mtcd_start_status(self) -> bool:
        """
        检查MTCD服务启动状态

        Returns:
            bool: True表示服务正在运行，False表示服务未运行
        """
        try:
            # 从mtcd_cli.json获取mtcd_start_status配置
            mtcd_start_config = self.mtcd_cli.get("mtcd_start_status")
            if not mtcd_start_config:
                log.error("mtcd_start_status configuration not found in mtcd_cli.json")
                return False

            cmd_line = mtcd_start_config.get("cmd_line")
            regx_pattern = mtcd_start_config.get("regx_pattern")

            if not cmd_line or not regx_pattern:
                log.error("cmd_line or regx_pattern not found in mtcd_start_status configuration")
                return False

            log.info(f"Checking MTCD start status with command: {cmd_line}")

            # 执行状态检查命令
            result = subprocess.run(
                cmd_line,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 获取命令输出
            output = result.stdout + result.stderr
            log.debug(f"Command output: {output}")

            # 使用正则表达式匹配输出
            if re.search(regx_pattern, output):
                log.info("MTCD service is active and running")
                return True
            else:
                log.warning("MTCD service is not active or not running")
                return False

        except Exception as e:
            log.error(f"Failed to check MTCD start status: {e}")
            return False

    def check_mtcd_stop_status(self) -> bool:
        """
        检查MTCD服务停止状态

        Returns:
            bool: True表示服务已停止，False表示服务仍在运行
        """
        try:
            # 从mtcd_cli.json获取mtcd_stop_status配置
            mtcd_stop_config = self.mtcd_cli.get("mtcd_stop_status")
            if not mtcd_stop_config:
                log.error("mtcd_stop_status configuration not found in mtcd_cli.json")
                return False

            cmd_line = mtcd_stop_config.get("cmd_line")
            regx_pattern = mtcd_stop_config.get("regx_pattern")

            if not cmd_line or not regx_pattern:
                log.error("cmd_line or regx_pattern not found in mtcd_stop_status configuration")
                return False

            log.info(f"Checking MTCD stop status with command: {cmd_line}")

            # 执行状态检查命令
            result = subprocess.run(
                cmd_line,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            # 获取命令输出
            output = result.stdout + result.stderr
            log.debug(f"Command output: {output}")

            # 使用正则表达式匹配输出
            if re.search(regx_pattern, output):
                log.info("MTCD service is inactive and dead")
                return True
            else:
                log.warning("MTCD service is still running")
                return False

        except Exception as e:
            log.error(f"Failed to check MTCD stop status: {e}")
            return False

    def move_to_cloud_storage(self, source_path: str) -> bool:
        """
        将源路径的文件夹移动到云存储路径

        Args:
            source_path: 源文件夹路径

        Returns:
            bool: 操作是否成功
        """
        try:
            # 从配置文件获取云存储路径
            mtcd_whitelist_config_paths = conf.get_config_value("mtcd_whitelist_config_paths")
            if not mtcd_whitelist_config_paths:
                log.error("mtcd_whitelist_config_paths not found in configuration")
                return False

            cloud_storage_path = mtcd_whitelist_config_paths.get("CloudStorage_path")
            if not cloud_storage_path:
                log.error("CloudStorage_path not found in mtcd_whitelist_config_paths configuration")
                return False

            # 检查源路径是否存在
            if not os.path.exists(source_path):
                log.error(f"Source path does not exist: {source_path}")
                return False

            # 执行移动操作
            mv_cmd = f"mv '{source_path}' '{cloud_storage_path}'"
            log.info(f"Moving folder: {mv_cmd}")

            result = subprocess.run(
                mv_cmd,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )

            if result.returncode == 0:
                log.info(f"Successfully moved folder to cloud storage")
                return True
            else:
                log.error(f"Failed to move folder: {result.stderr}")
                return False

        except Exception as e:
            log.error(f"Failed to move folder to cloud storage: {e}")
            return False


if __name__ == "__main__":
    mtcd_service = MtcdService()

    # 测试新的可配置方法
    # scenarios = ["driving_whitelist", "hpa_whitelist", "apa_whitelist"]

    # for scenario in scenarios:
    # success = mtcd_service.set_user_config_to_mtcd("driving_whitelist")
    # if success:
    #     log.info(f"✓ Successfully set driving_whitelist MTCD configuration")
    # else:
    #     log.error(f"✗ Failed to set driving_whitelist MTCD configuration")
    mtcd_service.check_mtcd_start_status()
    mtcd_service.check_mtcd_stop_status()
    # mtcd_service.stop_mtcd_service()
    # mtcd_service.set_user_config_to_mtcd("driving_whitelist")
    # mtcd_service.start_mtcd_service()

    # # 测试新的config_mtcclient方法
    # print("\n测试 config_mtcclient 方法...")
    # config_success = mtcd_service.config_mtcclient()
    # if config_success:
    #     log.info("✓ Successfully configured MTCD client")
    # else:
    #     log.error("✗ Failed to configure MTCD client")

    # # 测试优化后的stop_mtcclient方法
    # print("\n测试优化后的 stop_mtcclient 方法...")
    # stop_success = mtcd_service.stop_mtcclient()
    # if stop_success:
    #     log.info("✓ Successfully stopped MTCD client")
    # else:
    #     log.error("✗ Failed to stop MTCD client")

    # 测试优化后的start_recording方法
    print("\n测试优化后的 start_recording 方法...")
    recording_success, recording_result = mtcd_service.start_recording()
    if recording_success:
        log.info(f"✓ Successfully started recording, output directory: {recording_result}")
    else:
        log.error(f"✗ Failed to start recording: {recording_result}")
    
    time.sleep(10)
    # 测试优化后的stop_recording方法（带重命名功能）
    print("\n测试优化后的 stop_recording 方法...")
    if recording_success and recording_result.startswith('/'):
        # 使用录制路径和自定义名称测试重命名功能
        # stop_recording_success, final_path = mtcd_service.stop_recording(recording_result, "test_recording")
        stop_recording_success, final_path = mtcd_service.stop_recording(recording_result)
        if stop_recording_success:
            log.info(f"✓ Successfully stopped recording with rename, final path: {final_path}")

            # 测试move_to_cloud_storage方法
            print("\n测试 move_to_cloud_storage 方法...")
            move_success = mtcd_service.move_to_cloud_storage(final_path)
            if move_success:
                log.info("✓ Successfully moved to cloud storage")
            else:
                log.error("✗ Failed to move to cloud storage")
        else:
            log.error(f"✗ Failed to stop recording: {final_path}")
    else:
        # 没有录制路径，测试基本的stop_recording
        stop_recording_success, stop_recording_message = mtcd_service.stop_recording()
        if stop_recording_success:
            log.info(f"✓ {stop_recording_message}")
        else:
            log.error(f"✗ {stop_recording_message}")
