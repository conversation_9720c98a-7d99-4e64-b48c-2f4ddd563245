import os
import re
import json
import threading

from typing import Any

from src.utils.logger import log


def _dynamic_cast_string(val):
    if val == "True" or val == "true":
        return True
    if val == "False" or val == "false":
        return False

    try:
        return int(val)
    except:
        log.debug("%s is not a int", val)

    try:
        return float(val)
    except:
        log.debug("%s is not a float", val)

    log.debug("%s is string", val)
    return val


def _load_config(obj):
    log.info("begin to load config ... ...")
    # 读取config文件夹的json文件
    module_path = os.path.abspath(__file__)
    builtin_config_path = os.path.dirname(os.path.dirname(module_path))
    builtin_config_path = os.path.join(builtin_config_path, "config", "config.json")
    log.debug("builtin config %s", builtin_config_path)
    with open(builtin_config_path) as f:
        builtin_config = json.load(f)

    setattr(obj, "config", builtin_config)


def reload_config(obj, conf: dict):
    # load builtin_config again
    log.info("Reload configuration....")
    _load_config(obj)
    loaded_config = getattr(obj, "config")
    loaded_config.update(conf)
    setattr(obj, "config", loaded_config)


class Configuration:
    """
    一个增强版本的配置文件管理类， 参考config.json
    其结构为：
    {
        "your_key":{
            "value":...., #value部分
            "description":"本配置项的描述"
        }
    }
    其中 value 的值可以是任意的类型，例如数字，字符，bool或者级联的dict，但目前不支持数据。
    因此，key的表示方法是 根名字[.子名][:默认值]
    例如：dbc_channel_mapping.DA:1， 表示取dbc_channel_mapping下value dict的DA， 如果没有定义则取值1
    """

    cls_lock = threading.RLock()

    def __new__(cls, *args, **kwargs):
        with Configuration.cls_lock:
            if not hasattr(Configuration, "_instance"):
                Configuration._instance = object.__new__(cls)
                _load_config(Configuration._instance)
                log.info("instance created")
        # log.debug('return instance %s', Configuration._instance)
        return Configuration._instance

    def get_config_value(self, key: str, default_value: Any = None) -> Any:
        return self.get(key, default_value)

    def update_config(cls, config_dict):
        with Configuration.cls_lock:
            reload_config(Configuration._instance, config_dict)

    def __split_key_and_slice(self, the_key):
        matches = re.findall(r"\[\d+\]", the_key)
        if len(matches) > 1:
            raise Exception("%s is wrong expression", the_key)
        if len(matches) == 1:
            return the_key[: the_key.index("[")], int(
                matches[0].replace("[", "").replace("]", "")
            )
        return the_key, -1

    def get(self, key: str, default_value: Any = None):
        """
        get config， 支持级联的get。 可以利用 . 分割的方式获取内部级联的value。
        :param key 格式为 根名字[.子名][:默认值]
        :param default_value
        """
        if ":" in key:
            arr = key.split(":")
            default_value = _dynamic_cast_string(arr[1])
            log.info("%s contains default value %s", key, default_value)
            key = arr[0]

        config = getattr(self, "config")
        keys = key.split(".")

        the_key, slice_index = self.__split_key_and_slice(keys[0])

        if the_key not in config:
            log.warning(
                "%s does not in config, return default %s", the_key, default_value
            )
            return default_value

        value_obj = config[the_key]["value"]
        if slice_index >= 0:
            if not isinstance(value_obj, list):
                raise Exception("expression error, expacted array but not")
            value_obj = value_obj[slice_index]

        for nested_key in keys[1:]:
            the_key, slice_index = self.__split_key_and_slice(nested_key)
            if not isinstance(value_obj, dict):
                log.error("sub key %s is not an object", the_key)
                return default_value
            if the_key not in value_obj:
                log.error("sub key %s does not exists", the_key)
                return default_value
            value_obj = value_obj[the_key]
            if slice_index >= 0:
                if not isinstance(value_obj, list):
                    raise Exception("expression error, expacted array but not")
                value_obj = value_obj[slice_index]
        return value_obj

    def __getitem__(self, item):
        if not isinstance(item, str):
            raise Exception("only support str")
        return self.get_config_value(item, None)

conf = Configuration()