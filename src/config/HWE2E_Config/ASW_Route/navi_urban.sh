#!/bin/bash

#0 get build path
if [[ -z "$ASW_PATH" ]]; then
    BUILD_DIR="$PWD"
    export ASW_PATH=$BUILD_DIR/ # Defines asw software path
fi
PHIGENT_PATH="${ASW_PATH}Phigent/"

#1 ROS
# source /opt/ros/noetic/setup.bash

#2 add runtime library
export LD_LIBRARY_PATH=/opt/vrte/lib:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH=/opt/app/lib:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH=$ASW_PATH/lib_shared:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH=$ASW_PATH/lib:$LD_LIBRARY_PATH
export LD_LIBRARY_PATH=$ASW_PATH/lib/hdmap/lib:$LD_LIBRARY_PATH
export MODECODE="E03"
export DRIVEMODE="AWD"
export ASU="EXIST"
export LC="LC13"
export RBP_PARAM_ROOT_FOLDER="${ASW_PATH}package/param/rt_param"
# export RBP_VIS_RESOURCES_DIR="${ASW_PATH}package/data/vis/resources"
export RBP_VIS_VARIANT="jetour_d01"
export RBP_VIS_VEHICLE_MODEL_DIR="${ASW_PATH}package/data/vis/vehicle_models"

#3 environment variables
export ENABLE_NP=ON # Defines the communication method between iceoryx roudi and clients, ON is shared memory, OFF is mqueue
export DISABLE_INTROSPECTION=ON # Disable the introspection function in RouDi daemon, which means the two introspection threads will NOT be created
export DISABLE_ALIVE_CHECK=OFF # Disable the alive check function both in normal activity and RouDi daemon
export OPENCV_FOR_THREADS_NUM=1 # Defines the number of threads used by OpenCV for parallel regions
# map_loc_provider core affinity and priority
export MAP_LOC_PROVIDER_CORE_AFFINITY=3
export MAP_LOC_PROVIDER_PRIORITY=27
export GLOBAL_MSF_WRAPPER_CORE_AFFINITY=3
export GLOBAL_MSF_WRAPPER_PRIORITY=25
export MAP_MATCHER_WRAPPER_CORE_AFFINITY=3
export MAP_MATCHER_WRAPPER_PRIORITY=26
export HDMAP_CORE_AFFINITY=3
export HDMAP_PRIORITY=27
export MAP_MATCH_PREPROC_CORE_AFFINITY=3
export MAP_MATCH_PREPROC_PRIORITY=26
# E2E planning
export PNC_DATA_ROOT=${ASW_PATH}
export AI_DATA_ROOT=${ASW_PATH}

#4 phigent environment variables
export LD_LIBRARY_PATH=${LD_LIBRARY_PATH}:"${PHIGENT_PATH}lib":"${PHIGENT_PATH}lib_shared"
export PHIGENT_PERCEPTION_GFLAGS_CONFIG="${PHIGENT_PATH}config/perception/perception_gflags_vio_j6e.conf"
export GDC_BIN_IGNORE_ERROR_CHECK=1
export PHIGENT_POSTFUSION_GFLAGS_CONFIG="${PHIGENT_PATH}/config/postfusion/postfusion_gflags.conf"
export PHIGENT_VME_GFLAGS_CONFIG="${PHIGENT_PATH}/config/vme/localization_gflags.conf"

#5 typed memory
if [[ -d "/sys/firmware/devicetree/base/reserved-memory/etas_dma_buffer@838000000" ]] \
    || [[ -d "/sys/firmware/devicetree/base/reserved-memory/etas_dma_buffer@830000000" ]]; then
    export IOX_MMAP_MEMORY_MODE=2 # Represents shared memroy for management SHM, contiguous physical memory for segmented SHM
    export USING_ETAS_MEMORY_SRC_DT=ON # Means that the physical memory is obtained through the DT of etas-mem
fi

#6 config arp
arp -s ************ 02:51:52:00:00:02
arp -s ************ 02:51:52:00:00:03

/asw/scripts/route_gen_arch /map/nilite/route/NaviInfo-1691370178879_dushuhu_dushuhu_straight