{"mtcd_start_status": {"cmd_line": "systemctl status mtcd.service", "regx_pattern": ".*active.*running.*"}, "mtcd_stop_status": {"cmd_line": "systemctl status mtcd.service", "regx_pattern": ".*inactive.*dead.*"}, "mtcd_service_start": {"cmd_line": "systemctl start mtcd.service", "regx_pattern": ".*"}, "mtcd_service_stop": {"cmd_line": "systemctl stop mtcd.service", "regx_pattern": ".*"}, "mtcclient_reload": {"cmd_line": "/opt/mtc/bin/mtcclient reload -f", "regx_pattern": ".*Success!\n"}, "mtcclient_config": {"cmd_line": "/opt/mtc/bin/mtcclient config", "regx_pattern": ".*Success!\n"}, "mtcclient_start": {"cmd_line": "/opt/mtc/bin/mtcclient start", "regx_pattern": ".*Success!\n"}, "mtcclient_start_recording": {"cmd_line": "/opt/mtc/bin/mtcclient startRecording", "regx_pattern": ".*Success!\n"}, "mtcclient_stop_recording": {"cmd_line": "/opt/mtc/bin/mtcclient stopRecording", "regx_pattern": ".*Success!\n"}, "mtcclient_stop": {"cmd_line": "/opt/mtc/bin/mtcclient stop", "regx_pattern": ".*Success!\n"}, "mtcd_log": {"cmd_line": "cat /var/log/syslog | grep mtcd > /tmp/mtcd.log", "regx_pattern": ".*"}}