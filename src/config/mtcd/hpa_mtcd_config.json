{"imageFormat": "jpeg", "serveIp": "0.0.0.0", "servePort": 50000, "recorderList": ["cache", "rosbag", "image"], "recordDir": "/tmp", "vin": "14142C", "recordMode": "trigger", "timeBeforeTrigger": 30, "timeAfterTrigger": 10, "startWithoutRecording": true, "autoSplit": false, "autoSplitInterval": 300, "acquisitionType": "driving", "functionType": "bench_parking_hpa_training", "platform": "J6E", "tenant": "<PERSON><PERSON>", "proxy": {"address": "***************", "port": 50000}, "cacheRecorder": {"cacheSize": 10000000, "latchedPublishInterval": 20, "reopenDataflow": true, "latchedServices": ["REGEX:.*camera_provider__camera_intrinsic_set_senderport.*", "REGEX:.*camera_provider__camera_error_info_senderport.*", "REGEX:.*cross_gateway__sensor_calib_parameter_senderport.*", "REGEX:.*cross_gateway__variant_function_config_senderport.*", "REGEX:.*cross_gateway__camera_error_info_senderport.*", "REGEX:.*aracom_gateway__aracom_lcmstate_senderport.*", "REGEX:.*t20__runnable_main_proc_m_port_main_proc_out.*"], "dataflows": [{"comment": "", "sourceIp": "************", "sinkIp": "**************", "serviceList": ["1C:6Webcam:7Webcam0", "1C:6Camera:9FrontWide", "1C:6Camera:14NearRangeFront", "1C:6Camera:13NearRangeLeft", "1C:6Camera:13NearRangeRear", "1C:6Camera:14NearRangeRight", "32:ApaFctAciFaultEventListInterface38:apa_fct__aci___apa_fct_aci_evm_itf_out32:ApaFctAciFaultEventListInterface", "20:CFusasmControlOutput39:apa_fct__aci___netcom_onePMcControl_out20:CFusasmControlOutput", "17:RbpActiveFunction24:apa_fct__active_function17:RbpActiveFunction", "26:RbpApaDisplayInformationHU35:apa_fct__apa_display_information_hu26:RbpApaDisplayInformationHU", "13:ParkctlOutput29:apa_fct__apa_fct_park_ctl_out13:ParkctlOutput", "15:FctPubFuncState34:apa_fct__apa_func_state_senderport15:FctPubFuncState", "18:RbpApaHmiToCtlInfo43:apa_fct__apa_hmi_to_apa_ctl_info_senderport18:RbpApaHmiToCtlInfo", "23:RbCnApaHmi2SvsGwSignals33:apa_fct__apa_hmi_to_svs_gw_output23:RbCnApaHmi2SvsGwSignals", "29:RbpPmpInjectedParkingManeuver38:apa_fct__apa_injected_parking_maneuver29:RbpPmpInjectedParkingManeuver", "25:RbpApaParkingSpaceInfoVis35:apa_fct__apa_parking_space_info_vis25:RbpApaParkingSpaceInfoVis", "14:RbpApactlModes21:apa_fct__apactl_modes14:RbpApactlModes", "15:RbpApactlNotify26:apa_fct__apactl_notify_out15:RbpApactlNotify", "24:PersistJsonInterfaceType38:apa_fct__apactl_persist_jsonsenderport24:PersistJsonInterfaceType", "25:FunctionStateResponseType50:apa_fct__apafct_function_state_response_senderport25:FunctionStateResponseType", "36:ApaFctOverallFaultEventListInterface35:apa_fct__apafct_overall_evm_itf_out36:ApaFctOverallFaultEventListInterface", "12:RbpApaHmiPdm23:apa_fct__apahmi_pdm_out12:RbpApaHmiPdm", "9:RbpPsdHmi28:apa_fct__awp_injc_target_out9:RbpPsdHmi", "9:RbpPsdHmi28:apa_fct__awp_park_target_out9:RbpPsdHmi", "19:RbpApaInfoStatusItf30:apa_fct__fct_nfm_status_output19:RbpApaInfoStatusItf", "24:PersistJsonInterfaceType38:apa_fct__manwrn_persist_jsonsenderport24:PersistJsonInterfaceType", "21:RbpMasterctlStatusOut30:apa_fct__master_ctl_status_out21:RbpMasterctlStatusOut", "12:RbpMcaMaaPfc24:apa_fct__mcamaactl_modes12:RbpMcaMaaPfc", "12:RbpMcaMebPfc24:apa_fct__mcamebctl_modes12:RbpMcaMebPfc", "13:RbpMcaMoisPfc25:apa_fct__mcamoisctl_modes13:RbpMcaMoisPfc", "18:RbpMeasurementMode25:apa_fct__measurement_mode18:RbpMeasurementMode", "18:RbpMebControlState26:apa_fct__meb_control_state18:RbpMebControlState", "12:RbpMebMaaPdm24:apa_fct__meb_maa_pdm_out12:RbpMebMaaPdm", "20:RbpMotAllowedSenders28:apa_fct__mot_allowed_senders20:RbpMotAllowedSenders", "34:RbpMotEmergencyDecelerationRequest43:apa_fct__mot_emergency_deceleration_request34:RbpMotEmergencyDecelerationRequest", "35:RbpMotGradeBasedDecelerationRequest45:apa_fct__mot_grade_based_deceleration_request35:RbpMotGradeBasedDecelerationRequest", "24:RbpMotServiceRequestsLat32:apa_fct__mot_service_request_lat24:RbpMotServiceRequestsLat", "25:RbpMotServiceRequestsLong33:apa_fct__mot_service_request_long25:RbpMotServiceRequestsLong", "32:RbpMotSecureWhenAtStandstillLong35:apa_fct__mot_vehicle_secure_request32:RbpMotSecureWhenAtStandstillLong", "21:RbpMotctlControlState33:apa_fct__motctl_control_state_out21:RbpMotctlControlState", "13:RbpNfmOpmodes20:apa_fct__nfm_Opmodes13:RbpNfmOpmodes", "7:Message52:apa_fct__output_message_fct_hmi_apa_to_shadow_facade7:Message", "21:RbpParkTrajectoryVisu29:apa_fct__park_trajectory_visu21:RbpParkTrajectoryVisu", "13:ParkhmiOutput32:apa_fct__parkhmioutputsenderport13:ParkhmiOutput", "23:RbCnPdcHmiAudoInterface28:apa_fct__pdc_hmi_audo_output23:RbCnPdcHmiAudoInterface", "17:RbCnPdcHmiSignals31:apa_fct__pdc_hmi_meb_maa_output17:RbCnPdcHmiSignals", "9:RbpPdcPfc30:apa_fct__pdcctlpf_pfc_messages9:RbpPdcPfc", "20:RbpPmpOperatingModes26:apa_fct__pmpctl_pmp_sender20:RbpPmpOperatingModes", "9:RbpPsdPfc26:apa_fct__psdctl_psd_sender9:RbpPsdPfc", "29:RbpPmpSelectedParkingManeuver38:apa_fct__selected_parking_maneuver_out29:RbpPmpSelectedParkingManeuver", "16:RbpUssmpCnCtlItf21:apa_fct__ussmp_output16:RbpUssmpCnCtlItf", "12:RbpUssperCtl22:apa_fct__ussper_output12:RbpUssperCtl", "24:PersistJsonInterfaceType45:aracom_gateway__apactl_persist_jsonsenderport24:PersistJsonInterfaceType", "24:BswLcmStateInterfaceType42:aracom_gateway__aracom_lcmstate_senderport24:BswLcmStateInterfaceType", "24:PersistJsonInterfaceType42:aracom_gateway__avm_persist_jsonsenderport24:PersistJsonInterfaceType", "28:DiagnoseRequestInterfaceType39:aracom_gateway__diag_request_senderport28:DiagnoseRequestInterfaceType", "24:PersistJsonInterfaceType42:aracom_gateway__fct_persist_jsonsenderport24:PersistJsonInterfaceType", "30:FidReactionIdListInterfaceType41:aracom_gateway__fid_reactionid_senderport30:FidReactionIdListInterfaceType", "24:FunctionStateRequestType49:aracom_gateway__function_state_request_senderport24:FunctionStateRequestType", "24:PersistJsonInterfaceType45:aracom_gateway__manwrn_persist_jsonsenderport24:PersistJsonInterfaceType", "28:SensorXxtrinsicInterfaceType57:aracom_gateway__offline_sensor_calib_parameter_senderport28:SensorXxtrinsicInterfaceType", "28:SensorXxtrinsicInterfaceType56:aracom_gateway__online_sensor_calib_parameter_senderport28:SensorXxtrinsicInterfaceType", "24:PersistJsonInterfaceType49:aracom_gateway__rbpinfragw_persist_jsonsenderport24:PersistJsonInterfaceType", "24:PersistJsonInterfaceType50:aracom_gateway__rbpinfragw_persist_jsonsenderport124:PersistJsonInterfaceType", "24:PersistJsonInterfaceType50:aracom_gateway__rbpinfragw_persist_jsonsenderport224:PersistJsonInterfaceType", "28:SensorXxtrinsicInterfaceType49:aracom_gateway__sensor_calib_parameter_senderport28:SensorXxtrinsicInterfaceType", "34:VariantFunctionConfigInterfaceType50:aracom_gateway__variant_function_config_senderport34:VariantFunctionConfigInterfaceType", "40:VehicleIdentificationNumberInterfaceType53:aracom_gateway__vehicle_identification_num_senderport40:VehicleIdentificationNumberInterfaceType", "27:VehicleVariantInterfaceType42:aracom_gateway__vehicle_variant_senderport27:VehicleVariantInterfaceType", "24:FuncMonitorInterfaceType39:camera_service__camera_function_monitor24:FuncMonitorInterfaceType", "17:RbpUssmpCnEchoItf39:datahub_gateway__datahub_RcoreUssMp_Out17:RbpUssmpCnEchoItf", "22:DvrWorkStatusInterface31:dvr_gateway__dvr_WorkStatus_Out22:DvrWorkStatusInterface", "20:CSentryModeStatusItf45:dvr_gateway__sentry_mode_aracom_status_output20:CSentryModeStatusItf", "31:DynamicFusionToApaInterfaceType54:dynamic_fusion__driving_ego_dynamic_objects_senderport31:DynamicFusionToApaInterfaceType", "31:DynamicFusionToApaInterfaceType54:dynamic_fusion__dynamic_fusion_apa_obj_list_senderport31:DynamicFusionToApaInterfaceType", "24:FuncMonitorInterfaceType47:dynamic_fusion__dynamic_fusion_function_monitor24:FuncMonitorInterfaceType", "33:DynamicFusionObjListInterfaceType50:dynamic_fusion__dynamic_fusion_obj_list_senderport33:DynamicFusionObjListInterfaceType", "26:DynamicFusion_StateService35:dynamic_fusion.dynamic_fusion$State26:DynamicFusion_StateService", "40:DynamicfusionFaultEventListInterfaceType56:dynamic_fusion__dynamicfusion_faultevent_list_senderport40:DynamicfusionFaultEventListInterfaceType", "33:DynamicFusionObjListInterfaceType56:dynamic_fusion__parking_local_dynamic_objects_senderport33:DynamicFusionObjListInterfaceType", "27:Aosfsmrunnable_StateService24:fct.aosfsmrunnable$State27:Aosfsmrunnable_StateService", "30:Aoshpahmirunnable_StateService27:fct.aoshpahmirunnable$State30:Aoshpahmirunnable_StateService", "30:Aoshpahsmrunnable_StateService27:fct.aoshpahsmrunnable$State30:Aoshpahsmrunnable_StateService", "29:Aosractlrunnable_StateService26:fct.aosractlrunnable$State29:Aosractlrunnable_StateService", "35:CRACTLToRapathrecorderInterfaceType39:fct__cractlto_rapathrecorder_senderport35:CRACTLToRapathrecorderInterfaceType", "27:CvaloutHmiSomeipTxInterface26:fct__cvalout_hmi_someip_tx27:CvaloutHmiSomeipTxInterface", "27:CvaloutSensorStAllInterface41:fct__cvalout_sensor_status_all_senderport27:CvaloutSensorStAllInterface", "24:FuncMonitorInterfaceType25:fct__fct_function_monitor24:FuncMonitorInterfaceType", "25:FunctionStateResponseType43:fct__fct_function_state_response_senderport25:FunctionStateResponseType", "20:CFCTOverallStateType33:fct__fct_overall_state_senderport20:CFCTOverallStateType", "34:CFCTSOCOnlyFunctionStatesInterface45:fct__fct_soc_to_mcu_function_state_senderport34:CFCTSOCOnlyFunctionStatesInterface", "35:CFCTDrivingToVMCOutputInterfaceType23:fct__fdmtovmcsenderport35:CFCTDrivingToVMCOutputInterfaceType", "25:FsmBehaviorsInterfaceType29:fct__fsm_behaviors_senderport25:FsmBehaviorsInterfaceType", "12:CHmismOutput26:fct__hmismoutputsenderport12:CHmismOutput", "19:CHpaToApaInfoOutput28:fct__hpa_to_apa_fct_info_out19:CHpaToApaInfoOutput", "21:CHpaToApaOpModeOutput30:fct__hpa_to_apa_fct_opmode_out21:CHpaToApaOpModeOutput", "24:PersistJsonInterfaceType26:fct__persistjsonsenderport24:PersistJsonInterfaceType", "11:RaCtlOutput26:fct__ractloutputsenderport11:RaCtlOutput", "28:CHUtoFctRequestInterfaceType42:host_gateway__chuto_fct_request_senderport28:CHUtoFctRequestInterfaceType", "19:CvalinHostInterface35:host_gateway__cvalin_hmi_senderport19:CvalinHostInterface", "22:ImuCorrectionInterface31:imu__vmps_ImuCorrectionData_Out22:ImuCorrectionInterface", "15:ImuRawInterface24:imu__vmps_ImuRawData_Out15:ImuRawInterface", "16:CImuRbvInterface24:imu__vmps_ImuRbvData_Out16:CImuRbvInterface", "12:InsInterface21:imu__vmps_InsData_Out12:InsInterface", "28:XPerMapMatchingInterfaceType45:loc_map_proxy__map_matching_result_senderport28:XPerMapMatchingInterfaceType", "32:XPerGlobalLocResultInterfaceType48:loc_map_proxy__xper_global_loc_result_senderport32:XPerGlobalLocResultInterfaceType", "24:FuncMonitorInterfaceType44:local_mapping__localmapping_function_monitor24:FuncMonitorInterfaceType", "21:LocalMapInterfaceType47:local_mapping__xper_local_map_result_senderport21:LocalMapInterfaceType", "24:FuncMonitorInterfaceType36:local_msf__localmsf_function_monitor24:FuncMonitorInterfaceType", "28:XOdometryResultInterfaceType38:local_msf__xodometry_result_senderport28:XOdometryResultInterfaceType", "32:XOdometryResult2McuInterfaceType45:local_msf__xodometry_result_to_mcu_senderport32:XOdometryResult2McuInterfaceType", "31:XPerLocalLocResultInterfaceType43:local_msf__xper_local_loc_result_senderport31:XPerLocalLocResultInterfaceType", "11:VariantConf38:master_parameter_gateway__variant_conf11:VariantConf", "24:FuncMonitorInterfaceType39:motion_control__motion_function_monitor24:FuncMonitorInterfaceType", "36:MotionCtlFaultEventListInterfaceType52:motion_control__motionctl_faultevent_list_senderport36:MotionCtlFaultEventListInterfaceType", "19:VmcControlDebugInfo48:motion_control__vmc_control_debuginfo_senderport19:VmcControlDebugInfo", "22:VmcOutputInterfaceType37:motion_control__vmc_output_senderport22:VmcOutputInterfaceType", "19:CHVMOutputInterface34:netcom_gateway__netcom_HvmData_Out19:CHVMOutputInterface", "38:CFCTMCUOnlytoSOCFunctionStateInterface48:netcom_gateway__netcom_McuToSocFunctionState_Out38:CFCTMCUOnlytoSOCFunctionStateInterface", "18:RbpUss3rdOutPutItf38:netcom_gateway__netcom_RcoreUss3rd_Out18:RbpUss3rdOutPutItf", "17:RbpUssmpCnEchoItf37:netcom_gateway__netcom_RcoreUssMp_Out17:RbpUssmpCnEchoItf", "37:CvalinDrivingParkingBodyInterfaceType42:netcom_gateway__netcom_VehicleBodyData_Out37:CvalinDrivingParkingBodyInterfaceType", "40:CvalinDrivingParkingChassisInterfaceType45:netcom_gateway__netcom_VehicleChassisData_Out40:CvalinDrivingParkingChassisInterfaceType", "41:CvalinDrivingParkingSpecificInterfaceType46:netcom_gateway__netcom_VehicleSpecificData_Out41:CvalinDrivingParkingSpecificInterfaceType", "31:OfflineCalibResultInterfaceType50:online_calibration__online_calib_result_senderport31:OfflineCalibResultInterfaceType", "12:PnPDebugText17:pnp__hpa_env_info12:PnPDebugText", "12:PnPDebugText17:pnp__hpa_kpi_info12:PnPDebugText", "12:PnPDebugText17:pnp__hpa_lat_info12:PnPDebugText", "12:PnPDebugText17:pnp__hpa_lon_info12:PnPDebugText", "12:PnPDebugText18:pnp__hpa_main_info12:PnPDebugText", "12:PnPDebugText17:pnp__hpa_mkw_info12:PnPDebugText", "22:PlanningRuntimeKeyInfo30:pnp__planning_runtime_key_info22:PlanningRuntimeKeyInfo", "32:PlanningSafetyToFctInterfaceType38:pnp__planning_safety_to_fct_senderport32:PlanningSafetyToFctInterfaceType", "30:PlanningToControlInterfaceType35:pnp__planning_to_control_senderport30:PlanningToControlInterfaceType", "26:PlanningToFctInterfaceType31:pnp__planning_to_fct_senderport26:PlanningToFctInterfaceType", "30:PnPFaultEventListInterfaceType35:pnp__pnp_faultevent_list_senderport30:PnPFaultEventListInterfaceType", "32:PredDebugInfoOutputInterfaceType37:pnp__prediction_debug_info_senderport32:PredDebugInfoOutputInterfaceType", "23:PredictionInterfaceType26:pnp__prediction_senderport23:PredictionInterfaceType", "13:PredictionSet26:pnp__prediction_set_output13:PredictionSet", "32:ReferenceLineOutputInterfaceType30:pnp__reference_line_senderport32:ReferenceLineOutputInterfaceType", "24:FuncMonitorInterfaceType51:ra_path_recorder__ra_path_recorder_function_monitor24:FuncMonitorInterfaceType", "29:RATrajectoryInfoInterfaceType47:ra_path_recorder__ra_trajectory_info_senderport29:RATrajectoryInfoInterfaceType", "15:RbpVisOutPutItf44:rbp_avm_dummy_proxy__avm_combined_val_output15:RbpVisOutPutItf", "24:PersistJsonInterfaceType47:rbp_avm_dummy_proxy__avm_persist_jsonsenderport24:PersistJsonInterfaceType", "36:EnvPerOverallFaultEventListInterface39:rbp_env_per__envper_overall_evm_itf_out36:EnvPerOverallFaultEventListInterface", "25:RbpUssPerAll_StateService33:rbp_env_per.rbp_uss_per_all$State25:RbpUssPerAll_StateService", "14:RbpNfmContours37:rbp_env_per__static_fusion___contours14:RbpNfmContours", "26:NfmFaultEventListInterface52:rbp_env_per__static_fusion___env_per_nfm_evm_itf_out26:NfmFaultEventListInterface", "9:RbpNfmMoe32:rbp_env_per__static_fusion___moe9:RbpNfmMoe", "23:RbpStaticFusionDebugItf52:rbp_env_per__static_fusion___static_fusion_debug_out23:RbpStaticFusionDebugItf", "21:RbpStaticFusionPdmItf50:rbp_env_per__static_fusion___static_fusion_pdm_out21:RbpStaticFusionPdmItf", "28:RbpStaticFusionStateDebugItf52:rbp_env_per__static_fusion___static_fusion_state_out28:RbpStaticFusionStateDebugItf", "21:RbpNfmUssOnlyContours45:rbp_env_per__static_fusion___ussonly_contours21:RbpNfmUssOnlyContours", "28:RbpStaticFusion_StateService31:rbp_env_per.static_fusion$State31:RbpStaticFusionState_StateEvent", "14:RbpDegradation36:rbp_env_per__ussper_degradation_port14:RbpDegradation", "13:RbpDetections35:rbp_env_per__ussper_detections_port13:RbpDetections", "12:RbpUssperEvm30:rbp_env_per__ussper_evmif_port12:RbpUssperEvm", "11:RbpFeatures33:rbp_env_per__ussper_features_port11:RbpFeatures", "12:RbpFreespace34:rbp_env_per__ussper_freespace_port12:RbpFreespace", "26:AVMFaultEventListInterface29:rbp_infra_gw__avm_evm_itf_out26:AVMFaultEventListInterface", "9:RbpAvmPdm25:rbp_infra_gw__avm_pdm_out9:RbpAvmPdm", "15:RbpUssmpEchoItf28:rbp_infra_gw__common_echo_if15:RbpUssmpEchoItf", "14:RbpVisInputItf43:rbp_infra_gw__infra_avm_combined_val_output14:RbpVisInputItf", "23:RbpInjectedManeuverUser36:rbp_infra_gw__injected_maneuver_user23:RbpInjectedManeuverUser", "13:RbpMcaBusData33:rbp_infra_gw__mca_vehicle_signals13:RbpMcaBusData", "44:RbpMotFromExternalForTarSteeringAngleVariant42:rbp_infra_gw__mot_if_steeringangle_variant44:RbpMotFromExternalForTarSteeringAngleVariant", "33:RbpMotFromExternalForTarSxVariant31:rbp_infra_gw__mot_if_sx_variant33:RbpMotFromExternalForTarSxVariant", "9:RbpPmpVal20:rbp_infra_gw__pmp_if9:RbpPmpVal", "13:RbpInfraValin36:rbp_infra_gw__rbp_infra_valin_output13:RbpInfraValin", "29:RbpXPerViperInternalEnvOddItf46:rbp_infra_gw__rbp_internal_xper_env_odd_output29:RbpXPerViperInternalEnvOddItf", "24:PersistJsonInterfaceType47:rbp_infra_gw__rbpinfragw_persist_jsonsenderport24:PersistJsonInterfaceType", "56:CvaloutADCCParkingFuncInfoADASstrtSvsFuncStatusInterface36:rbp_infra_gw__svs_func_status_output56:CvaloutADCCParkingFuncInfoADASstrtSvsFuncStatusInterface", "19:RbCnGwApaFctSignals39:rbp_infra_gw__svs_gw_pdc_signals_output19:RbCnGwApaFctSignals", "27:RbpNfmUssOnlyFromMCUInfoItf35:rbp_infra_gw__uss_info_from_mcu_out27:RbpNfmUssOnlyFromMCUInfoItf", "15:RbpUssStatusItf31:rbp_infra_gw__uss_status_output15:RbpUssStatusItf", "12:RbpUssperVal23:rbp_infra_gw__ussper_if12:RbpUssperVal", "26:RbCnVisRefGwVehicleSignals42:rbp_infra_gw__visrefgw_vehicle_signals_cpj26:RbCnVisRefGwVehicleSignals", "9:RbpVmeVal20:rbp_infra_gw__vme_if9:RbpVmeVal", "12:RbpApaHmiPdm26:rbp_infra__apahmi_pdm_read12:RbpApaHmiPdm", "9:RbpAvmPdm23:rbp_infra__avm_pdm_read9:RbpAvmPdm", "16:RbpCsiPdmCnRoads23:rbp_infra__csi_pdm_read16:RbpCsiPdmCnRoads", "10:RbpAVMFlCl35:rbp_infra__failure_status_info_port10:RbpAVMFlCl", "10:RbpNfmFlCl19:rbp_infra__flcl_nfm10:RbpNfmFlCl", "13:RbpPdcFlclItf19:rbp_infra__flcl_pdc13:RbpPdcFlclItf", "10:RbpPsdFlCl19:rbp_infra__flcl_psd10:RbpPsdFlCl", "13:RbpUssperFlcl22:rbp_infra__flcl_ussper13:RbpUssperFlcl", "30:FidReactionIdListInterfaceType35:rbp_infra__infra_fid_reactionid_out30:FidReactionIdListInterfaceType", "21:RbpInfraRMDebugOutput30:rbp_infra__infra_rm_debug_info21:RbpInfraRMDebugOutput", "12:RbpMcaConfig21:rbp_infra__mca_config12:RbpMcaConfig", "12:RbpMebMaaPdm27:rbp_infra__meb_maa_pdm_read12:RbpMebMaaPdm", "14:RbpMotCnConfig24:rbp_infra__mot_cn_config14:RbpMotCnConfig", "12:RbpMotConfig21:rbp_infra__mot_config12:RbpMotConfig", "12:RbpNfmConfig21:rbp_infra__nfm_config12:RbpNfmConfig", "27:RbpOnePCnConanVersionConfig32:rbp_infra__onp_conan_version_out27:RbpOnePCnConanVersionConfig", "34:ParkingFaultEventListInterfaceType46:rbp_infra__parking_falultevent_list_senderport34:ParkingFaultEventListInterfaceType", "24:PersistJsonInterfaceType37:rbp_infra__pdm_persist_jsonsenderport24:PersistJsonInterfaceType", "24:PersistJsonInterfaceType38:rbp_infra__pdm_persist_jsonsenderport124:PersistJsonInterfaceType", "24:PersistJsonInterfaceType38:rbp_infra__pdm_persist_jsonsenderport224:PersistJsonInterfaceType", "12:RbpPfcConfig21:rbp_infra__pfc_config12:RbpPfcConfig", "12:RbpPmpConfig21:rbp_infra__pmp_config12:RbpPmpConfig", "15:RbpPmpHmiConfig24:rbp_infra__pmphmi_config15:RbpPmpHmiConfig", "12:RbpPsdConfig21:rbp_infra__psd_config12:RbpPsdConfig", "21:RbpStaticFusionPdmItf33:rbp_infra__static_fusion_pdm_read21:RbpStaticFusionPdmItf", "15:RbpUssperConfig24:rbp_infra__ussper_config15:RbpUssperConfig", "24:RbpVmeSteeringConversion40:rbp_infra__vhm___steering_conversion_itf24:RbpVmeSteeringConversion", "18:RbpVmeDebugInfoItf31:rbp_infra__vhm___vme_debug_info18:RbpVmeDebugInfoItf", "26:VmeFaultEventListInterface32:rbp_infra__vhm___vme_evm_itf_out26:VmeFaultEventListInterface", "9:RbpVmePdm28:rbp_infra__vhm___vme_pdm_out9:RbpVmePdm", "27:RbpExternalVhmAbstBufferPub53:rbp_infra__vhm_abst___external_vhm_abst_buffer_output27:RbpExternalVhmAbstBufferPub", "21:RbpExternalVhmAbstPub46:rbp_infra__vhm_abst___external_vhm_abst_output21:RbpExternalVhmAbstPub", "16:RbpVhmAbstBuffer44:rbp_infra__vhm_abst___vhm_abst_buffer_output16:RbpVhmAbstBuffer", "10:RbpVhmAbst37:rbp_infra__vhm_abst___vhm_abst_output10:RbpVhmAbst", "26:VmeFaultEventListInterface37:rbp_infra__vhm_abst___vme_evm_itf_out26:VmeFaultEventListInterface", "12:RbpVmeConfig21:rbp_infra__vme_config12:RbpVmeConfig", "9:RbpVmePdm23:rbp_infra__vme_pdm_read9:RbpVmePdm", "28:RbpMotExternalAxLimitRequest45:rbp_mca__mca_maa_ax_limit_request_output_port28:RbpMotExternalAxLimitRequest", "12:RbpMcaMaaItf28:rbp_mca__mca_maa_output_port12:RbpMcaMaaItf", "19:RbpMcaMebBrakeState40:rbp_mca__mca_meb_brake_state_output_port19:RbpMcaMebBrakeState", "12:RbpMcaMebItf28:rbp_mca__mca_meb_output_port12:RbpMcaMebItf", "33:McaOverallFaultEventListInterface32:rbp_mca__mca_overall_evm_itf_out33:McaOverallFaultEventListInterface", "11:RbpMebDebug25:rbp_mca__meb_debug_output11:RbpMebDebug", "34:RbpMotEmergencyDecelerationRequest55:rbp_mca__mot_emergency_deceleration_request_output_port34:RbpMotEmergencyDecelerationRequest", "29:McaPdcFaultEventListInterface39:rbp_mca__pdc_main___mca_pdc_evm_itf_out29:McaPdcFaultEventListInterface", "22:RbpPdcCollisionAreaItf42:rbp_mca__pdc_main___pdc_collision_area_out22:RbpPdcCollisionAreaItf", "15:RbpPdcOutputItf27:rbp_mca__pdc_main___pdc_out15:RbpPdcOutputItf", "22:MotCoreCn_StateService39:rbp_mot_core_instance.mot_core_cn$State25:MotCoreCnState_StateEvent", "29:RbpMotDesiredDrivingDirection52:rbp_mot_core_instance__mot_desired_driving_direction29:RbpMotDesiredDrivingDirection", "34:RbpMotEmergencyDecelerationRequest57:rbp_mot_core_instance__mot_emergency_deceleration_request34:RbpMotEmergencyDecelerationRequest", "26:MotFaultEventListInterface38:rbp_mot_core_instance__mot_evm_itf_out26:MotFaultEventListInterface", "35:RbpMotGradeBasedDecelerationRequest59:rbp_mot_core_instance__mot_grade_based_deceleration_request35:RbpMotGradeBasedDecelerationRequest", "32:RbpMotSteeringInStandstillActive69:rbp_mot_core_instance__mot_internal_steering_in_standstill_active_out32:RbpMotSteeringInStandstillActive", "35:RbpMotLongitudinalControllerRequest53:rbp_mot_core_instance__mot_long_ax_controller_request35:RbpMotLongitudinalControllerRequest", "35:RbpMotLongitudinalControllerRequest53:rbp_mot_core_instance__mot_long_sx_controller_request35:RbpMotLongitudinalControllerRequest", "25:RbpMotManeuverFeedbackLat48:rbp_mot_core_instance__mot_maneuver_feedback_lat25:RbpMotManeuverFeedbackLat", "26:RbpMotManeuverFeedbackLong49:rbp_mot_core_instance__mot_maneuver_feedback_long26:RbpMotManeuverFeedbackLong", "26:RbpMotPathProviderFeedback49:rbp_mot_core_instance__mot_path_provider_feedback26:RbpMotPathProviderFeedback", "32:RbpMotSecureWhenAtStandstillLong61:rbp_mot_core_instance__mot_secure_when_at_standstill_long_out32:RbpMotSecureWhenAtStandstillLong", "32:RbpMotSelectedPathSenderFeedback56:rbp_mot_core_instance__mot_selected_path_sender_feedback32:RbpMotSelectedPathSenderFeedback", "30:RbpMotServiceStatusFeedbackLat54:rbp_mot_core_instance__mot_service_status_feedback_lat30:RbpMotServiceStatusFeedbackLat", "31:RbpMotServiceStatusFeedbackLong55:rbp_mot_core_instance__mot_service_status_feedback_long31:RbpMotServiceStatusFeedbackLong", "29:RbpMotSteeringActuatorRequest52:rbp_mot_core_instance__mot_steering_actuator_request29:RbpMotSteeringActuatorRequest", "11:RbpMotTarAx33:rbp_mot_core_instance__mot_tar_ax11:RbpMotTarAx", "22:RbpMotTarSteeringAngle51:rbp_mot_core_instance__mot_tar_steering_angle_front22:RbpMotTarSteeringAngle", "22:RbpMotTarSteeringAngle50:rbp_mot_core_instance__mot_tar_steering_angle_rear22:RbpMotTarSteeringAngle", "11:RbpMotTarSx33:rbp_mot_core_instance__mot_tar_sx11:RbpMotTarSx", "33:RbpMotUnexpectedEventsFeedbackLat57:rbp_mot_core_instance__mot_unexpected_events_feedback_lat33:RbpMotUnexpectedEventsFeedbackLat", "34:RbpMotUnexpectedEventsFeedbackLong58:rbp_mot_core_instance__mot_unexpected_events_feedback_long34:RbpMotUnexpectedEventsFeedbackLong", "13:RbpCsiContext40:rbp_pmp_psd_ccp__csire___csi_context_out13:RbpCsiContext", "16:RbpCsiPdmCnRoads36:rbp_pmp_psd_ccp__csire___csi_pdm_out16:RbpCsiPdmCnRoads", "11:RbpCsiRoads30:rbp_pmp_psd_ccp__csire___roads11:RbpCsiRoads", "34:RbpMotEmergencyDecelerationRequest47:rbp_pmp_psd_ccp__emergency_deceleration_request34:RbpMotEmergencyDecelerationRequest", "35:RbpMotGradeBasedDecelerationRequest49:rbp_pmp_psd_ccp__grade_based_deceleration_request35:RbpMotGradeBasedDecelerationRequest", "25:RbpPmpLongitudinalRequest37:rbp_pmp_psd_ccp__longitudinal_request25:RbpPmpLongitudinalRequest", "24:RbpPmpLongitudinalResult49:rbp_pmp_psd_ccp__longitudinalPlanner___trajectory24:RbpPmpLongitudinalResult", "14:RbpPmpManeuver32:rbp_pmp_psd_ccp__maneuver_output14:RbpPmpManeuver", "27:RbpPmpManeuver_StateService30:rbp_pmp_psd_ccp.maneuver$State30:RbpPmpManeuverState_StateEvent", "14:RbpPmpParkable40:rbp_pmp_psd_ccp__parkable_parking_spaces14:RbpPmpParkable", "18:RbpMotPathSequence30:rbp_pmp_psd_ccp__path_sequence18:RbpMotPathSequence", "20:RbpPmpPlanningResult46:rbp_pmp_psd_ccp__pathPlanner___planning_result20:RbpPmpPlanningResult", "30:RbpPmpPathPlanner_StateService33:rbp_pmp_psd_ccp.pathPlanner$State33:RbpPmpPathPlannerState_StateEvent", "21:RbpPmpPlanningRequest33:rbp_pmp_psd_ccp__planning_request21:RbpPmpPlanningRequest", "33:PmpOverallFaultEventListInterface40:rbp_pmp_psd_ccp__pmp_overall_evm_itf_out33:PmpOverallFaultEventListInterface", "6:RbpPsd45:rbp_pmp_psd_ccp__psdceo___psd_activity_output6:RbpPsd", "26:PsdFaultEventListInterface41:rbp_pmp_psd_ccp__psdceo___psd_evm_itf_out26:PsdFaultEventListInterface", "22:RbpPmpPathVisuManeuver42:rbp_pmp_psd_ccp__selected_target_path_visu22:RbpPmpPathVisuManeuver", "26:CvalinHmiSomeipRxInterface35:someip_gateway__someIpGw_FctHmi_Out26:CvalinHmiSomeipRxInterface", "24:FuncMonitorInterfaceType41:someip_gateway__someIpGw_function_monitor24:FuncMonitorInterfaceType", "25:XPerViperMotInterfaceType46:viper__bev_dyn_post_parking___bev_dynamic_objs25:XPerViperMotInterfaceType", "37:XPerViperFreeSpaceOutputInterfaceType52:viper__bev_freespace_post___bev_freespace_senderport37:XPerViperFreeSpaceOutputInterfaceType", "31:XPerViperModeDebugInterfaceType52:viper__bev_static_post___debug_viper_mode_senderport31:XPerViperModeDebugInterfaceType", "44:XPerViperBevStaticRoadStructureInterfaceType61:viper__bev_static_post___prk_static_road_structure_senderport44:XPerViperBevStaticRoadStructureInterfaceType", "33:XPerViperBlockageOddInterfaceType57:viper__drv_image_odd_post___viper_blockage_odd_senderport33:XPerViperBlockageOddInterfaceType", "28:XPerViperEnvOddInterfaceType52:viper__drv_traffic_fusion___viper_env_odd_senderport28:XPerViperEnvOddInterfaceType", "23:ViperBpuTriggerInfoType59:viper__inference_comfort___drv_viper_bpu_trigger_senderport23:ViperBpuTriggerInfoType", "39:ParkingViperFaultEventListInterfaceType76:viper__inference_comfort___parkingviper_inference_faultevent_list_senderport39:ParkingViperFaultEventListInterfaceType", "31:PsdImgsStitchedMsgInterfaceType47:viper__inference_safedrv___nrcs_imgs_stitch_msg31:PsdImgsStitchedMsgInterfaceType", "32:Iso23150ParkingSlotObjectExtType32:viper__iso_parking_slots_mot_out32:Iso23150ParkingSlotObjectExtType", "33:XPerViperParkingSlotInterfaceType29:viper__nrcs_parking_slots_mot33:XPerViperParkingSlotInterfaceType", "32:XPerViperRoadMarkerInterfaceType28:viper__nrcs_road_markers_mot32:XPerViperRoadMarkerInterfaceType", "33:XPerViperParkingSlotInterfaceType36:viper__psd_post___nrcs_parking_slots33:XPerViperParkingSlotInterfaceType", "33:XPerCameraRoadMarkerInterfaceType35:viper__psd_post___nrcs_road_markers33:XPerCameraRoadMarkerInterfaceType", "39:DrivingViperFaultEventListInterfaceType65:viper__status_collector___drivingviper_faultevent_list_senderport39:DrivingViperFaultEventListInterfaceType", "24:FuncMonitorInterfaceType48:viper__status_collector___viper_function_monitor24:FuncMonitorInterfaceType", "MTA.POSH2MTA.ClassInfo"]}]}, "imageRecorder": {"serveIp": "0.0.0.0", "servePort": 11883, "sendFinishResponse": false, "connectionIdleTime": 30, "recodeWithCpu": false, "webcam": true, "webcamList": []}, "rosbagRecorder": {"postProcess": false, "compression": "lz4", "sinkThreshold": 18000}, "sopRecorder": {"dataflows": [{"name": "aos", "sourceAddress": "tcp://**********:5555", "endpoints": [{"channel": "aos", "sinkAddress": "tcp://***********:6666", "serviceLists": ["1C:6Webcam:7Webcam0", "1C:6Camera:9FrontWide", "1C:6Camera:14NearRangeFront", "1C:6Camera:13NearRangeLeft", "1C:6Camera:13NearRangeRear", "1C:6Camera:14NearRangeRight", "ApaFctAciFaultEventListInterface.apa_fct__aci___apa_fct_aci_evm_itf_out.ApaFctAciFaultEventListInterface", "CFusasmControlOutput.apa_fct__aci___netcom_onePMcControl_out.CFusasmControlOutput", "RbpActiveFunction.apa_fct__active_function.RbpActiveFunction", "RbpApaDisplayInformationHU.apa_fct__apa_display_information_hu.RbpApaDisplayInformationHU", "ParkctlOutput.apa_fct__apa_fct_park_ctl_out.ParkctlOutput", "FctPubFuncState.apa_fct__apa_func_state_senderport.FctPubFuncState", "RbpApaHmiToCtlInfo.apa_fct__apa_hmi_to_apa_ctl_info_senderport.RbpApaHmiToCtlInfo", "RbCnApaHmi2SvsGwSignals.apa_fct__apa_hmi_to_svs_gw_output.RbCnApaHmi2SvsGwSignals", "RbpPmpInjectedParkingManeuver.apa_fct__apa_injected_parking_maneuver.RbpPmpInjectedParkingManeuver", "RbpApaParkingSpaceInfoVis.apa_fct__apa_parking_space_info_vis.RbpApaParkingSpaceInfoVis", "RbpApactlModes.apa_fct__apactl_modes.RbpApactlModes", "RbpApactlNotify.apa_fct__apactl_notify_out.RbpApactlNotify", "PersistJsonInterfaceType.apa_fct__apactl_persist_jsonsenderport.PersistJsonInterfaceType", "FunctionStateResponseType.apa_fct__apafct_function_state_response_senderport.FunctionStateResponseType", "ApaFctOverallFaultEventListInterface.apa_fct__apafct_overall_evm_itf_out.ApaFctOverallFaultEventListInterface", "RbpApaHmiPdm.apa_fct__apahmi_pdm_out.RbpApaHmiPdm", "RbpPsdHmi.apa_fct__awp_injc_target_out.RbpPsdHmi", "RbpPsdHmi.apa_fct__awp_park_target_out.RbpPsdHmi", "RbpApaInfoStatusItf.apa_fct__fct_nfm_status_output.RbpApaInfoStatusItf", "PersistJsonInterfaceType.apa_fct__manwrn_persist_jsonsenderport.PersistJsonInterfaceType", "RbpMasterctlStatusOut.apa_fct__master_ctl_status_out.RbpMasterctlStatusOut", "RbpMcaMaaPfc.apa_fct__mcamaactl_modes.RbpMcaMaaPfc", "RbpMcaMebPfc.apa_fct__mcamebctl_modes.RbpMcaMebPfc", "RbpMcaMoisPfc.apa_fct__mcamoisctl_modes.RbpMcaMoisPfc", "RbpMeasurementMode.apa_fct__measurement_mode.RbpMeasurementMode", "RbpMebControlState.apa_fct__meb_control_state.RbpMebControlState", "RbpMebMaaPdm.apa_fct__meb_maa_pdm_out.RbpMebMaaPdm", "RbpMotAllowedSenders.apa_fct__mot_allowed_senders.RbpMotAllowedSenders", "RbpMotEmergencyDecelerationRequest.apa_fct__mot_emergency_deceleration_request.RbpMotEmergencyDecelerationRequest", "RbpMotGradeBasedDecelerationRequest.apa_fct__mot_grade_based_deceleration_request.RbpMotGradeBasedDecelerationRequest", "RbpMotServiceRequestsLat.apa_fct__mot_service_request_lat.RbpMotServiceRequestsLat", "RbpMotServiceRequestsLong.apa_fct__mot_service_request_long.RbpMotServiceRequestsLong", "RbpMotSecureWhenAtStandstillLong.apa_fct__mot_vehicle_secure_request.RbpMotSecureWhenAtStandstillLong", "RbpMotctlControlState.apa_fct__motctl_control_state_out.RbpMotctlControlState", "RbpNfmOpmodes.apa_fct__nfm_Opmodes.RbpNfmOpmodes", "Message.apa_fct__output_message_fct_hmi_apa_to_shadow_facade.Message", "RbpParkTrajectoryVisu.apa_fct__park_trajectory_visu.RbpParkTrajectoryVisu", "ParkhmiOutput.apa_fct__parkhmioutputsenderport.ParkhmiOutput", "RbCnPdcHmiAudoInterface.apa_fct__pdc_hmi_audo_output.RbCnPdcHmiAudoInterface", "RbCnPdcHmiSignals.apa_fct__pdc_hmi_meb_maa_output.RbCnPdcHmiSignals", "RbpPdcPfc.apa_fct__pdcctlpf_pfc_messages.RbpPdcPfc", "RbpPmpOperatingModes.apa_fct__pmpctl_pmp_sender.RbpPmpOperatingModes", "RbpPsdPfc.apa_fct__psdctl_psd_sender.RbpPsdPfc", "RbpPmpSelectedParkingManeuver.apa_fct__selected_parking_maneuver_out.RbpPmpSelectedParkingManeuver", "RbpUssmpCnCtlItf.apa_fct__ussmp_output.RbpUssmpCnCtlItf", "RbpUssperCtl.apa_fct__ussper_output.RbpUssperCtl", "PersistJsonInterfaceType.aracom_gateway__apactl_persist_jsonsenderport.PersistJsonInterfaceType", "BswLcmStateInterfaceType.aracom_gateway__aracom_lcmstate_senderport.BswLcmStateInterfaceType", "PersistJsonInterfaceType.aracom_gateway__avm_persist_jsonsenderport.PersistJsonInterfaceType", "DiagnoseRequestInterfaceType.aracom_gateway__diag_request_senderport.DiagnoseRequestInterfaceType", "PersistJsonInterfaceType.aracom_gateway__fct_persist_jsonsenderport.PersistJsonInterfaceType", "FidReactionIdListInterfaceType.aracom_gateway__fid_reactionid_senderport.FidReactionIdListInterfaceType", "FunctionStateRequestType.aracom_gateway__function_state_request_senderport.FunctionStateRequestType", "PersistJsonInterfaceType.aracom_gateway__manwrn_persist_jsonsenderport.PersistJsonInterfaceType", "SensorXxtrinsicInterfaceType.aracom_gateway__offline_sensor_calib_parameter_senderport.SensorXxtrinsicInterfaceType", "SensorXxtrinsicInterfaceType.aracom_gateway__online_sensor_calib_parameter_senderport.SensorXxtrinsicInterfaceType", "PersistJsonInterfaceType.aracom_gateway__rbpinfragw_persist_jsonsenderport.PersistJsonInterfaceType", "PersistJsonInterfaceType.aracom_gateway__rbpinfragw_persist_jsonsenderport.PersistJsonInterfaceType", "PersistJsonInterfaceType.aracom_gateway__rbpinfragw_persist_jsonsenderport.PersistJsonInterfaceType", "SensorXxtrinsicInterfaceType.aracom_gateway__sensor_calib_parameter_senderport.SensorXxtrinsicInterfaceType", "VariantFunctionConfigInterfaceType.aracom_gateway__variant_function_config_senderport.VariantFunctionConfigInterfaceType", "VehicleIdentificationNumberInterfaceType.aracom_gateway__vehicle_identification_num_senderport.VehicleIdentificationNumberInterfaceType", "VehicleVariantInterfaceType.aracom_gateway__vehicle_variant_senderport.VehicleVariantInterfaceType", "FuncMonitorInterfaceType.camera_service__camera_function_monitor.FuncMonitorInterfaceType", "RbpUssmpCnEchoItf.datahub_gateway__datahub_RcoreUssMp_Out.RbpUssmpCnEchoItf", "DvrWorkStatusInterface.dvr_gateway__dvr_WorkStatus_Out.DvrWorkStatusInterface", "CSentryModeStatusItf.dvr_gateway__sentry_mode_aracom_status_output.CSentryModeStatusItf", "DynamicFusionToApaInterfaceType.dynamic_fusion__driving_ego_dynamic_objects_senderport.DynamicFusionToApaInterfaceType", "DynamicFusionToApaInterfaceType.dynamic_fusion__dynamic_fusion_apa_obj_list_senderport.DynamicFusionToApaInterfaceType", "FuncMonitorInterfaceType.dynamic_fusion__dynamic_fusion_function_monitor.FuncMonitorInterfaceType", "DynamicFusionObjListInterfaceType.dynamic_fusion__dynamic_fusion_obj_list_senderport.DynamicFusionObjListInterfaceType", "DynamicFusion_StateService.dynamic_fusion\\.dynamic_fusion$State.DynamicFusion_StateService", "DynamicfusionFaultEventListInterfaceType.dynamic_fusion__dynamicfusion_faultevent_list_senderport.DynamicfusionFaultEventListInterfaceType", "DynamicFusionObjListInterfaceType.dynamic_fusion__parking_local_dynamic_objects_senderport.DynamicFusionObjListInterfaceType", "Aosfsmrunnable_StateService.fct\\.aosfsmrunnable$State.Aosfsmrunnable_StateService", "Aoshpahmirunnable_StateService.fct\\.aoshpahmirunnable$State.Aoshpahmirunnable_StateService", "Aoshpahsmrunnable_StateService.fct\\.aoshpahsmrunnable$State.Aoshpahsmrunnable_StateService", "Aosractlrunnable_StateService.fct\\.aosractlrunnable$State.Aosractlrunnable_StateService", "CRACTLToRapathrecorderInterfaceType.fct__cractlto_rapathrecorder_senderport.CRACTLToRapathrecorderInterfaceType", "CvaloutHmiSomeipTxInterface.fct__cvalout_hmi_someip_tx.CvaloutHmiSomeipTxInterface", "CvaloutSensorStAllInterface.fct__cvalout_sensor_status_all_senderport.CvaloutSensorStAllInterface", "FuncMonitorInterfaceType.fct__fct_function_monitor.FuncMonitorInterfaceType", "FunctionStateResponseType.fct__fct_function_state_response_senderport.FunctionStateResponseType", "CFCTOverallStateType.fct__fct_overall_state_senderport.CFCTOverallStateType", "CFCTSOCOnlyFunctionStatesInterface.fct__fct_soc_to_mcu_function_state_senderport.CFCTSOCOnlyFunctionStatesInterface", "CFCTDrivingToVMCOutputInterfaceType.fct__fdmtovmcsenderport.CFCTDrivingToVMCOutputInterfaceType", "FsmBehaviorsInterfaceType.fct__fsm_behaviors_senderport.FsmBehaviorsInterfaceType", "CHmismOutput.fct__hmismoutputsenderport.CHmismOutput", "CHpaToApaInfoOutput.fct__hpa_to_apa_fct_info_out.CHpaToApaInfoOutput", "CHpaToApaOpModeOutput.fct__hpa_to_apa_fct_opmode_out.CHpaToApaOpModeOutput", "PersistJsonInterfaceType.fct__persistjsonsenderport.PersistJsonInterfaceType", "RaCtlOutput.fct__ractloutputsenderport.RaCtlOutput", "CHUtoFctRequestInterfaceType.host_gateway__chuto_fct_request_senderport.CHUtoFctRequestInterfaceType", "CvalinHostInterface.host_gateway__cvalin_hmi_senderport.CvalinHostInterface", "ImuCorrectionInterface.imu__vmps_ImuCorrectionData_Out.ImuCorrectionInterface", "ImuRawInterface.imu__vmps_ImuRawData_Out.ImuRawInterface", "CImuRbvInterface.imu__vmps_ImuRbvData_Out.CImuRbvInterface", "InsInterface.imu__vmps_InsData_Out.InsInterface", "XPerMapMatchingInterfaceType.loc_map_proxy__map_matching_result_senderport.XPerMapMatchingInterfaceType", "XPerGlobalLocResultInterfaceType.loc_map_proxy__xper_global_loc_result_senderport.XPerGlobalLocResultInterfaceType", "FuncMonitorInterfaceType.local_mapping__localmapping_function_monitor.FuncMonitorInterfaceType", "LocalMapInterfaceType.local_mapping__xper_local_map_result_senderport.LocalMapInterfaceType", "FuncMonitorInterfaceType.local_msf__localmsf_function_monitor.FuncMonitorInterfaceType", "XOdometryResultInterfaceType.local_msf__xodometry_result_senderport.XOdometryResultInterfaceType", "XOdometryResult2McuInterfaceType.local_msf__xodometry_result_to_mcu_senderport.XOdometryResult2McuInterfaceType", "XPerLocalLocResultInterfaceType.local_msf__xper_local_loc_result_senderport.XPerLocalLocResultInterfaceType", "VariantConf.master_parameter_gateway__variant_conf.VariantConf", "FuncMonitorInterfaceType.motion_control__motion_function_monitor.FuncMonitorInterfaceType", "MotionCtlFaultEventListInterfaceType.motion_control__motionctl_faultevent_list_senderport.MotionCtlFaultEventListInterfaceType", "VmcControlDebugInfo.motion_control__vmc_control_debuginfo_senderport.VmcControlDebugInfo", "VmcOutputInterfaceType.motion_control__vmc_output_senderport.VmcOutputInterfaceType", "CHVMOutputInterface.netcom_gateway__netcom_HvmData_Out.CHVMOutputInterface", "CFCTMCUOnlytoSOCFunctionStateInterface.netcom_gateway__netcom_McuToSocFunctionState_Out.CFCTMCUOnlytoSOCFunctionStateInterface", "RbpUss3rdOutPutItf.netcom_gateway__netcom_RcoreUss3rd_Out.RbpUss3rdOutPutItf", "RbpUssmpCnEchoItf.netcom_gateway__netcom_RcoreUssMp_Out.RbpUssmpCnEchoItf", "CvalinDrivingParkingBodyInterfaceType.netcom_gateway__netcom_VehicleBodyData_Out.CvalinDrivingParkingBodyInterfaceType", "CvalinDrivingParkingChassisInterfaceType.netcom_gateway__netcom_VehicleChassisData_Out.CvalinDrivingParkingChassisInterfaceType", "CvalinDrivingParkingSpecificInterfaceType.netcom_gateway__netcom_VehicleSpecificData_Out.CvalinDrivingParkingSpecificInterfaceType", "OfflineCalibResultInterfaceType.online_calibration__online_calib_result_senderport.OfflineCalibResultInterfaceType", "PnPDebugText.pnp__hpa_env_info.PnPDebugText", "PnPDebugText.pnp__hpa_kpi_info.PnPDebugText", "PnPDebugText.pnp__hpa_lat_info.PnPDebugText", "PnPDebugText.pnp__hpa_lon_info.PnPDebugText", "PnPDebugText.pnp__hpa_main_info.PnPDebugText", "PnPDebugText.pnp__hpa_mkw_info.PnPDebugText", "PlanningRuntimeKeyInfo.pnp__planning_runtime_key_info.PlanningRuntimeKeyInfo", "PlanningSafetyToFctInterfaceType.pnp__planning_safety_to_fct_senderport.PlanningSafetyToFctInterfaceType", "PlanningToControlInterfaceType.pnp__planning_to_control_senderport.PlanningToControlInterfaceType", "PlanningToFctInterfaceType.pnp__planning_to_fct_senderport.PlanningToFctInterfaceType", "PnPFaultEventListInterfaceType.pnp__pnp_faultevent_list_senderport.PnPFaultEventListInterfaceType", "PredDebugInfoOutputInterfaceType.pnp__prediction_debug_info_senderport.PredDebugInfoOutputInterfaceType", "PredictionInterfaceType.pnp__prediction_senderport.PredictionInterfaceType", "PredictionSet.pnp__prediction_set_output.PredictionSet", "ReferenceLineOutputInterfaceType.pnp__reference_line_senderport.ReferenceLineOutputInterfaceType", "FuncMonitorInterfaceType.ra_path_recorder__ra_path_recorder_function_monitor.FuncMonitorInterfaceType", "RATrajectoryInfoInterfaceType.ra_path_recorder__ra_trajectory_info_senderport.RATrajectoryInfoInterfaceType", "RbpVisOutPutItf.rbp_avm_dummy_proxy__avm_combined_val_output.RbpVisOutPutItf", "PersistJsonInterfaceType.rbp_avm_dummy_proxy__avm_persist_jsonsenderport.PersistJsonInterfaceType", "EnvPerOverallFaultEventListInterface.rbp_env_per__envper_overall_evm_itf_out.EnvPerOverallFaultEventListInterface", "RbpUssPerAll_StateService.rbp_env_per\\.rbp_uss_per_all$State.RbpUssPerAll_StateService", "RbpNfmContours.rbp_env_per__static_fusion___contours.RbpNfmContours", "NfmFaultEventListInterface.rbp_env_per__static_fusion___env_per_nfm_evm_itf_out.NfmFaultEventListInterface", "RbpNfmMoe.rbp_env_per__static_fusion___moe.RbpNfmMoe", "RbpStaticFusionDebugItf.rbp_env_per__static_fusion___static_fusion_debug_out.RbpStaticFusionDebugItf", "RbpStaticFusionPdmItf.rbp_env_per__static_fusion___static_fusion_pdm_out.RbpStaticFusionPdmItf", "RbpStaticFusionStateDebugItf.rbp_env_per__static_fusion___static_fusion_state_out.RbpStaticFusionStateDebugItf", "RbpNfmUssOnlyContours.rbp_env_per__static_fusion___ussonly_contours.RbpNfmUssOnlyContours", "RbpStaticFusion_StateService.rbp_env_per\\.static_fusion$State.RbpStaticFusionState_StateEvent", "RbpDegradation.rbp_env_per__ussper_degradation_port.RbpDegradation", "RbpDetections.rbp_env_per__ussper_detections_port.RbpDetections", "RbpUssperEvm.rbp_env_per__ussper_evmif_port.RbpUssperEvm", "RbpFeatures.rbp_env_per__ussper_features_port.RbpFeatures", "RbpFreespace.rbp_env_per__ussper_freespace_port.RbpFreespace", "AVMFaultEventListInterface.rbp_infra_gw__avm_evm_itf_out.AVMFaultEventListInterface", "RbpAvmPdm.rbp_infra_gw__avm_pdm_out.RbpAvmPdm", "RbpUssmpEchoItf.rbp_infra_gw__common_echo_if.RbpUssmpEchoItf", "RbpVisInputItf.rbp_infra_gw__infra_avm_combined_val_output.RbpVisInputItf", "RbpInjectedManeuverUser.rbp_infra_gw__injected_maneuver_user.RbpInjectedManeuverUser", "RbpMcaBusData.rbp_infra_gw__mca_vehicle_signals.RbpMcaBusData", "RbpMotFromExternalForTarSteeringAngleVariant.rbp_infra_gw__mot_if_steeringangle_variant.RbpMotFromExternalForTarSteeringAngleVariant", "RbpMotFromExternalForTarSxVariant.rbp_infra_gw__mot_if_sx_variant.RbpMotFromExternalForTarSxVariant", "RbpPmpVal.rbp_infra_gw__pmp_if.RbpPmpVal", "RbpInfraValin.rbp_infra_gw__rbp_infra_valin_output.RbpInfraValin", "RbpXPerViperInternalEnvOddItf.rbp_infra_gw__rbp_internal_xper_env_odd_output.RbpXPerViperInternalEnvOddItf", "PersistJsonInterfaceType.rbp_infra_gw__rbpinfragw_persist_jsonsenderport.PersistJsonInterfaceType", "CvaloutADCCParkingFuncInfoADASstrtSvsFuncStatusInterface.rbp_infra_gw__svs_func_status_output.CvaloutADCCParkingFuncInfoADASstrtSvsFuncStatusInterface", "RbCnGwApaFctSignals.rbp_infra_gw__svs_gw_pdc_signals_output.RbCnGwApaFctSignals", "RbpNfmUssOnlyFromMCUInfoItf.rbp_infra_gw__uss_info_from_mcu_out.RbpNfmUssOnlyFromMCUInfoItf", "RbpUssStatusItf.rbp_infra_gw__uss_status_output.RbpUssStatusItf", "RbpUssperVal.rbp_infra_gw__ussper_if.RbpUssperVal", "RbCnVisRefGwVehicleSignals.rbp_infra_gw__visrefgw_vehicle_signals_cpj.RbCnVisRefGwVehicleSignals", "RbpVmeVal.rbp_infra_gw__vme_if.RbpVmeVal", "RbpApaHmiPdm.rbp_infra__apahmi_pdm_read.RbpApaHmiPdm", "RbpAvmPdm.rbp_infra__avm_pdm_read.RbpAvmPdm", "RbpCsiPdmCnRoads.rbp_infra__csi_pdm_read.RbpCsiPdmCnRoads", "RbpAVMFlCl.rbp_infra__failure_status_info_port.RbpAVMFlCl", "RbpNfmFlCl.rbp_infra__flcl_nfm.RbpNfmFlCl", "RbpPdcFlclItf.rbp_infra__flcl_pdc.RbpPdcFlclItf", "RbpPsdFlCl.rbp_infra__flcl_psd.RbpPsdFlCl", "RbpUssperFlcl.rbp_infra__flcl_ussper.RbpUssperFlcl", "FidReactionIdListInterfaceType.rbp_infra__infra_fid_reactionid_out.FidReactionIdListInterfaceType", "RbpInfraRMDebugOutput.rbp_infra__infra_rm_debug_info.RbpInfraRMDebugOutput", "RbpMcaConfig.rbp_infra__mca_config.RbpMcaConfig", "RbpMebMaaPdm.rbp_infra__meb_maa_pdm_read.RbpMebMaaPdm", "RbpMotCnConfig.rbp_infra__mot_cn_config.RbpMotCnConfig", "RbpMotConfig.rbp_infra__mot_config.RbpMotConfig", "RbpNfmConfig.rbp_infra__nfm_config.RbpNfmConfig", "RbpOnePCnConanVersionConfig.rbp_infra__onp_conan_version_out.RbpOnePCnConanVersionConfig", "ParkingFaultEventListInterfaceType.rbp_infra__parking_falultevent_list_senderport.ParkingFaultEventListInterfaceType", "PersistJsonInterfaceType.rbp_infra__pdm_persist_jsonsenderport.PersistJsonInterfaceType", "PersistJsonInterfaceType.rbp_infra__pdm_persist_jsonsenderport.PersistJsonInterfaceType", "PersistJsonInterfaceType.rbp_infra__pdm_persist_jsonsenderport.PersistJsonInterfaceType", "RbpPfcConfig.rbp_infra__pfc_config.RbpPfcConfig", "RbpPmpConfig.rbp_infra__pmp_config.RbpPmpConfig", "RbpPmpHmiConfig.rbp_infra__pmphmi_config.RbpPmpHmiConfig", "RbpPsdConfig.rbp_infra__psd_config.RbpPsdConfig", "RbpStaticFusionPdmItf.rbp_infra__static_fusion_pdm_read.RbpStaticFusionPdmItf", "RbpUssperConfig.rbp_infra__ussper_config.RbpUssperConfig", "RbpVmeSteeringConversion.rbp_infra__vhm___steering_conversion_itf.RbpVmeSteeringConversion", "RbpVmeDebugInfoItf.rbp_infra__vhm___vme_debug_info.RbpVmeDebugInfoItf", "VmeFaultEventListInterface.rbp_infra__vhm___vme_evm_itf_out.VmeFaultEventListInterface", "RbpVmePdm.rbp_infra__vhm___vme_pdm_out.RbpVmePdm", "RbpExternalVhmAbstBufferPub.rbp_infra__vhm_abst___external_vhm_abst_buffer_output.RbpExternalVhmAbstBufferPub", "RbpExternalVhmAbstPub.rbp_infra__vhm_abst___external_vhm_abst_output.RbpExternalVhmAbstPub", "RbpVhmAbstBuffer.rbp_infra__vhm_abst___vhm_abst_buffer_output.RbpVhmAbstBuffer", "RbpVhmAbst.rbp_infra__vhm_abst___vhm_abst_output.RbpVhmAbst", "VmeFaultEventListInterface.rbp_infra__vhm_abst___vme_evm_itf_out.VmeFaultEventListInterface", "RbpVmeConfig.rbp_infra__vme_config.RbpVmeConfig", "RbpVmePdm.rbp_infra__vme_pdm_read.RbpVmePdm", "RbpMotExternalAxLimitRequest.rbp_mca__mca_maa_ax_limit_request_output_port.RbpMotExternalAxLimitRequest", "RbpMcaMaaItf.rbp_mca__mca_maa_output_port.RbpMcaMaaItf", "RbpMcaMebBrakeState.rbp_mca__mca_meb_brake_state_output_port.RbpMcaMebBrakeState", "RbpMcaMebItf.rbp_mca__mca_meb_output_port.RbpMcaMebItf", "McaOverallFaultEventListInterface.rbp_mca__mca_overall_evm_itf_out.McaOverallFaultEventListInterface", "RbpMebDebug.rbp_mca__meb_debug_output.RbpMebDebug", "RbpMotEmergencyDecelerationRequest.rbp_mca__mot_emergency_deceleration_request_output_port.RbpMotEmergencyDecelerationRequest", "McaPdcFaultEventListInterface.rbp_mca__pdc_main___mca_pdc_evm_itf_out.McaPdcFaultEventListInterface", "RbpPdcCollisionAreaItf.rbp_mca__pdc_main___pdc_collision_area_out.RbpPdcCollisionAreaItf", "RbpPdcOutputItf.rbp_mca__pdc_main___pdc_out.RbpPdcOutputItf", "MotCoreCn_StateService.rbp_mot_core_instance\\.mot_core_cn$State.MotCoreCnState_StateEvent", "RbpMotDesiredDrivingDirection.rbp_mot_core_instance__mot_desired_driving_direction.RbpMotDesiredDrivingDirection", "RbpMotEmergencyDecelerationRequest.rbp_mot_core_instance__mot_emergency_deceleration_request.RbpMotEmergencyDecelerationRequest", "MotFaultEventListInterface.rbp_mot_core_instance__mot_evm_itf_out.MotFaultEventListInterface", "RbpMotGradeBasedDecelerationRequest.rbp_mot_core_instance__mot_grade_based_deceleration_request.RbpMotGradeBasedDecelerationRequest", "RbpMotSteeringInStandstillActive.rbp_mot_core_instance__mot_internal_steering_in_standstill_active_out.RbpMotSteeringInStandstillActive", "RbpMotLongitudinalControllerRequest.rbp_mot_core_instance__mot_long_ax_controller_request.RbpMotLongitudinalControllerRequest", "RbpMotLongitudinalControllerRequest.rbp_mot_core_instance__mot_long_sx_controller_request.RbpMotLongitudinalControllerRequest", "RbpMotManeuverFeedbackLat.rbp_mot_core_instance__mot_maneuver_feedback_lat.RbpMotManeuverFeedbackLat", "RbpMotManeuverFeedbackLong.rbp_mot_core_instance__mot_maneuver_feedback_long.RbpMotManeuverFeedbackLong", "RbpMotPathProviderFeedback.rbp_mot_core_instance__mot_path_provider_feedback.RbpMotPathProviderFeedback", "RbpMotSecureWhenAtStandstillLong.rbp_mot_core_instance__mot_secure_when_at_standstill_long_out.RbpMotSecureWhenAtStandstillLong", "RbpMotSelectedPathSenderFeedback.rbp_mot_core_instance__mot_selected_path_sender_feedback.RbpMotSelectedPathSenderFeedback", "RbpMotServiceStatusFeedbackLat.rbp_mot_core_instance__mot_service_status_feedback_lat.RbpMotServiceStatusFeedbackLat", "RbpMotServiceStatusFeedbackLong.rbp_mot_core_instance__mot_service_status_feedback_long.RbpMotServiceStatusFeedbackLong", "RbpMotSteeringActuatorRequest.rbp_mot_core_instance__mot_steering_actuator_request.RbpMotSteeringActuatorRequest", "RbpMotTarAx.rbp_mot_core_instance__mot_tar_ax.RbpMotTarAx", "RbpMotTarSteeringAngle.rbp_mot_core_instance__mot_tar_steering_angle_front.RbpMotTarSteeringAngle", "RbpMotTarSteeringAngle.rbp_mot_core_instance__mot_tar_steering_angle_rear.RbpMotTarSteeringAngle", "RbpMotTarSx.rbp_mot_core_instance__mot_tar_sx.RbpMotTarSx", "RbpMotUnexpectedEventsFeedbackLat.rbp_mot_core_instance__mot_unexpected_events_feedback_lat.RbpMotUnexpectedEventsFeedbackLat", "RbpMotUnexpectedEventsFeedbackLong.rbp_mot_core_instance__mot_unexpected_events_feedback_long.RbpMotUnexpectedEventsFeedbackLong", "RbpCsiContext.rbp_pmp_psd_ccp__csire___csi_context_out.RbpCsiContext", "RbpCsiPdmCnRoads.rbp_pmp_psd_ccp__csire___csi_pdm_out.RbpCsiPdmCnRoads", "RbpCsiRoads.rbp_pmp_psd_ccp__csire___roads.RbpCsiRoads", "RbpMotEmergencyDecelerationRequest.rbp_pmp_psd_ccp__emergency_deceleration_request.RbpMotEmergencyDecelerationRequest", "RbpMotGradeBasedDecelerationRequest.rbp_pmp_psd_ccp__grade_based_deceleration_request.RbpMotGradeBasedDecelerationRequest", "RbpPmpLongitudinalRequest.rbp_pmp_psd_ccp__longitudinal_request.RbpPmpLongitudinalRequest", "RbpPmpLongitudinalResult.rbp_pmp_psd_ccp__longitudinalPlanner___trajectory.RbpPmpLongitudinalResult", "RbpPmpManeuver.rbp_pmp_psd_ccp__maneuver_output.RbpPmpManeuver", "RbpPmpManeuver_StateService.rbp_pmp_psd_ccp\\.maneuver$State.RbpPmpManeuverState_StateEvent", "RbpPmpParkable.rbp_pmp_psd_ccp__parkable_parking_spaces.RbpPmpParkable", "RbpMotPathSequence.rbp_pmp_psd_ccp__path_sequence.RbpMotPathSequence", "RbpPmpPlanningResult.rbp_pmp_psd_ccp__pathPlanner___planning_result.RbpPmpPlanningResult", "RbpPmpPathPlanner_StateService.rbp_pmp_psd_ccp\\.pathPlanner$State.RbpPmpPathPlannerState_StateEvent", "RbpPmpPlanningRequest.rbp_pmp_psd_ccp__planning_request.RbpPmpPlanningRequest", "PmpOverallFaultEventListInterface.rbp_pmp_psd_ccp__pmp_overall_evm_itf_out.PmpOverallFaultEventListInterface", "RbpPsd.rbp_pmp_psd_ccp__psdceo___psd_activity_output.RbpPsd", "PsdFaultEventListInterface.rbp_pmp_psd_ccp__psdceo___psd_evm_itf_out.PsdFaultEventListInterface", "RbpPmpPathVisuManeuver.rbp_pmp_psd_ccp__selected_target_path_visu.RbpPmpPathVisuManeuver", "CvalinHmiSomeipRxInterface.someip_gateway__someIpGw_FctHmi_Out.CvalinHmiSomeipRxInterface", "FuncMonitorInterfaceType.someip_gateway__someIpGw_function_monitor.FuncMonitorInterfaceType", "XPerViperMotInterfaceType.viper__bev_dyn_post_parking___bev_dynamic_objs.XPerViperMotInterfaceType", "XPerViperFreeSpaceOutputInterfaceType.viper__bev_freespace_post___bev_freespace_senderport.XPerViperFreeSpaceOutputInterfaceType", "XPerViperModeDebugInterfaceType.viper__bev_static_post___debug_viper_mode_senderport.XPerViperModeDebugInterfaceType", "XPerViperBevStaticRoadStructureInterfaceType.viper__bev_static_post___prk_static_road_structure_senderport.XPerViperBevStaticRoadStructureInterfaceType", "XPerViperBlockageOddInterfaceType.viper__drv_image_odd_post___viper_blockage_odd_senderport.XPerViperBlockageOddInterfaceType", "XPerViperEnvOddInterfaceType.viper__drv_traffic_fusion___viper_env_odd_senderport.XPerViperEnvOddInterfaceType", "ViperBpuTriggerInfoType.viper__inference_comfort___drv_viper_bpu_trigger_senderport.ViperBpuTriggerInfoType", "ParkingViperFaultEventListInterfaceType.viper__inference_comfort___parkingviper_inference_faultevent_list_senderport.ParkingViperFaultEventListInterfaceType", "PsdImgsStitchedMsgInterfaceType.viper__inference_safedrv___nrcs_imgs_stitch_msg.PsdImgsStitchedMsgInterfaceType", "Iso23150ParkingSlotObjectExtType.viper__iso_parking_slots_mot_out.Iso23150ParkingSlotObjectExtType", "XPerViperParkingSlotInterfaceType.viper__nrcs_parking_slots_mot.XPerViperParkingSlotInterfaceType", "XPerViperRoadMarkerInterfaceType.viper__nrcs_road_markers_mot.XPerViperRoadMarkerInterfaceType", "XPerViperParkingSlotInterfaceType.viper__psd_post___nrcs_parking_slots.XPerViperParkingSlotInterfaceType", "XPerCameraRoadMarkerInterfaceType.viper__psd_post___nrcs_road_markers.XPerCameraRoadMarkerInterfaceType", "DrivingViperFaultEventListInterfaceType.viper__status_collector___drivingviper_faultevent_list_senderport.DrivingViperFaultEventListInterfaceType", "FuncMonitorInterfaceType.viper__status_collector___viper_function_monitor.FuncMonitorInterfaceType", "MTA.POSH2MTA.ClassInfo"]}]}]}, "socFileDump": [{"hostInfo": {"address": "**********", "user": "root", "authType": "none"}, "fileTasks": [{"from": "/ota/output/version_info.json", "to": "asw_version_info.json", "cache": true, "uniformPrefix": true}, {"from": "/opt/buildinfo.txt", "to": "bsw_version_info.txt", "cache": true, "uniformPrefix": true}, {"from": "/data/calib", "to": "calibration_parameter", "cache": true, "zip": true, "uniformPrefix": true}]}]}