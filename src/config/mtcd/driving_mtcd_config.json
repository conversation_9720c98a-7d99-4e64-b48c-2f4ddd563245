{"imageFormat": "jpeg", "serveIp": "0.0.0.0", "servePort": 50000, "recorderList": ["cache", "rosbag", "image"], "recordDir": "/tmp", "vin": "HIL", "recordMode": "trigger", "timeBeforeTrigger": 30, "timeAfterTrigger": 10, "startWithoutRecording": true, "autoSplit": false, "autoSplitInterval": 300, "acquisitionType": "driving", "functionType": "bench_parking_hpa_training", "platform": "J6E", "tenant": "<PERSON><PERSON>", "proxy": {"address": "***************", "port": 50000}, "cacheRecorder": {"cacheSize": 10000000, "latchedPublishInterval": 20, "reopenDataflow": true, "latchedServices": ["REGEX:.*camera_provider__camera_intrinsic_set_senderport.*", "REGEX:.*camera_provider__camera_error_info_senderport.*", "REGEX:.*cross_gateway__sensor_calib_parameter_senderport.*", "REGEX:.*cross_gateway__variant_function_config_senderport.*", "REGEX:.*cross_gateway__camera_error_info_senderport.*", "REGEX:.*aracom_gateway__aracom_lcmstate_senderport.*", "REGEX:.*t20__runnable_main_proc_m_port_main_proc_out.*"], "dataflows": [{"comment": "", "sourceIp": "************", "sinkIp": "**************", "serviceList": ["MTA.POSH2MTA.ClassInfo", "39:DaCorePerInputConnectedEnvironmentInput62:activity_spp__spp_mops_video_objects_connected_environment_out39:DaCorePerInputConnectedEnvironmentInput", "24:PersistJsonInterfaceType42:aracom_gateway__fct_persist_jsonsenderport24:PersistJsonInterfaceType", "28:SensorXxtrinsicInterfaceType57:aracom_gateway__offline_sensor_calib_parameter_senderport28:SensorXxtrinsicInterfaceType", "28:SensorXxtrinsicInterfaceType56:aracom_gateway__online_sensor_calib_parameter_senderport28:SensorXxtrinsicInterfaceType", "28:SensorXxtrinsicInterfaceType49:aracom_gateway__sensor_calib_parameter_senderport28:SensorXxtrinsicInterfaceType", "34:VariantFunctionConfigInterfaceType50:aracom_gateway__variant_function_config_senderport34:VariantFunctionConfigInterfaceType", "36:CameraSrvFaultEventListInterfaceType52:camera_service__camerasrv_faultevent_list_senderport36:CameraSrvFaultEventListInterfaceType", "10:CSlaOutput31:dummy_sky__portslaoutsenderport10:CSlaOutput", "34:VariantFunctionConfigInterfaceType45:dummy_sky__variant_function_config_senderport34:VariantFunctionConfigInterfaceType", "31:DynamicFusionToApaInterfaceType54:dynamic_fusion__driving_ego_dynamic_objects_senderport31:DynamicFusionToApaInterfaceType", "31:DynamicFusionToApaInterfaceType54:dynamic_fusion__dynamic_fusion_apa_obj_list_senderport31:DynamicFusionToApaInterfaceType", "33:DynamicFusionObjListInterfaceType50:dynamic_fusion__dynamic_fusion_obj_list_senderport33:DynamicFusionObjListInterfaceType", "26:DynamicFusion_StateService35:dynamic_fusion.dynamic_fusion$State29:DynamicFusionState_StateEvent", "40:DynamicfusionFaultEventListInterfaceType56:dynamic_fusion__dynamicfusion_faultevent_list_senderport40:DynamicfusionFaultEventListInterfaceType", "41:DaCorePerOutputConnectedEnvironmentOutput61:env_model1v__per_eim_tpr_runnable_m_connected_environment_out41:DaCorePerOutputConnectedEnvironmentOutput", "34:DaCorePerOutputFusedObjectsDynamic52:env_model1v__per_eim_tpr_runnable_m_fus_obj_port_out34:DaCorePerOutputFusedObjectsDynamic", "39:DaCorePerInputOutputImageDerivedObjects68:env_model1v__per_eim_tpr_runnable_m_fused_image_derived_obj_port_out39:DaCorePerInputOutputImageDerivedObjects", "39:DaCorePerInputOutputImageDerivedObjects73:env_model1v__per_eim_tpr_runnable_m_video_only_image_derived_obj_port_out39:DaCorePerInputOutputImageDerivedObjects", "27:DaCorePerOutputCourseLimits54:env_model1v__per_rep_runnable_m_course_limits_port_out27:DaCorePerOutputCourseLimits", "38:DaCorePerOutputMapRoadGeometryAndLanes68:env_model1v__per_rep_runnable_m_map_road_geometry_and_lanes_port_out38:DaCorePerOutputMapRoadGeometryAndLanes", "24:DaCorePerOutputRoadLines46:env_model1v__per_rep_runnable_m_road_lines_out24:DaCorePerOutputRoadLines", "34:DaCorePerOutputRoadLinesPolynomial59:env_model1v__per_rep_runnable_m_road_lines_polynom_port_out34:DaCorePerOutputRoadLinesPolynomial", "37:DaCorePerOutputSensorInputUncertainty60:env_model1v__per_rep_runnable_m_sensor_input_uncertainty_out37:DaCorePerOutputSensorInputUncertainty", "35:DaCorePerOutputTraceOutputInterface46:env_model1v__per_rep_runnable_m_trace_port_out35:DaCorePerOutputTraceOutputInterface", "38:DaCorePerOutputRoadSignsRepresentation67:env_model1v__per_rep_runnable_m_video_road_sign_properties_port_out38:DaCorePerOutputRoadSignsRepresentation", "39:DaCorePerInputOutputImageDerivedObjects71:env_model1v__per_track2_track_runnable_m_image_derived_objects_port_out39:DaCorePerInputOutputImageDerivedObjects", "34:DaCorePerOutputFusedObjectsDynamic40:env_model1v__sync_env_m_trigger_port_out34:DaCorePerOutputFusedObjectsDynamic", "27:Aosfsmrunnable_StateService24:fct.aosfsmrunnable$State30:AosfsmrunnableState_StateEvent", "35:CRACTLToRapathrecorderInterfaceType39:fct__cractlto_rapathrecorder_senderport35:CRACTLToRapathrecorderInterfaceType", "27:CvaloutHmiSomeipTxInterface26:fct__cvalout_hmi_someip_tx27:CvaloutHmiSomeipTxInterface", "25:FunctionStateResponseType43:fct__fct_function_state_response_senderport25:FunctionStateResponseType", "20:CFCTOverallStateType33:fct__fct_overall_state_senderport20:CFCTOverallStateType", "34:CFCTSOCOnlyFunctionStatesInterface45:fct__fct_soc_to_mcu_function_state_senderport34:CFCTSOCOnlyFunctionStatesInterface", "35:CFCTDrivingToVMCOutputInterfaceType23:fct__fdmtovmcsenderport35:CFCTDrivingToVMCOutputInterfaceType", "25:FsmBehaviorsInterfaceType29:fct__fsm_behaviors_senderport25:FsmBehaviorsInterfaceType", "12:CHmismOutput26:fct__hmismoutputsenderport12:CHmismOutput", "21:CMdaPathMngrReqOutput31:fct__mda_pathmngrreq_senderport21:CMdaPathMngrReqOutput", "24:CMdaPathOperateInterface35:fct__mda_pathoperateinfo_senderport24:CMdaPathOperateInterface", "22:CMdaWorkModeInfoOutput32:fct__mda_workmodeinfo_senderport22:CMdaWorkModeInfoOutput", "26:CPathMngrResponseInterface59:fct__pathMetaDataMgr___cfct_map_response_service_senderport26:CPathMngrResponseInterface", "25:AmbEngOutputParameterstst28:gnss__ambengoutputparameters25:AmbEngOutputParameterstst", "24:CdHdlOutputParameterstst27:gnss__cdhdloutputparameters24:CdHdlOutputParameterstst", "26:NavCoreOutputParameterstst29:gnss__navcoreoutputparameters26:NavCoreOutputParameterstst", "31:VmpsFaultEventListInterfaceType37:gnss__vmps_faultevent_list_senderport31:VmpsFaultEventListInterfaceType", "28:CHUtoFctRequestInterfaceType42:host_gateway__chuto_fct_request_senderport28:CHUtoFctRequestInterfaceType", "31:VmpsFaultEventListInterfaceType58:imu_calibration__imucalibration_faultevent_list_senderport31:VmpsFaultEventListInterfaceType", "25:HnrEngOutputParameterstst27:imu__hnrengoutputparameters25:HnrEngOutputParameterstst", "29:ImuSigCdnrOutputParameterstst31:imu__imusigcdnroutputparameters29:ImuSigCdnrOutputParameterstst", "13:GNSSInterface22:imu__vmps_GnssData_Out13:GNSSInterface", "22:ImuCorrectionInterface31:imu__vmps_ImuCorrectionData_Out22:ImuCorrectionInterface", "15:ImuRawInterface24:imu__vmps_ImuRawData_Out15:ImuRawInterface", "16:CImuRbvInterface24:imu__vmps_ImuRbvData_Out16:CImuRbvInterface", "12:InsInterface21:imu__vmps_InsData_Out12:InsInterface", "25:CustOutMidTrimSFIntMsgtst20:imu__vmps_IntMsg_Out25:CustOutMidTrimSFIntMsgtst", "33:CvaloutHmiImuRpcSomeipTxInterface35:imu__vmps_VMPSINSInfoSrv_senderport33:CvaloutHmiImuRpcSomeipTxInterface", "38:LocmapproxyFaultEventListInterfaceType46:loc_map_proxy__gmsf_faultevent_list_senderport38:LocmapproxyFaultEventListInterfaceType", "38:LocmapproxyFaultEventListInterfaceType54:loc_map_proxy__map_matching_faultevent_list_senderport38:LocmapproxyFaultEventListInterfaceType", "28:XPerMapMatchingInterfaceType45:loc_map_proxy__map_matching_result_senderport28:XPerMapMatchingInterfaceType", "31:HDMapDataHighSpeedInterfaceType42:loc_map_proxy__map_srv_hdmap_hs_senderport31:HDMapDataHighSpeedInterfaceType", "30:HDMapDataLowSpeedInterfaceType42:loc_map_proxy__map_srv_hdmap_ls_senderport30:HDMapDataLowSpeedInterfaceType", "30:HDMapDataMidSpeedInterfaceType42:loc_map_proxy__map_srv_hdmap_ms_senderport30:HDMapDataMidSpeedInterfaceType", "38:LocmapproxyFaultEventListInterfaceType54:loc_map_proxy__mm_pre_viper_faultevent_list_senderport38:LocmapproxyFaultEventListInterfaceType", "32:XPerGlobalLocResultInterfaceType48:loc_map_proxy__xper_global_loc_result_senderport32:XPerGlobalLocResultInterfaceType", "34:XPerGlobalLocToMapSrvInterfaceType51:loc_map_proxy__xper_global_loc_to_mapsrv_senderport34:XPerGlobalLocToMapSrvInterfaceType", "21:LocalMapInterfaceType47:local_mapping__xper_local_map_result_senderport21:LocalMapInterfaceType", "13:GNSSInterface26:local_msf__gnss_senderport13:GNSSInterface", "12:InsInterface25:local_msf__ins_senderport12:InsInterface", "33:LocmsfFaultEventListInterfaceType46:local_msf__localmsf_faultevent_list_senderport33:LocmsfFaultEventListInterfaceType", "33:LocmsfFaultEventListInterfaceType45:local_msf__perodom_faultevent_list_senderport33:LocmsfFaultEventListInterfaceType", "28:XOdometryResultInterfaceType38:local_msf__xodometry_result_senderport28:XOdometryResultInterfaceType", "31:XPerLocalLocResultInterfaceType43:local_msf__xper_local_loc_result_senderport31:XPerLocalLocResultInterfaceType", "36:StaticFusionRoadInterfaceL2DebugType57:map_enhancement__static_fusion_l2funcion_debug_senderport36:StaticFusionRoadInterfaceL2DebugType", "29:StaticFusionRoadInterfaceType46:map_enhancement__static_fusion_road_senderport29:StaticFusionRoadInterfaceType", "24:BaseMapInfoInterfaceType32:mapsrv__base_map_info_senderport24:BaseMapInfoInterfaceType", "27:ManuverMapInfoInterfaceType36:mapsrv__guidance___maneuver_map_info27:ManuverMapInfoInterfaceType", "31:SdpMapGuidanceInfoInterfaceType51:mapsrv__guidance___sdp_map_guidance_info_senderport31:SdpMapGuidanceInfoInterfaceType", "23:LocMapInfoInterfaceType31:mapsrv__loc_map_info_senderport23:LocMapInfoInterfaceType", "26:OriginMapInfoInterfaceType34:mapsrv__origin_map_info_senderport26:OriginMapInfoInterfaceType", "28:PlanningMapInfoInterfaceType36:mapsrv__planning_map_info_senderport28:PlanningMapInfoInterfaceType", "30:SdpMapHorizonInfoInterfaceType39:mapsrv__sdp_map_horizon_info_senderport30:SdpMapHorizonInfoInterfaceType", "14:AcfPlannerConf42:master_parameter_gateway__acf_planner_conf14:AcfPlannerConf", "12:FctParamConf40:master_parameter_gateway__fct_param_conf12:FctParamConf", "10:LatMpcConf38:master_parameter_gateway__lat_mpc_conf10:LatMpcConf", "18:LatPathPlannerConf47:master_parameter_gateway__lat_path_planner_conf18:LatPathPlannerConf", "17:LonControllerConf45:master_parameter_gateway__lon_controller_conf17:LonControllerConf", "17:MotionPlannerConf45:master_parameter_gateway__motion_planner_conf17:MotionPlannerConf", "14:TosPlannerConf42:master_parameter_gateway__tos_planner_conf14:TosPlannerConf", "11:VariantConf38:master_parameter_gateway__variant_conf11:VariantConf", "36:MotionCtlFaultEventListInterfaceType52:motion_control__motionctl_faultevent_list_senderport36:MotionCtlFaultEventListInterfaceType", "19:VmcControlDebugInfo48:motion_control__vmc_control_debuginfo_senderport19:VmcControlDebugInfo", "22:VmcOutputInterfaceType37:motion_control__vmc_output_senderport22:VmcOutputInterfaceType", "21:CFCTSafetyEnvInfoType43:netcom_gateway__netcom_FctSafetyEnvInfo_Out21:CFCTSafetyEnvInfoType", "20:RadarRFCLGUInterface46:netcom_gateway__netcom_FrontCencerRadarLgu_Out20:RadarRFCLGUInterface", "20:RadarRFCSGUInterface46:netcom_gateway__netcom_FrontCencerRadarSgu_Out20:RadarRFCSGUInterface", "20:RadarRFLSGUInterface44:netcom_gateway__netcom_FrontLeftRadarSgu_Out20:RadarRFLSGUInterface", "20:RadarRFRSGUInterface45:netcom_gateway__netcom_FrontRightRadarSgu_Out20:RadarRFRSGUInterface", "19:CHVMOutputInterface34:netcom_gateway__netcom_HvmData_Out19:CHVMOutputInterface", "38:CFCTMCUOnlytoSOCFunctionStateInterface48:netcom_gateway__netcom_McuToSocFunctionState_Out38:CFCTMCUOnlytoSOCFunctionStateInterface", "24:RadarListStatusInterface42:netcom_gateway__netcom_RadarListStatus_Out24:RadarListStatusInterface", "20:RadarRRLSGUInterface43:netcom_gateway__netcom_RearLeftRadarSgu_Out20:RadarRRLSGUInterface", "20:RadarRRRSGUInterface44:netcom_gateway__netcom_RearRightRadarSgu_Out20:RadarRRRSGUInterface", "37:CvalinDrivingParkingBodyInterfaceType42:netcom_gateway__netcom_VehicleBodyData_Out37:CvalinDrivingParkingBodyInterfaceType", "40:CvalinDrivingParkingChassisInterfaceType45:netcom_gateway__netcom_VehicleChassisData_Out40:CvalinDrivingParkingChassisInterfaceType", "41:CvalinDrivingParkingSpecificInterfaceType46:netcom_gateway__netcom_VehicleSpecificData_Out41:CvalinDrivingParkingSpecificInterfaceType", "32:TrainingRespondInfoInterfaceType49:onboard_mapping__training_respond_info_senderport32:TrainingRespondInfoInterfaceType", "44:DaCoreSitImbaFollowPrecedingObjectParameters69:parameter__sit_parameter_m_imba_follow_preceding_object_parameter_out44:DaCoreSitImbaFollowPrecedingObjectParameters", "20:PnPDebugExtendedText49:planning_driving__lane_change_point_searcher_info20:PnPDebugExtendedText", "12:PnPDebugText34:planning_driving__lat_decider_info12:PnPDebugText", "12:PnPDebugText36:planning_driving__lat_first_textinfo12:PnPDebugText", "12:PnPDebugText35:planning_driving__lon_decision_info12:PnPDebugText", "12:PnPDebugText26:planning_driving__mlm_info12:PnPDebugText", "12:PnPDebugText36:planning_driving__path_strategy_info12:PnPDebugText", "19:PlanningConfigDebug39:planning_driving__planning_config_debug19:PlanningConfigDebug", "42:PlanningDrivingFaultEventListInterfaceType61:planning_driving__planning_driving_faultevent_list_senderport42:PlanningDrivingFaultEventListInterfaceType", "22:PlanningRuntimeKeyInfo43:planning_driving__planning_runtime_key_info22:PlanningRuntimeKeyInfo", "30:PlanningToControlInterfaceType48:planning_driving__planning_to_control_senderport30:PlanningToControlInterfaceType", "26:PlanningToFctInterfaceType44:planning_driving__planning_to_fct_senderport26:PlanningToFctInterfaceType", "8:PlotData27:planning_driving__plot_data8:PlotData", "15:PlanningGrading29:planning_driving__pnp_grading15:PlanningGrading", "13:PredictionSet39:planning_driving__prediction_set_output13:PredictionSet", "12:PnPDebugText30:planning_driving__viz_api_info12:PnPDebugText", "22:EgoCarLightMarkerArray29:planning_driving__viz_ego_car22:EgoCarLightMarkerArray", "4:Path34:planning_driving__viz_global_route4:Path", "12:PnPDebugText38:planning_driving__viz_lane_change_info12:PnPDebugText", "24:LatFirstLightMarkerArray31:planning_driving__viz_lat_first24:LatFirstLightMarkerArray", "24:MapFrameLightMarkerArray31:planning_driving__viz_map_frame24:MapFrameLightMarkerArray", "12:PnPDebugText42:planning_driving__viz_motion_mpc_cost_info12:PnPDebugText", "22:MpcLatLightMarkerArray29:planning_driving__viz_mpc_lat22:MpcLatLightMarkerArray", "22:MpcLonLightMarkerArray29:planning_driving__viz_mpc_lon22:MpcLonLightMarkerArray", "22:EgoCarLightMarkerArray41:planning_driving__viz_obs_aligned_ego_car22:EgoCarLightMarkerArray", "22:ObsMapLightMarkerArray29:planning_driving__viz_obs_map22:ObsMapLightMarkerArray", "12:PnPDebugText35:planning_driving__viz_planning_info12:PnPDebugText", "26:PredictionLightMarkerArray32:planning_driving__viz_prediction26:PredictionLightMarkerArray", "12:PnPDebugText33:planning_driving__viz_status_info12:PnPDebugText", "27:TargetRouteLightMarkerArray34:planning_driving__viz_target_route27:TargetRouteLightMarkerArray", "38:HpaplanningFaultEventListInterfaceType52:planning_hpa__hpaplanning_faultevent_list_senderport38:HpaplanningFaultEventListInterfaceType", "30:PlanningToControlInterfaceType44:planning_hpa__planning_to_control_senderport30:PlanningToControlInterfaceType", "26:PlanningToFctInterfaceType40:planning_hpa__planning_to_fct_senderport26:PlanningToFctInterfaceType", "32:PredDebugInfoOutputInterfaceType46:planning_hpa__prediction_debug_info_senderport32:PredDebugInfoOutputInterfaceType", "23:PredictionInterfaceType35:planning_hpa__prediction_senderport23:PredictionInterfaceType", "32:ReferenceLineOutputInterfaceType39:planning_hpa__reference_line_senderport32:ReferenceLineOutputInterfaceType", "12:PnPDebugText44:planning_hpa__viz_l2_planning_debug_text_alc12:PnPDebugText", "12:PnPDebugText44:planning_hpa__viz_l2_planning_debug_text_gtp12:PnPDebugText", "12:PnPDebugText53:planning_hpa__viz_l2_planning_debug_text_lat_decision12:PnPDebugText", "12:PnPDebugText52:planning_hpa__viz_l2_planning_debug_text_lat_planner12:PnPDebugText", "12:PnPDebugText53:planning_hpa__viz_l2_planning_debug_text_lon_decision12:PnPDebugText", "12:PnPDebugText52:planning_hpa__viz_l2_planning_debug_text_lon_planner12:PnPDebugText", "12:PnPDebugText49:planning_hpa__viz_l2_planning_debug_text_planning12:PnPDebugText", "12:PnPDebugText44:planning_hpa__viz_l2_planning_debug_text_tos12:PnPDebugText", "9:RbpVmeVal20:rbp_infra_gw__vme_if9:RbpVmeVal", "27:RbpExternalVhmAbstBufferPub53:rbp_infra__vhm_abst___external_vhm_abst_buffer_output27:RbpExternalVhmAbstBufferPub", "21:RbpExternalVhmAbstPub46:rbp_infra__vhm_abst___external_vhm_abst_output21:RbpExternalVhmAbstPub", "17:DaCoreSitLocaData29:sit__loca_runnable_m_loca_out17:DaCoreSitLocaData", "45:DaCoreSitIntrospectionTiplBehaviorIndependent65:sit__runnable_behavior_strategy_tipl_m_tx_port_introspection_tipl45:DaCoreSitIntrospectionTiplBehaviorIndependent", "47:DaCoreSitBehaviorEvaluationPacketCfmDoorOpening63:sit__runnable_cfm_door_opening_m_behavior_evaluation_packet_out47:DaCoreSitBehaviorEvaluationPacketCfmDoorOpening", "53:DaCoreSitBehaviorEvaluationPacketCfmFrontCrossTraffic70:sit__runnable_cfm_front_cross_traffic_m_behavior_evaluation_packet_out53:DaCoreSitBehaviorEvaluationPacketCfmFrontCrossTraffic", "59:DaCoreSitBehaviorEvaluationPacketCfmFrontTrafficCoDriverLon78:sit__runnable_cfm_front_traffic_co_driver_lon_m_behavior_evaluation_packet_out59:DaCoreSitBehaviorEvaluationPacketCfmFrontTrafficCoDriverLon", "48:DaCoreSitBehaviorEvaluationPacketCfmPreCrashRear65:sit__runnable_cfm_pre_crash_rear_m_behavior_evaluation_packet_out48:DaCoreSitBehaviorEvaluationPacketCfmPreCrashRear", "52:DaCoreSitBehaviorEvaluationPacketCfmRearCrossTraffic69:sit__runnable_cfm_rear_cross_traffic_m_behavior_evaluation_packet_out52:DaCoreSitBehaviorEvaluationPacketCfmRearCrossTraffic", "62:DaCoreSitBehaviorEvaluationPacketTiplForwardCollisionAvoidance79:sit__runnable_tipl_forward_collision_avoidance_m_behavior_evaluation_packet_out62:DaCoreSitBehaviorEvaluationPacketTiplForwardCollisionAvoidance", "54:DaCoreSitBehaviorEvaluationPacketTiplLaneChangeWarning71:sit__runnable_tipl_lane_change_warning_m_behavior_evaluation_packet_out54:DaCoreSitBehaviorEvaluationPacketTiplLaneChangeWarning", "57:DaCoreSitBehaviorEvaluationPacketTiplLaneKeepingAssisting74:sit__runnable_tipl_lane_keeping_assisting_m_behavior_evaluation_packet_out57:DaCoreSitBehaviorEvaluationPacketTiplLaneKeepingAssisting", "49:DaCoreSitBehaviorEvaluationPacketTiplTurnCoDriver66:sit__runnable_tipl_turn_co_driver_m_behavior_evaluation_packet_out49:DaCoreSitBehaviorEvaluationPacketTiplTurnCoDriver", "60:DaCoreSitBehaviorEvaluationPacketTixlIntersectionCoDriverLon78:sit__runnable_tixl_intersection_co_driver_lon_m_behavior_evaluation_packet_out60:DaCoreSitBehaviorEvaluationPacketTixlIntersectionCoDriverLon", "26:CvalinHmiSomeipRxInterface35:someip_gateway__someIpGw_FctHmi_Out26:CvalinHmiSomeipRxInterface", "28:CvalinSDMapPathInfoInterface42:someip_gateway__someIpGw_SDMapPathInfo_Out28:CvalinSDMapPathInfoInterface", "7:PreProc28:t10__pre_proc_m_pre_proc_out7:PreProc", "33:DaCorePerOutputHostVehicleHistory46:t20__per_pme_runnable_m_host_veh_hist_port_out33:DaCorePerOutputHostVehicleHistory", "40:DaCorePerOutputHostVehicleMovementStates36:t20__per_pme_runnable_m_pme_port_out40:DaCorePerOutputHostVehicleMovementStates", "35:DaCorePerInputHostVehicleParameters40:t20__per_spp_hvm_hvm_parameters_port_out35:DaCorePerInputHostVehicleParameters", "21:CFCTSafetyEnvInfoType36:t20__post_proc_m_safety_env_info_out21:CFCTSafetyEnvInfoType", "27:CFCTSafetyFunctionStateType42:t20__post_proc_m_safety_function_state_out27:CFCTSafetyFunctionStateType", "25:FunctionStateResponseType51:t20__post_proc_m_safety_function_state_response_out25:FunctionStateResponseType", "21:DaCoreRpmServiceState39:t20__rpm_execution_m_port_service_state21:DaCoreRpmServiceState", "13:MainProcDebug60:t20__runnable_main_proc_m_port_main_proc_debug_interface_out13:MainProcDebug", "8:MainProc44:t20__runnable_main_proc_m_port_main_proc_out8:MainProc", "37:XPerViperFreeSpaceOutputInterfaceType52:viper__bev_freespace_post___bev_freespace_senderport37:XPerViperFreeSpaceOutputInterfaceType", "31:XPerViperModeDebugInterfaceType52:viper__bev_static_post___debug_viper_mode_senderport31:XPerViperModeDebugInterfaceType", "33:XPerDebugViperObjectInterfaceType57:viper__drv_bev_dynamic_post___debug_bev_front_tele_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType57:viper__drv_bev_dynamic_post___debug_bev_front_wide_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType57:viper__drv_bev_dynamic_post___debug_bev_left_front_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType56:viper__drv_bev_dynamic_post___debug_bev_left_rear_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType51:viper__drv_bev_dynamic_post___debug_bev_rear_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType58:viper__drv_bev_dynamic_post___debug_bev_right_front_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType57:viper__drv_bev_dynamic_post___debug_bev_right_rear_object33:XPerDebugViperObjectInterfaceType", "25:XPerViperMotInterfaceType50:viper__drv_bev_dynamic_post___viper_mot_senderport25:XPerViperMotInterfaceType", "20:XPerViperAexLaneType49:viper__drv_bev_mot___viper_Aexlane_bev_senderport20:XPerViperAexLaneType", "22:XPerViperAexBevObjType48:viper__drv_bev_mot___viper_Aexobj_bev_senderport22:XPerViperAexBevObjType", "31:XPerViperModeDebugInterfaceType56:viper__drv_bev_static_post___debug_viper_mode_senderport31:XPerViperModeDebugInterfaceType", "44:XPerViperBevStaticRoadStructureInterfaceType65:viper__drv_bev_static_post___drv_static_road_structure_senderport44:XPerViperBevStaticRoadStructureInterfaceType", "26:XPerViperLaneInterfaceType50:viper__drv_bev_static_post___lane_debug_senderport26:XPerViperLaneInterfaceType", "35:XPerViperProcessedLaneInterfaceType54:viper__drv_bev_static_post___processed_lane_senderport35:XPerViperProcessedLaneInterfaceType", "30:XPerViperRoadEdgeInterfaceType55:viper__drv_bev_static_post___road_edge_debug_senderport30:XPerViperRoadEdgeInterfaceType", "20:XPerViperAexLaneType53:viper__drv_bev_static_post___viper_Aexlane_senderport20:XPerViperAexLaneType", "20:XPerViperAexLaneType48:viper__drv_fv_net_mot___viper_Aexlane_senderport20:XPerViperAexLaneType", "23:XPerViperAexMonoObjType52:viper__drv_fv_net_mot___viper_Aexobj_mono_senderport23:XPerViperAexMonoObjType", "33:XPerDebugViperObjectInterfaceType51:viper__drv_fv_net_post___debug_fv_front_tele_object33:XPerDebugViperObjectInterfaceType", "33:XPerDebugViperObjectInterfaceType51:viper__drv_fv_net_post___debug_fv_front_wide_object33:XPerDebugViperObjectInterfaceType", "25:XPerViperMotInterfaceType48:viper__drv_fv_net_post___viper_fv_mot_senderport25:XPerViperMotInterfaceType", "33:XPerViperBlockageOddInterfaceType57:viper__drv_image_odd_post___viper_blockage_odd_senderport33:XPerViperBlockageOddInterfaceType", "37:XPerViperFreeSpaceOutputInterfaceType50:viper__drv_occ_post___drv_bev_freespace_senderport37:XPerViperFreeSpaceOutputInterfaceType", "31:XPerViperOCCPointsInterfaceType43:viper__drv_occ_post___occ_points_senderport31:XPerViperOCCPointsInterfaceType", "28:XPerViperEnvOddInterfaceType52:viper__drv_traffic_fusion___viper_env_odd_senderport28:XPerViperEnvOddInterfaceType", "40:XPerViperTrafficLightOutputInterfaceType58:viper__drv_traffic_fusion___viper_traffic_light_senderport40:XPerViperTrafficLightOutputInterfaceType", "39:XPerViperTrafficSignOutputInterfaceType57:viper__drv_traffic_fusion___viper_traffic_sign_senderport39:XPerViperTrafficSignOutputInterfaceType", "40:XPerViperTrafficLightOutputInterfaceType50:viper__drv_traffic_post___debug_traffic_light_post40:XPerViperTrafficLightOutputInterfaceType", "39:XPerViperTrafficSignOutputInterfaceType49:viper__drv_traffic_post___debug_traffic_sign_post39:XPerViperTrafficSignOutputInterfaceType", "21:PrivCXPerEnvOddOutput54:viper__drv_traffic_post___processed_env_odd_senderport21:PrivCXPerEnvOddOutput", "27:PrivCXPerTrafficLightOutput60:viper__drv_traffic_post___processed_traffic_light_senderport27:PrivCXPerTrafficLightOutput", "26:PrivCXPerTrafficSignOutput59:viper__drv_traffic_post___processed_traffic_sign_senderport26:PrivCXPerTrafficSignOutput", "39:DrivingViperFaultEventListInterfaceType76:viper__inference_comfort___drivingviper_inference_faultevent_list_senderport39:DrivingViperFaultEventListInterfaceType", "30:XPerViperRoadListInterfaceType59:viper__inference_comfort___drv_static_drive_path_debug_port30:XPerViperRoadListInterfaceType", "23:ViperBpuTriggerInfoType59:viper__inference_comfort___drv_viper_bpu_trigger_senderport23:ViperBpuTriggerInfoType", "39:ParkingViperFaultEventListInterfaceType76:viper__inference_comfort___parkingviper_inference_faultevent_list_senderport39:ParkingViperFaultEventListInterfaceType", "23:ViperBpuTriggerInfoType58:viper__inference_safedrv___drv_safe_bpu_trigger_senderport23:ViperBpuTriggerInfoType", "39:DrivingViperFaultEventListInterfaceType72:viper__inference_safedrv___drv_safe_inference_faultevent_list_senderport39:DrivingViperFaultEventListInterfaceType", "32:XPerViperLampObjectInterfaceType45:viper__lamp_post_proc___viper_lamp_senderport32:XPerViperLampObjectInterfaceType", "39:DrivingViperFaultEventListInterfaceType65:viper__status_collector___drivingviper_faultevent_list_senderport39:DrivingViperFaultEventListInterfaceType", "31:PrmHdlrOutputParametersCalibtst44:vmpsGateway__prmgatewayoutputparameterscalib31:PrmHdlrOutputParametersCalibtst", "38:PrmHdlrOutputParametersCertificatestst51:vmpsGateway__prmgatewayoutputparameterscertificates38:PrmHdlrOutputParametersCertificatestst", "35:PrmHdlrOutputParametersLeverarmstst48:vmpsGateway__prmgatewayoutputparametersleverarms35:PrmHdlrOutputParametersLeverarmstst", "28:PrmHdlrOutputParametersWntst41:vmpsGateway__prmgatewayoutputparameterswn28:PrmHdlrOutputParametersWntst", "29:PrmHdlrOutputParametersWsstst42:vmpsGateway__prmgatewayoutputparameterswss29:PrmHdlrOutputParametersWsstst", "31:VmpsFaultEventListInterfaceType44:vmpsGateway__vmps_faultevent_list_senderport31:VmpsFaultEventListInterfaceType", "24:CdHdlGatewayEphOutputtst31:vmpsGateway__vmps_gw_cd_eph_out24:CdHdlGatewayEphOutputtst", "16:GnssWssCdhIfType37:vmpsGateway__vmps_gw_gnss_wss_cdh_out16:GnssWssCdhIfType", "27:CdHdlLBandAuthInterfaceType35:vmpsGateway__vmps_gw_lband_auth_out27:CdHdlLBandAuthInterfaceType", "32:GnssGatewayOutputLBandPmpRawData34:vmpsGateway__vmps_gw_lband_raw_out32:GnssGatewayOutputLBandPmpRawData", "24:AexSafetyFunctionControl45:t10__vmc_post_proc_m_aex_function_control_out24:AexSafetyFunctionControl"]}]}, "imageRecorder": {"serveIp": "0.0.0.0", "servePort": 11883, "sendFinishResponse": false, "connectionIdleTime": 30, "recodeWithCpu": false, "webcam": true, "webcamList": []}, "rosbagRecorder": {"postProcess": false, "compression": "lz4", "sinkThreshold": 18000}, "sopRecorder": {"dataflows": [{"name": "aos", "sourceAddress": "tcp://**********:5555", "endpoints": [{"channel": "aos", "sinkAddress": "tcp://***********:6666", "serviceLists": ["DaCorePerInputConnectedEnvironmentInput.activity_spp__spp_mops_video_objects_connected_environment_out.DaCorePerInputConnectedEnvironmentInput", "PersistJsonInterfaceType.aracom_gateway__fct_persist_jsonsenderport.PersistJsonInterfaceType", "SensorXxtrinsicInterfaceType.aracom_gateway__offline_sensor_calib_parameter_senderport.SensorXxtrinsicInterfaceType", "SensorXxtrinsicInterfaceType.aracom_gateway__online_sensor_calib_parameter_senderport.SensorXxtrinsicInterfaceType", "SensorXxtrinsicInterfaceType.aracom_gateway__sensor_calib_parameter_senderport.SensorXxtrinsicInterfaceType", "VariantFunctionConfigInterfaceType.aracom_gateway__variant_function_config_senderport.VariantFunctionConfigInterfaceType", "CameraSrvFaultEventListInterfaceType.camera_service__camerasrv_faultevent_list_senderport.CameraSrvFaultEventListInterfaceType", "CSlaOutput.dummy_sky__portslaoutsenderport.CSlaOutput", "VariantFunctionConfigInterfaceType.dummy_sky__variant_function_config_senderport.VariantFunctionConfigInterfaceType", "DynamicFusionToApaInterfaceType.dynamic_fusion__driving_ego_dynamic_objects_senderport.DynamicFusionToApaInterfaceType", "DynamicFusionToApaInterfaceType.dynamic_fusion__dynamic_fusion_apa_obj_list_senderport.DynamicFusionToApaInterfaceType", "DynamicFusionObjListInterfaceType.dynamic_fusion__dynamic_fusion_obj_list_senderport.DynamicFusionObjListInterfaceType", "DynamicFusion_StateService.dynamic_fusion\\.dynamic_fusion$State.DynamicFusionState_StateEvent", "DynamicfusionFaultEventListInterfaceType.dynamic_fusion__dynamicfusion_faultevent_list_senderport.DynamicfusionFaultEventListInterfaceType", "DaCorePerOutputConnectedEnvironmentOutput.env_model1v__per_eim_tpr_runnable_m_connected_environment_out.DaCorePerOutputConnectedEnvironmentOutput", "DaCorePerOutputFusedObjectsDynamic.env_model1v__per_eim_tpr_runnable_m_fus_obj_port_out.DaCorePerOutputFusedObjectsDynamic", "DaCorePerInputOutputImageDerivedObjects.env_model1v__per_eim_tpr_runnable_m_fused_image_derived_obj_port_out.DaCorePerInputOutputImageDerivedObjects", "DaCorePerInputOutputImageDerivedObjects.env_model1v__per_eim_tpr_runnable_m_video_only_image_derived_obj_port_out.DaCorePerInputOutputImageDerivedObjects", "DaCorePerOutputCourseLimits.env_model1v__per_rep_runnable_m_course_limits_port_out.DaCorePerOutputCourseLimits", "DaCorePerOutputMapRoadGeometryAndLanes.env_model1v__per_rep_runnable_m_map_road_geometry_and_lanes_port_out.DaCorePerOutputMapRoadGeometryAndLanes", "DaCorePerOutputRoadLines.env_model1v__per_rep_runnable_m_road_lines_out.DaCorePerOutputRoadLines", "DaCorePerOutputRoadLinesPolynomial.env_model1v__per_rep_runnable_m_road_lines_polynom_port_out.DaCorePerOutputRoadLinesPolynomial", "DaCorePerOutputSensorInputUncertainty.env_model1v__per_rep_runnable_m_sensor_input_uncertainty_out.DaCorePerOutputSensorInputUncertainty", "DaCorePerOutputTraceOutputInterface.env_model1v__per_rep_runnable_m_trace_port_out.DaCorePerOutputTraceOutputInterface", "DaCorePerOutputRoadSignsRepresentation.env_model1v__per_rep_runnable_m_video_road_sign_properties_port_out.DaCorePerOutputRoadSignsRepresentation", "DaCorePerInputOutputImageDerivedObjects.env_model1v__per_track2_track_runnable_m_image_derived_objects_port_out.DaCorePerInputOutputImageDerivedObjects", "DaCorePerOutputFusedObjectsDynamic.env_model1v__sync_env_m_trigger_port_out.DaCorePerOutputFusedObjectsDynamic", "Aosfsmrunnable_StateService.fct\\.aosfsmrunnable$State.AosfsmrunnableState_StateEvent", "CRACTLToRapathrecorderInterfaceType.fct__cractlto_rapathrecorder_senderport.CRACTLToRapathrecorderInterfaceType", "CvaloutHmiSomeipTxInterface.fct__cvalout_hmi_someip_tx.CvaloutHmiSomeipTxInterface", "FunctionStateResponseType.fct__fct_function_state_response_senderport.FunctionStateResponseType", "CFCTOverallStateType.fct__fct_overall_state_senderport.CFCTOverallStateType", "CFCTSOCOnlyFunctionStatesInterface.fct__fct_soc_to_mcu_function_state_senderport.CFCTSOCOnlyFunctionStatesInterface", "CFCTDrivingToVMCOutputInterfaceType.fct__fdmtovmcsenderport.CFCTDrivingToVMCOutputInterfaceType", "FsmBehaviorsInterfaceType.fct__fsm_behaviors_senderport.FsmBehaviorsInterfaceType", "CHmismOutput.fct__hmismoutputsenderport.CHmismOutput", "CMdaPathMngrReqOutput.fct__mda_pathmngrreq_senderport.CMdaPathMngrReqOutput", "CMdaPathOperateInterface.fct__mda_pathoperateinfo_senderport.CMdaPathOperateInterface", "CMdaWorkModeInfoOutput.fct__mda_workmodeinfo_senderport.CMdaWorkModeInfoOutput", "CPathMngrResponseInterface.fct__pathMetaDataMgr___cfct_map_response_service_senderport.CPathMngrResponseInterface", "AmbEngOutputParameterstst.gnss__ambengoutputparameters.AmbEngOutputParameterstst", "CdHdlOutputParameterstst.gnss__cdhdloutputparameters.CdHdlOutputParameterstst", "NavCoreOutputParameterstst.gnss__navcoreoutputparameters.NavCoreOutputParameterstst", "VmpsFaultEventListInterfaceType.gnss__vmps_faultevent_list_senderport.VmpsFaultEventListInterfaceType", "CHUtoFctRequestInterfaceType.host_gateway__chuto_fct_request_senderport.CHUtoFctRequestInterfaceType", "VmpsFaultEventListInterfaceType.imu_calibration__imucalibration_faultevent_list_senderport.VmpsFaultEventListInterfaceType", "HnrEngOutputParameterstst.imu__hnrengoutputparameters.HnrEngOutputParameterstst", "ImuSigCdnrOutputParameterstst.imu__imusigcdnroutputparameters.ImuSigCdnrOutputParameterstst", "GNSSInterface.imu__vmps_GnssData_Out.GNSSInterface", "ImuCorrectionInterface.imu__vmps_ImuCorrectionData_Out.ImuCorrectionInterface", "ImuRawInterface.imu__vmps_ImuRawData_Out.ImuRawInterface", "CImuRbvInterface.imu__vmps_ImuRbvData_Out.CImuRbvInterface", "InsInterface.imu__vmps_InsData_Out.InsInterface", "CustOutMidTrimSFIntMsgtst.imu__vmps_IntMsg_Out.CustOutMidTrimSFIntMsgtst", "CvaloutHmiImuRpcSomeipTxInterface.imu__vmps_VMPSINSInfoSrv_senderport.CvaloutHmiImuRpcSomeipTxInterface", "LocmapproxyFaultEventListInterfaceType.loc_map_proxy__gmsf_faultevent_list_senderport.LocmapproxyFaultEventListInterfaceType", "LocmapproxyFaultEventListInterfaceType.loc_map_proxy__map_matching_faultevent_list_senderport.LocmapproxyFaultEventListInterfaceType", "XPerMapMatchingInterfaceType.loc_map_proxy__map_matching_result_senderport.XPerMapMatchingInterfaceType", "HDMapDataHighSpeedInterfaceType.loc_map_proxy__map_srv_hdmap_hs_senderport.HDMapDataHighSpeedInterfaceType", "HDMapDataLowSpeedInterfaceType.loc_map_proxy__map_srv_hdmap_ls_senderport.HDMapDataLowSpeedInterfaceType", "HDMapDataMidSpeedInterfaceType.loc_map_proxy__map_srv_hdmap_ms_senderport.HDMapDataMidSpeedInterfaceType", "LocmapproxyFaultEventListInterfaceType.loc_map_proxy__mm_pre_viper_faultevent_list_senderport.LocmapproxyFaultEventListInterfaceType", "XPerGlobalLocResultInterfaceType.loc_map_proxy__xper_global_loc_result_senderport.XPerGlobalLocResultInterfaceType", "XPerGlobalLocToMapSrvInterfaceType.loc_map_proxy__xper_global_loc_to_mapsrv_senderport.XPerGlobalLocToMapSrvInterfaceType", "LocalMapInterfaceType.local_mapping__xper_local_map_result_senderport.LocalMapInterfaceType", "GNSSInterface.local_msf__gnss_senderport.GNSSInterface", "InsInterface.local_msf__ins_senderport.InsInterface", "LocmsfFaultEventListInterfaceType.local_msf__localmsf_faultevent_list_senderport.LocmsfFaultEventListInterfaceType", "LocmsfFaultEventListInterfaceType.local_msf__perodom_faultevent_list_senderport.LocmsfFaultEventListInterfaceType", "XOdometryResultInterfaceType.local_msf__xodometry_result_senderport.XOdometryResultInterfaceType", "XPerLocalLocResultInterfaceType.local_msf__xper_local_loc_result_senderport.XPerLocalLocResultInterfaceType", "StaticFusionRoadInterfaceL2DebugType.map_enhancement__static_fusion_l2funcion_debug_senderport.StaticFusionRoadInterfaceL2DebugType", "StaticFusionRoadInterfaceType.map_enhancement__static_fusion_road_senderport.StaticFusionRoadInterfaceType", "BaseMapInfoInterfaceType.mapsrv__base_map_info_senderport.BaseMapInfoInterfaceType", "ManuverMapInfoInterfaceType.mapsrv__guidance___maneuver_map_info.ManuverMapInfoInterfaceType", "SdpMapGuidanceInfoInterfaceType.mapsrv__guidance___sdp_map_guidance_info_senderport.SdpMapGuidanceInfoInterfaceType", "LocMapInfoInterfaceType.mapsrv__loc_map_info_senderport.LocMapInfoInterfaceType", "OriginMapInfoInterfaceType.mapsrv__origin_map_info_senderport.OriginMapInfoInterfaceType", "PlanningMapInfoInterfaceType.mapsrv__planning_map_info_senderport.PlanningMapInfoInterfaceType", "SdpMapHorizonInfoInterfaceType.mapsrv__sdp_map_horizon_info_senderport.SdpMapHorizonInfoInterfaceType", "AcfPlannerConf.master_parameter_gateway__acf_planner_conf.AcfPlannerConf", "FctParamConf.master_parameter_gateway__fct_param_conf.FctParamConf", "LatMpcConf.master_parameter_gateway__lat_mpc_conf.LatMpcConf", "LatPathPlannerConf.master_parameter_gateway__lat_path_planner_conf.LatPathPlannerConf", "LonControllerConf.master_parameter_gateway__lon_controller_conf.LonControllerConf", "MotionPlannerConf.master_parameter_gateway__motion_planner_conf.MotionPlannerConf", "TosPlannerConf.master_parameter_gateway__tos_planner_conf.TosPlannerConf", "VariantConf.master_parameter_gateway__variant_conf.VariantConf", "MotionCtlFaultEventListInterfaceType.motion_control__motionctl_faultevent_list_senderport.MotionCtlFaultEventListInterfaceType", "VmcControlDebugInfo.motion_control__vmc_control_debuginfo_senderport.VmcControlDebugInfo", "VmcOutputInterfaceType.motion_control__vmc_output_senderport.VmcOutputInterfaceType", "CFCTSafetyEnvInfoType.netcom_gateway__netcom_FctSafetyEnvInfo_Out.CFCTSafetyEnvInfoType", "RadarRFCLGUInterface.netcom_gateway__netcom_FrontCencerRadarLgu_Out.RadarRFCLGUInterface", "RadarRFCSGUInterface.netcom_gateway__netcom_FrontCencerRadarSgu_Out.RadarRFCSGUInterface", "RadarRFLSGUInterface.netcom_gateway__netcom_FrontLeftRadarSgu_Out.RadarRFLSGUInterface", "RadarRFRSGUInterface.netcom_gateway__netcom_FrontRightRadarSgu_Out.RadarRFRSGUInterface", "CHVMOutputInterface.netcom_gateway__netcom_HvmData_Out.CHVMOutputInterface", "CFCTMCUOnlytoSOCFunctionStateInterface.netcom_gateway__netcom_McuToSocFunctionState_Out.CFCTMCUOnlytoSOCFunctionStateInterface", "RadarListStatusInterface.netcom_gateway__netcom_RadarListStatus_Out.RadarListStatusInterface", "RadarRRLSGUInterface.netcom_gateway__netcom_RearLeftRadarSgu_Out.RadarRRLSGUInterface", "RadarRRRSGUInterface.netcom_gateway__netcom_RearRightRadarSgu_Out.RadarRRRSGUInterface", "CvalinDrivingParkingBodyInterfaceType.netcom_gateway__netcom_VehicleBodyData_Out.CvalinDrivingParkingBodyInterfaceType", "CvalinDrivingParkingChassisInterfaceType.netcom_gateway__netcom_VehicleChassisData_Out.CvalinDrivingParkingChassisInterfaceType", "CvalinDrivingParkingSpecificInterfaceType.netcom_gateway__netcom_VehicleSpecificData_Out.CvalinDrivingParkingSpecificInterfaceType", "TrainingRespondInfoInterfaceType.onboard_mapping__training_respond_info_senderport.TrainingRespondInfoInterfaceType", "DaCoreSitImbaFollowPrecedingObjectParameters.parameter__sit_parameter_m_imba_follow_preceding_object_parameter_out.DaCoreSitImbaFollowPrecedingObjectParameters", "PnPDebugExtendedText.planning_driving__lane_change_point_searcher_info.PnPDebugExtendedText", "PnPDebugText.planning_driving__lat_decider_info.PnPDebugText", "PnPDebugText.planning_driving__lat_first_textinfo.PnPDebugText", "PnPDebugText.planning_driving__lon_decision_info.PnPDebugText", "PnPDebugText.planning_driving__mlm_info.PnPDebugText", "PnPDebugText.planning_driving__path_strategy_info.PnPDebugText", "PlanningConfigDebug.planning_driving__planning_config_debug.PlanningConfigDebug", "PlanningDrivingFaultEventListInterfaceType.planning_driving__planning_driving_faultevent_list_senderport.PlanningDrivingFaultEventListInterfaceType", "PlanningRuntimeKeyInfo.planning_driving__planning_runtime_key_info.PlanningRuntimeKeyInfo", "PlanningToControlInterfaceType.planning_driving__planning_to_control_senderport.PlanningToControlInterfaceType", "PlanningToFctInterfaceType.planning_driving__planning_to_fct_senderport.PlanningToFctInterfaceType", "PlotData.planning_driving__plot_data.PlotData", "PlanningGrading.planning_driving__pnp_grading.PlanningGrading", "PredictionSet.planning_driving__prediction_set_output.PredictionSet", "PnPDebugText.planning_driving__viz_api_info.PnPDebugText", "EgoCarLightMarkerArray.planning_driving__viz_ego_car.EgoCarLightMarkerArray", "Path.planning_driving__viz_global_route.Path", "PnPDebugText.planning_driving__viz_lane_change_info.PnPDebugText", "LatFirstLightMarkerArray.planning_driving__viz_lat_first.LatFirstLightMarkerArray", "MapFrameLightMarkerArray.planning_driving__viz_map_frame.MapFrameLightMarkerArray", "PnPDebugText.planning_driving__viz_motion_mpc_cost_info.PnPDebugText", "MpcLatLightMarkerArray.planning_driving__viz_mpc_lat.MpcLatLightMarkerArray", "MpcLonLightMarkerArray.planning_driving__viz_mpc_lon.MpcLonLightMarkerArray", "EgoCarLightMarkerArray.planning_driving__viz_obs_aligned_ego_car.EgoCarLightMarkerArray", "ObsMapLightMarkerArray.planning_driving__viz_obs_map.ObsMapLightMarkerArray", "PnPDebugText.planning_driving__viz_planning_info.PnPDebugText", "PredictionLightMarkerArray.planning_driving__viz_prediction.PredictionLightMarkerArray", "PnPDebugText.planning_driving__viz_status_info.PnPDebugText", "TargetRouteLightMarkerArray.planning_driving__viz_target_route.TargetRouteLightMarkerArray", "HpaplanningFaultEventListInterfaceType.planning_hpa__hpaplanning_faultevent_list_senderport.HpaplanningFaultEventListInterfaceType", "PlanningToControlInterfaceType.planning_hpa__planning_to_control_senderport.PlanningToControlInterfaceType", "PlanningToFctInterfaceType.planning_hpa__planning_to_fct_senderport.PlanningToFctInterfaceType", "PredDebugInfoOutputInterfaceType.planning_hpa__prediction_debug_info_senderport.PredDebugInfoOutputInterfaceType", "PredictionInterfaceType.planning_hpa__prediction_senderport.PredictionInterfaceType", "ReferenceLineOutputInterfaceType.planning_hpa__reference_line_senderport.ReferenceLineOutputInterfaceType", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_alc.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_gtp.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_lat_decision.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_lat_planner.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_lon_decision.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_lon_planner.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_planning.PnPDebugText", "PnPDebugText.planning_hpa__viz_l2_planning_debug_text_tos.PnPDebugText", "RbpVmeVal.rbp_infra_gw__vme_if.RbpVmeVal", "RbpExternalVhmAbstBufferPub.rbp_infra__vhm_abst___external_vhm_abst_buffer_output.RbpExternalVhmAbstBufferPub", "RbpExternalVhmAbstPub.rbp_infra__vhm_abst___external_vhm_abst_output.RbpExternalVhmAbstPub", "DaCoreSitLocaData.sit__loca_runnable_m_loca_out.DaCoreSitLocaData", "DaCoreSitIntrospectionTiplBehaviorIndependent.sit__runnable_behavior_strategy_tipl_m_tx_port_introspection_tipl.DaCoreSitIntrospectionTiplBehaviorIndependent", "DaCoreSitBehaviorEvaluationPacketCfmDoorOpening.sit__runnable_cfm_door_opening_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketCfmDoorOpening", "DaCoreSitBehaviorEvaluationPacketCfmFrontCrossTraffic.sit__runnable_cfm_front_cross_traffic_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketCfmFrontCrossTraffic", "DaCoreSitBehaviorEvaluationPacketCfmFrontTrafficCoDriverLon.sit__runnable_cfm_front_traffic_co_driver_lon_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketCfmFrontTrafficCoDriverLon", "DaCoreSitBehaviorEvaluationPacketCfmPreCrashRear.sit__runnable_cfm_pre_crash_rear_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketCfmPreCrashRear", "DaCoreSitBehaviorEvaluationPacketCfmRearCrossTraffic.sit__runnable_cfm_rear_cross_traffic_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketCfmRearCrossTraffic", "DaCoreSitBehaviorEvaluationPacketTiplForwardCollisionAvoidance.sit__runnable_tipl_forward_collision_avoidance_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketTiplForwardCollisionAvoidance", "DaCoreSitBehaviorEvaluationPacketTiplLaneChangeWarning.sit__runnable_tipl_lane_change_warning_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketTiplLaneChangeWarning", "DaCoreSitBehaviorEvaluationPacketTiplLaneKeepingAssisting.sit__runnable_tipl_lane_keeping_assisting_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketTiplLaneKeepingAssisting", "DaCoreSitBehaviorEvaluationPacketTiplTurnCoDriver.sit__runnable_tipl_turn_co_driver_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketTiplTurnCoDriver", "DaCoreSitBehaviorEvaluationPacketTixlIntersectionCoDriverLon.sit__runnable_tixl_intersection_co_driver_lon_m_behavior_evaluation_packet_out.DaCoreSitBehaviorEvaluationPacketTixlIntersectionCoDriverLon", "CvalinHmiSomeipRxInterface.someip_gateway__someIpGw_FctHmi_Out.CvalinHmiSomeipRxInterface", "CvalinSDMapPathInfoInterface.someip_gateway__someIpGw_SDMapPathInfo_Out.CvalinSDMapPathInfoInterface", "PreProc.t10__pre_proc_m_pre_proc_out.PreProc", "DaCorePerOutputHostVehicleHistory.t20__per_pme_runnable_m_host_veh_hist_port_out.DaCorePerOutputHostVehicleHistory", "DaCorePerOutputHostVehicleMovementStates.t20__per_pme_runnable_m_pme_port_out.DaCorePerOutputHostVehicleMovementStates", "DaCorePerInputHostVehicleParameters.t20__per_spp_hvm_hvm_parameters_port_out.DaCorePerInputHostVehicleParameters", "CFCTSafetyEnvInfoType.t20__post_proc_m_safety_env_info_out.CFCTSafetyEnvInfoType", "CFCTSafetyFunctionStateType.t20__post_proc_m_safety_function_state_out.CFCTSafetyFunctionStateType", "FunctionStateResponseType.t20__post_proc_m_safety_function_state_response_out.FunctionStateResponseType", "DaCoreRpmServiceState.t20__rpm_execution_m_port_service_state.DaCoreRpmServiceState", "MainProcDebug.t20__runnable_main_proc_m_port_main_proc_debug_interface_out.MainProcDebug", "MainProc.t20__runnable_main_proc_m_port_main_proc_out.MainProc", "XPerViperFreeSpaceOutputInterfaceType.viper__bev_freespace_post___bev_freespace_senderport.XPerViperFreeSpaceOutputInterfaceType", "XPerViperModeDebugInterfaceType.viper__bev_static_post___debug_viper_mode_senderport.XPerViperModeDebugInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_front_tele_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_front_wide_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_left_front_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_left_rear_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_rear_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_right_front_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_bev_dynamic_post___debug_bev_right_rear_object.XPerDebugViperObjectInterfaceType", "XPerViperMotInterfaceType.viper__drv_bev_dynamic_post___viper_mot_senderport.XPerViperMotInterfaceType", "XPerViperAexLaneType.viper__drv_bev_mot___viper_Aexlane_bev_senderport.XPerViperAexLaneType", "XPerViperAexBevObjType.viper__drv_bev_mot___viper_Aexobj_bev_senderport.XPerViperAexBevObjType", "XPerViperModeDebugInterfaceType.viper__drv_bev_static_post___debug_viper_mode_senderport.XPerViperModeDebugInterfaceType", "XPerViperBevStaticRoadStructureInterfaceType.viper__drv_bev_static_post___drv_static_road_structure_senderport.XPerViperBevStaticRoadStructureInterfaceType", "XPerViperLaneInterfaceType.viper__drv_bev_static_post___lane_debug_senderport.XPerViperLaneInterfaceType", "XPerViperProcessedLaneInterfaceType.viper__drv_bev_static_post___processed_lane_senderport.XPerViperProcessedLaneInterfaceType", "XPerViperRoadEdgeInterfaceType.viper__drv_bev_static_post___road_edge_debug_senderport.XPerViperRoadEdgeInterfaceType", "XPerViperAexLaneType.viper__drv_bev_static_post___viper_Aexlane_senderport.XPerViperAexLaneType", "XPerViperAexLaneType.viper__drv_fv_net_mot___viper_Aexlane_senderport.XPerViperAexLaneType", "XPerViperAexMonoObjType.viper__drv_fv_net_mot___viper_Aexobj_mono_senderport.XPerViperAexMonoObjType", "XPerDebugViperObjectInterfaceType.viper__drv_fv_net_post___debug_fv_front_tele_object.XPerDebugViperObjectInterfaceType", "XPerDebugViperObjectInterfaceType.viper__drv_fv_net_post___debug_fv_front_wide_object.XPerDebugViperObjectInterfaceType", "XPerViperMotInterfaceType.viper__drv_fv_net_post___viper_fv_mot_senderport.XPerViperMotInterfaceType", "XPerViperBlockageOddInterfaceType.viper__drv_image_odd_post___viper_blockage_odd_senderport.XPerViperBlockageOddInterfaceType", "XPerViperFreeSpaceOutputInterfaceType.viper__drv_occ_post___drv_bev_freespace_senderport.XPerViperFreeSpaceOutputInterfaceType", "XPerViperOCCPointsInterfaceType.viper__drv_occ_post___occ_points_senderport.XPerViperOCCPointsInterfaceType", "XPerViperEnvOddInterfaceType.viper__drv_traffic_fusion___viper_env_odd_senderport.XPerViperEnvOddInterfaceType", "XPerViperTrafficLightOutputInterfaceType.viper__drv_traffic_fusion___viper_traffic_light_senderport.XPerViperTrafficLightOutputInterfaceType", "XPerViperTrafficSignOutputInterfaceType.viper__drv_traffic_fusion___viper_traffic_sign_senderport.XPerViperTrafficSignOutputInterfaceType", "XPerViperTrafficLightOutputInterfaceType.viper__drv_traffic_post___debug_traffic_light_post.XPerViperTrafficLightOutputInterfaceType", "XPerViperTrafficSignOutputInterfaceType.viper__drv_traffic_post___debug_traffic_sign_post.XPerViperTrafficSignOutputInterfaceType", "PrivCXPerEnvOddOutput.viper__drv_traffic_post___processed_env_odd_senderport.PrivCXPerEnvOddOutput", "PrivCXPerTrafficLightOutput.viper__drv_traffic_post___processed_traffic_light_senderport.PrivCXPerTrafficLightOutput", "PrivCXPerTrafficSignOutput.viper__drv_traffic_post___processed_traffic_sign_senderport.PrivCXPerTrafficSignOutput", "DrivingViperFaultEventListInterfaceType.viper__inference_comfort___drivingviper_inference_faultevent_list_senderport.DrivingViperFaultEventListInterfaceType", "XPerViperRoadListInterfaceType.viper__inference_comfort___drv_static_drive_path_debug_port.XPerViperRoadListInterfaceType", "ViperBpuTriggerInfoType.viper__inference_comfort___drv_viper_bpu_trigger_senderport.ViperBpuTriggerInfoType", "ParkingViperFaultEventListInterfaceType.viper__inference_comfort___parkingviper_inference_faultevent_list_senderport.ParkingViperFaultEventListInterfaceType", "ViperBpuTriggerInfoType.viper__inference_safedrv___drv_safe_bpu_trigger_senderport.ViperBpuTriggerInfoType", "DrivingViperFaultEventListInterfaceType.viper__inference_safedrv___drv_safe_inference_faultevent_list_senderport.DrivingViperFaultEventListInterfaceType", "XPerViperLampObjectInterfaceType.viper__lamp_post_proc___viper_lamp_senderport.XPerViperLampObjectInterfaceType", "DrivingViperFaultEventListInterfaceType.viper__status_collector___drivingviper_faultevent_list_senderport.DrivingViperFaultEventListInterfaceType", "PrmHdlrOutputParametersCalibtst.vmpsGateway__prmgatewayoutputparameterscalib.PrmHdlrOutputParametersCalibtst", "PrmHdlrOutputParametersCertificatestst.vmpsGateway__prmgatewayoutputparameterscertificates.PrmHdlrOutputParametersCertificatestst", "PrmHdlrOutputParametersLeverarmstst.vmpsGateway__prmgatewayoutputparametersleverarms.PrmHdlrOutputParametersLeverarmstst", "PrmHdlrOutputParametersWntst.vmpsGateway__prmgatewayoutputparameterswn.PrmHdlrOutputParametersWntst", "PrmHdlrOutputParametersWsstst.vmpsGateway__prmgatewayoutputparameterswss.PrmHdlrOutputParametersWsstst", "VmpsFaultEventListInterfaceType.vmpsGateway__vmps_faultevent_list_senderport.VmpsFaultEventListInterfaceType", "CdHdlGatewayEphOutputtst.vmpsGateway__vmps_gw_cd_eph_out.CdHdlGatewayEphOutputtst", "GnssWssCdhIfType.vmpsGateway__vmps_gw_gnss_wss_cdh_out.GnssWssCdhIfType", "CdHdlLBandAuthInterfaceType.vmpsGateway__vmps_gw_lband_auth_out.CdHdlLBandAuthInterfaceType", "GnssGatewayOutputLBandPmpRawData.vmpsGateway__vmps_gw_lband_raw_out.GnssGatewayOutputLBandPmpRawData", "AexSafetyFunctionControl.t10__vmc_post_proc_m_aex_function_control_out.AexSafetyFunctionControl", "MTA.POSH2MTA.ClassInfo"]}]}]}, "socFileDump": [{"hostInfo": {"address": "**********", "user": "root", "authType": "none"}, "fileTasks": [{"from": "/ota/output/version_info.json", "to": "asw_version_info.json", "cache": true, "uniformPrefix": true}, {"from": "/opt/buildinfo.txt", "to": "bsw_version_info.txt", "cache": true, "uniformPrefix": true}, {"from": "/data/calib", "to": "calibration_parameter", "cache": true, "zip": true, "uniformPrefix": true}]}]}