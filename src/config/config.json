{"master_ecu_conf": {"value": {"IP": "**********", "port": 22, "username": "root", "password": "", "timeout": 30}, "description": "the master_ecu IP, PORT , user and password"}, "ipc_info": {"value": {"username": "env1szh", "password": "ENGVV202410@", "ota_package_path": "/home/<USER>/ota/package"}, "description": "IPC related info, such as ota package path, "}, "sqlite_path": {"value": "C:/Users/<USER>/Downloads/tasks.db", "description": "local sqlite database path"}, "application_launch_task_name": {"value": {"carmaker_hil": "StartCarMakerHiL", "rtmaker_radar": "StartRTMakerRadar", "rtmaker_lidar": ""}, "description": "the task name for schtasks on Windows System"}, "windows_pc_info": {"value": {"ip": "************", "port": 22, "username": "admin", "password": "abc.123"}, "description": "ipg's windows pc information"}, "uds_info": {"value": {"test_ip": "************", "target_ip": "*************", "test_address": 3712, "target_address": 1024, "port": 13400}, "description": "doip info, test_address: 0x0E80, target_address: 0x0400"}, "programme_power_info": {"value": {"ip": "***********", "port": 8462}, "description": "programme power tcp connection information"}, "mtcd_whitelist_config_paths": {"value": {"driving_whitelist": "config/mtcd/driving_mtcd_config.json", "hpa_whitelist": "config/mtcd/hpa_mtcd_config.json", "apa_whitelist": "config/mtcd/parking_mtcd_config.json", "CloudStorage_path": "/home/<USER>/public/dfs/02_Bench_And_Simulation_Test/Automation_Result/", "output_path": "/home/<USER>/Documents/LCY/mtcd_test/mtcd.json"}, "description": "MTCD configuration file paths for different scenarios and output path"}}