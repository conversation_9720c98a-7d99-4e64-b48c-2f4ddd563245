{"master_ecu_conf": {"value": {"IP": "**********", "port": 22, "username": "root", "password": "", "timeout": 30}, "description": "the master_ecu IP, PORT , user and password"}, "ipc_info": {"value": {"username": "env1szh", "password": "ENGVV202410@", "ota_package_path": "/home/<USER>/ota/package"}, "description": "IPC related info, such as ota package path, "}, "sqlite_path": {"value": "/home/<USER>/Documents/LCY/pj_w3_hil_driving/src/orm/tasks.db", "description": "local sqlite database path"}, "windows_pc_info": {"value": {"ip": "************", "port": 22, "username": "admin", "password": "abc.123"}, "description": "ipg's windows pc information"}, "uds_info": {"value": {"test_ip": "127.0.0.1", "target_ip": "127.0.0.1", "test_address": "0x0E80", "target_address": "0x0400", "port": 13400}, "description": "doip info, test_address: 0x0E80, target_address: 0x0400"}, "programme_power_info": {"value": {"ip": "***********", "port": 8462}, "description": "programme power tcp connection information"}, "mtcd_whitelist_config_paths": {"value": {"driving_whitelist": "./config/mtcd/driving_mtcd_config.json", "hpa_whitelist": "./config/mtcd/hpa_mtcd_config.json", "parking_whitelist": "./config/mtcd/parking_mtcd_config.json", "CloudStorage_path": "/home/<USER>/public/dfs/02_Bench_And_Simulation_Test/Automation_Result/", "output_path": "/opt/mtc/etc/mtcd.json"}, "description": "MTCD configuration file paths for different scenarios and output path"}, "carmaker": {"value": {"ports": {"RT_Primary": 16660, "RT_Radar": 16661}, "task_name": {"RT_Primary": "RT_Primary", "RT_Radar": "RT_Radar"}, "project_path": {"driving": "src_arch9.1.3_DBC2.3.5_SIP2.3.1_MyUAQ/CarMaker.xeno", "parking": "C:/CM_Projects/CM13_JETOUR_HPA/src_arch10.0_DBC2.3.1_SIP2.3.1_MyUAQ_V2/", "description": "carmaker hil executor path"}, "tickle_folder_path": "C:/Users/<USER>/AppData/Local/HiLAutomation"}, "description": "CarMaker client configuration"}, "ota_mail_list": {"value": ["<EMAIL>", "<EMAIL>"], "description": "OTA mail list, who will recevie failed ota result."}, "testrun_folder_path": {"value": "C:/CM_Projects/CMH_11_1_1_<PERSON>sch_Jetour/Data/TestRun/", "description": "absolute path of testrun folder"}}