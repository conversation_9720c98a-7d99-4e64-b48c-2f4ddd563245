#!/usr/bin/python

###################
# provide basic framework for test-scripts.
# TesterBase is limited to one connection.
###################


import sys
import time
import socket
import atexit
import argparse
import binascii
import traceback

from src.doipdiagnostic import doip, doiptypes
from src.utils.logger import log

verbose = False

##########################
#  utilities
##########################


def EmitResult(indicator, msg):
    print("RES:__" + indicator + "__:" + str(msg))
    sys.stdout.flush()


def toHex(binInput):
    res = ""
    for b in binInput:
        #        TraceLocal("toHex: " + str(b))
        res += "%02x" % b
    return res


def strToArray(strVal):
    return strVal.encode()


def U16ToByteArray(u16):
    u16 = int(u16)
    TraceLocal("U16ToByteArray: u16=" + str(u16))
    res = bytearray([(u16 >> 8) & 0xFF, u16 & 0xFF])
    TraceLocal("U16ToByteArray: u16=" + str(u16) +
               " -> " + str(binascii.hexlify(res)))
    return res


def U8ToByteArray(u8):
    res = bytearray([u8 & 0xFF])
    TraceLocal("U8ToByteArray: u8=" + str(u8) +
               " -> " + str(binascii.hexlify(res)))
    return res


def uxxToByteArray(uxxValue, formatSize):
    res = bytearray()
    for i in range(0, formatSize):
        shift = (formatSize - (1 + i)) * 8
        val = (uxxValue >> shift) & 0xFF
        TraceLocal(
            "formatSize: i= " +
            str(i) +
            " shift=" +
            str(shift) +
            " val=" +
            str(val) +
            "(" +
            toHex(
                bytearray(
                    [val])) +
            ")")
        res.append(val)
    TraceLocal(
        "uxxToByteArray(uxxValue=" +
        str(uxxValue) +
        " formatSize=" +
        str(formatSize) +
        ") -> " +
        toHex(res))
    return res


def getUxxFromByteArray(bArray, iStart, numBytes):
    TraceLocal(
        "getUxxFromByteArray: iStart=" +
        str(iStart) +
        " numBytes=" +
        str(numBytes))
    res = bArray[iStart]
    TraceLocal("getUxxFromByteArray: res0=" + str(res))
    if (numBytes == 1):
        return res
    for i in range(1, numBytes):
        res = (res << 8) + bArray[iStart + i]
        TraceLocal("getUxxFromByteArray: adding val=" +
                   str(bArray[iStart + i]) + " res=" + str(res))

    return res


def TraceLocal(info):
    Trace(info, "testerBase")


def Trace(info, source="main"):
    global verbose
    if verbose:
        print("[" + str(time.time()) + "][" + source + "]: " + str(info))
        sys.stdout.flush()


class TestFailException(Exception):
    pass


def tcFail(comment=None):
    TraceLocal("tcFail: " + str(comment))
    traceback.print_stack()

    raise TestFailException(str(comment))


def getArgumentParser():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--port",
        default=13400,
        type=int,
        help="default: 13400")
    parser.add_argument(
        "--testerIP",
        default="**********",
        type=str,
        help="default: **********")
    parser.add_argument(
        "--targetIP",
        default="**********",
        type=str,
        help="default: **********")
    parser.add_argument(
        "--testerAddress",
        default=0x767,
        type=int,
        help="default: 1895 (0x767)")
    parser.add_argument(
        "--targetAddress",
        default=0x747,
        type=int,
        help="default: 1863 (0x747)")
    parser.add_argument(
        "--route",
        default=0x02,
        type=int,
        help="doip-route default: 0x02")
    parser.add_argument(
        "--verbose",
        default=False,
        const=True,
        action='store_const',
        dest='verbose',
        help="verbose-mode")

    return parser


###################################################
# class to combine several tests into a test-suite
###################################################
class TestSuite:
    def __init__(self):
        self.tests = []
        self.numTc = 0
        self.numPassed = 0
        self.numFailed = 0

    def doTest(self, testFn):
        self.tcName = testFn.__name__
        self.passed = True
        self.numTc = self.numTc + 1
        result = {
            "name": self.tcName,
            "passed": True
        }
        try:
            testFn()
        except TestFailException as error:
            TraceLocal("#### tcFail(" + str(self.tcName) + ")")
            TraceLocal("Cought TestFailException: " + str(error))
            result["passed"] = False
        self.tests.append(result)
        if (result["passed"]):
            self.numPassed = self.numPassed + 1
        else:
            self.numFailed = self.numFailed + 1

    def showVerdict(self):
        TraceLocal("showVerdict:")

        TraceLocal("num TC=" + str(self.numTc))
        TraceLocal("numPassed=" + str(self.numPassed))
        TraceLocal("numFailed=" + str(self.numFailed))

        for tc in self.tests:
            if (tc["passed"]):
                TraceLocal("   " + tc["name"] + " -> PASSED")
            else:
                TraceLocal("   " + tc["name"] + " -> FAILED")


class Uds:
    def __init__(self, data=None):
        self.data = bytearray()
        if data:
            self.data = data

    def fromDoipMsg(self, doipMsg):
        self.data = doipMsg.payload[4:]

    def getType(self):
        if len(self.data):
            return self.data[0]
        else:
            return 0

    def getData(self):
        if len(self.data) > 1:
            return self.data[1:]
        else:
            return bytearray()


class TestNode(doip.Node):
    def __init__(self, targetIP, targetAddress, tester):
        TraceLocal("TestNode:__init__")
        TraceLocal("   targetIP=" + str(targetIP))
        TraceLocal("   targetAddress=" + str(targetAddress))
        doip.Node.__init__(
            self,
            targetIP,
            U16ToByteArray(targetAddress),
            tester)
        self.targetIP = targetIP
        self.targetAddress = targetAddress


class DoipMsg():
    def __init__(
            self,
            prot=0x02,
            protInv=0xFD,
            payloadType=0x8001,
            payloadLen=None,
            payload=bytearray(),
            doPrint=False):
        if payloadLen is None:
            payloadLen = len(payload)
        self.hdr = bytearray(8)
        self.hdr[0] = prot
        self.hdr[1] = protInv
        self.hdr[2] = ((payloadType >> 8) & 0xFF)
        self.hdr[3] = ((payloadType >> 0) & 0xFF)
        self.hdr[4] = ((payloadLen >> 24) & 0xFF)
        self.hdr[5] = ((payloadLen >> 16) & 0xFF)
        self.hdr[6] = ((payloadLen >> 8) & 0xFF)
        self.hdr[7] = ((payloadLen >> 0) & 0xFF)
        self.payload = payload
        if (doPrint):
            TraceLocal("DoipMsg len=" + str(len(self.payload)))
            TraceLocal("   hdr:" + str(binascii.hexlify(self.hdr)))
            TraceLocal("   payload:" + str(binascii.hexlify(self.payload)))
        self.data = self.hdr + self.payload
        self.message = None

    def set(self, data, message):
        self.hdr = data[0:8]
        self.payload = data[8:]
        self.data = data
        self.message = message

    def getName(self):
        if (self.message is None):
            return "Unknown"
        return self.message.payload.Name


#######################################################
# test-cases use TesterBase to communicate with server
#######################################################
class Exeption:
    pass


class TesterBase(doip.Tester):
    def __init__(
            self,
            testerIP="**************",
            targetIP="***************",
            testerAddress=0x767,
            targetAddress=0x764,
            port=13400):
        TraceLocal("TesterBase:__init__")
        # log = log
        self.testerIP = testerIP
        self.targetIP = targetIP
        self.testerAddress = eval(testerAddress)
        self.targetAddress = eval(targetAddress)
        self.port = port
        log.info("tester IP is %s" % self.testerIP)
        log.info("target IP is %s" % self.targetIP)
        log.info("tester Address is %s" % str(self.testerAddress))
        log.info("target Address is %s" % str(self.targetAddress))
        log.info("port is %s" % str(self.port))
        global verbose
        verbose = False
        doiptypes.setVerbose(verbose)
        doip.setVerbose(verbose)
        doip.Tester.__init__(self, self.testerIP, self.port, self.testerAddress)

        self.route = 0x00
        log.info("TesterBase:targetIP=" + str(self.targetIP))

        self.testNode = TestNode(self.targetIP, self.targetAddress, self)
        self.connected = False

    def connectSync(self):
        self.socketTCP = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.socketTCP.setsockopt(socket.IPPROTO_TCP, socket.TCP_NODELAY, 1)
        self.socketTCP.settimeout(5)

        log.info("Tester:connectSync: try to connect to " +
                   self.targetIP + ":" + str(self.port))
        self.socketTCP.connect((self.targetIP, self.port))
        self.testNode.socketTCP = self.socketTCP
        self.connected = True

    def disconnect(self):
        log.info("Tester:disconnect()")

        try:
            self.socketTCP.shutdown(socket.SHUT_RDWR)
            self.socketTCP.close()
        except Exception as e:
            log.error("disconnect: not connected")
        self.connected = False

    def receiveBytes(self, numBytes, timeout):
        self.socketTCP.settimeout(timeout)
        numReceived = 0
        data = bytearray()

        try:
            chunk = self.socketTCP.recv(numBytes)
            data += chunk
        except socket.timeout as e:
            log.error("Tester:receiveBytes: Timeout")
#            self.socketTCP.close()
            return ("Timeout", data)
        if (len(chunk) == 0):
            self.socketTCP.close()
            return ("Disconnect", data)
        else:
            return ("Ok", data)

    def receiveMsg(self, timeout=4):
        # receive header
        allOk = False
        doipMsgBin = bytearray()
        log.info("Test::receiveMsg START timeout=" + str(timeout))
        (res, doipMsgBin) = self.receiveBytes(8, timeout)
        if res == "Timeout":
            log.info("Test::receiveMsg Timout waiting for doipHeader")
            message = doiptypes.Message(doiptypes.Timeout())
            doipMsgBin = message.payload.getDefaultData()
        elif res == "Disconnect":
            log.info("Test::receiveMsg Disconnect waiting for doipHeader")
            message = doiptypes.Message(doiptypes.Disconnect())
            doipMsgBin = message.payload.getDefaultData()
        else:
            payloadLen = (doipMsgBin[4] << 24) + (doipMsgBin[5]
                                                  << 16) + (doipMsgBin[6] << 8) + (doipMsgBin[7])
            log.info("Test::receiveMsg doipHdr=" +
                       str(binascii.hexlify(doipMsgBin)) +
                       " payloadLen=" +
                       str(payloadLen))
            allOk = True

        if allOk:
            (res, payloadBin) = self.receiveBytes(payloadLen, timeout)
            if res == "Timeout":
                log.info("Test::receiveMsg Timout waiting for payload")
                message = doiptypes.Message(doiptypes.Timeout())
                doipMsgBin = message.payload.getDefaultData()
            elif res == "Disconnect":
                log.info("Test::receiveMsg Disconnect waiting for payload")
                message = doiptypes.Message(doiptypes.Disconnect())
                doipMsgBin = message.payload.getDefaultData()
            else:
                doipMsgBin = doipMsgBin + payloadBin
                (message, bla) = doiptypes.parseMessage(doipMsgBin)
                allOk = True

        log.info("Tester:receiveMsg:got:" +
                   str(binascii.hexlify(doipMsgBin)))
        doipMsg = DoipMsg(doPrint=False)
        doipMsg.set(doipMsgBin, message)
        return doipMsg

    def expectMsg(self, msgName, timeout=4, expect=None, doFail=True):
        log.info("Tester:expectMsg:" + str(msgName) + " START")
        log.info("Tester:expectMsg:" + str(msgName) + " START")
        doipMsg = self.receiveMsg(timeout)

        res = (msgName is None or (doipMsg.getName() == msgName))
        log.info("Tester:expectMsg next step: got " + doipMsg.getName())
        log.info("Tester:expectMsg next step: got %s " % str(doipMsg.getName()))
        if expect:
            if expect.data != doipMsg.data:
                log.info("Tester:expectMsg: wrong data:")
                log.error("expected: %s" % str(binascii.hexlify(expect.data)))
                log.info("expected: %s" % str(binascii.hexlify(expect.data)))
                log.info("got:      %s"  % str(binascii.hexlify(doipMsg.data)))
                log.info("Expect %s : wrong content" % str(msgName))
                log.error("Tester:expectMsg: END res = %s" % str(res))
                if not res:
                    if (doFail):
                        log.error("Expect %s : wrong type" % str(msgName))
                        return None

        return doipMsg

    def send(self, doipMsg):
        # for some reason, large chunks don't make it to the target, so we
        # split.
        log.info("send: %s" % str(binascii.hexlify(doipMsg.data)))
        numBytes = len(doipMsg.data)
        offset = 0
        log.info("send()  START numBytes= %s" % str(numBytes))
        while numBytes:
            numSend = numBytes
            if numSend > 0x4000:
                numSend = 0x4000
            numBytes = numBytes - numSend
            log.info("send() chunk numSend= %s" % str(numSend))
            self.socketTCP.send(doipMsg.data[offset:offset + numSend])
            offset = offset + numSend
            time.sleep(0.1)
        log.info("send() DONE numBytes= %s" % str(numBytes))

    def sendUds(self, uds):
        log.info("sendUds: SID= %s" % str(hex(uds[0])))
        self.testNode.diagnosticRequest(self.targetAddress, uds)

    def getTimeMs(self):
        return int(round(time.time() * 1000))

    def expectUds(self, udsType=0, udsContent=None, timeout=6):
        log.info("Tester:expectUds START")
        timeStartMs = self.getTimeMs()
        timeEndMs = timeStartMs + timeout * 1000
        while self.getTimeMs() < timeEndMs:
            log.info("Tester:expectUds timeLeftMs= %s" % str(timeEndMs - self.getTimeMs()))
            msgtimeout = max(1, timeEndMs - self.getTimeMs())
            doipMsg = self.expectMsg(None, msgtimeout / 1000)
            msgName = doipMsg.getName()
            log.info("doipMsg.payload= %s" % str(toHex(doipMsg.payload)))
            if (msgName == "Timeout"):
                log.error("Tester:expectUds: got Timeout")
                break

            if (msgName == "DiagnosticMessageAck"):
                log.info("Tester:expectUds: got DiagnosticMessageAck")
                continue
            if (msgName == "DiagnosticMessage"):
                log.info("Tester:expectUds got DiagnosticMessage")
                uds = Uds()
                uds.fromDoipMsg(doipMsg)
                if uds.getType() == 0x7F:
                    log.info("Tester:received negative Response 7F")
                    if len(uds.data) == 2 and uds.data[1] == 0x3e:
                        log.info(
                            "Tester:expectUds ignore error on tester-present")
                        continue
                    if len(uds.data) == 3 and uds.data[2] == 0x78:
                        log.info("Tester:expectUds ignore response pending")
                        continue
                break

        content = bytearray()
        if udsContent:
            content = udsContent[0:]
# 02fd80020000000c074707670007670747
        uds = Uds()
        if len(doipMsg.data) < 5:
            tcFail("expectUds: uds-content empty:")
        uds.fromDoipMsg(doipMsg)
        if udsType and udsType != uds.getType():
            tcFail("expectUds: wrong uds-type: expected: " +
                   hex(udsType) + " got: " + hex(uds.getType()))
        if len(content):
            if (udsType != 0):
                content.insert(0, udsType)
            if content != uds.data:
                log.error("Tester:expectUds: wrong data:")
                log.error("expected: " + str(binascii.hexlify(content)))
                log.error("got:      " + str(binascii.hexlify(doipMsg.data)))
                tcFail("ExpectUds " + msgName + " : wrong content")
        log.info("expectUds ok, got:      " +
                   str(binascii.hexlify(uds.data)))
        return uds

    def connectWithRoute(self, route=None):

        log.info("connect With Route")
        if (route is None):
            route = self.route
        self.connectSync()
        self.testNode.routingActivationReq(route, bytearray(4))
        data = self.expectMsg("RoutingActivationResponse")

    def enterSession(self, sessionType):
        TraceLocal("enterSession:" + str(sessionType))
        uds = bytearray([0x10, sessionType])
        self.testNode.diagnosticRequest(self.targetAddress, uds)
        doipMsg = self.expectMsg("DiagnosticMessageAck")
        doipMsg = self.expectMsg("DiagnosticMessage")

    def getDiagMsgHeader(self, testerAddress=None, targetAddress=None):
        if testerAddress is None:
            testerAddress = self.testerAddress
        if targetAddress is None:
            targetAddress = self.targetAddress
        diagMsgHdr = bytearray()

        diagMsgHdr = diagMsgHdr + \
            U16ToByteArray(testerAddress) + U16ToByteArray(targetAddress)
        return diagMsgHdr


def exitHandler():
    TraceLocal("ExitHandler!!!")
    sys.stdout.flush()
    sys.stderr.flush()
    time.sleep(1)


atexit.register(exitHandler)
