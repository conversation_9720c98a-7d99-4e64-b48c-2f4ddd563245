#*****************************************************************************
# C O P Y R I G H T
#-----------------------------------------------------------------------------
# Copyright (c) 2006-2012 by Robert <PERSON>.            All rights reserved.
#
# This file is property of Robert Bosch GmbH. Any unauthorised copy, use or 
# distribution is an offensive act against international law and may me 
# prosecuted under federal law. Its content is company confidential.
#-----------------------------------------------------------------------------
# D E S C R I P T I O N
#-----------------------------------------------------------------------------
#     $ProjectName: d:/MKS_Data/Projects/TST/PP_GEN6/PF_SIP6/CarMakerHIL/script/script_shared.pj $ 
#     $Source: rb_reportFunctions.tcl $ 
#     $Revision: 1.5 $ 
#     $Date: 2016/09/06 09:20:44CEST $  
#     $State: in_work $ 
#
#
#    Purpose:        Script to create test report for CM HIL TestRuns
#                    
#    Target system:   Windows XP/ Windows7/ Windows 10
#    Compiler:        tcl   
#-----------------------------------------------------------------------------
# N O T E S
#-----------------------------------------------------------------------------
#     Notes:
#-----------------------------------------------------------------------------
# A U T H O R   I D E N T I T Y
#-----------------------------------------------------------------------------
#      Network account       Name                   Department
#      ----------------      --------------------   ---------------------------
#                            Stephan Stühmer        AE-BE/EPM5
#
#******************************************************************************

proc ExecuteTestManager { UseCANape CANapeProjectDir TestRunArray UserName MDFStorageDir} {
	global Qu
 
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
 # Begin script
 #--------------------------------------------------------------------------------------------------------------
	StopSim
	WaitForStatus idle
	set StartDateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d_%H-%M-%S}]
	set ProjectFolder [pwd]
 
	
	set MdfFolder [format "%s" $MDFStorageDir]
	set MeasurementSubFolder $StartDateTime
	
	set openedGroups 0
 
 #--------------------------------------------------------------------------------------------------------------
 # XML: write header
 #--------------------------------------------------------------------------------------------------------------
	set dummy [file mkdir ../../../HIL-Automation/TestReports]
	set ReportFileName	[format "report_%s" $StartDateTime]
	
	WriteXmlHeader $ReportFileName $UserName
	
	#
	set TestModuleStatusId [open "../../../HIL-Automation/Test_Report_Scripts/TestModuleStatus_TEMP.txt" "w"]
	puts $TestModuleStatusId "pass"
	close $TestModuleStatusId
	
	# open one group
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
 # TestRun Control
 #--------------------------------------------------------------------------------------------------------------
 #--------------------------------------------------------------------------------------------------------------
	set numGroups [expr [llength $TestRunArray]/2]
	set NumTotalRuns 0
	for {set runs 0} {$runs < $numGroups} {incr runs} {
		incr NumTotalRuns [lrange $TestRunArray [expr $runs*2+1] [expr $runs*2+1]]
	}
	set NumStartedRuns 0
	set groupCounter 0
	while {$groupCounter < $numGroups} {
		#-Group---------------------------------------
		set TestRunName [lrange $TestRunArray [expr $groupCounter*2] [expr $groupCounter*2]]
		set repetitions [lrange $TestRunArray [expr $groupCounter*2+1] [expr $groupCounter*2+1]]
		#
		
		WriteNewTestGroupXML $ReportFileName $TestRunName
		
		incr openedGroups
		set repetitionCounter 1
				
				
		set TestGroupStatusId [open "../../../HIL-Automation/Test_Report_Scripts/TestGroupStatus_TEMP.txt" "w"]
		puts $TestGroupStatusId "pass"
		close $TestGroupStatusId
		while {$repetitionCounter <= $repetitions} {
			incr NumStartedRuns
		
			LoadAndRunTestRun $ReportFileName $repetitionCounter $repetitions $TestRunName $NumStartedRuns $NumTotalRuns
			
			incr repetitionCounter
		}

		CloseTestGroupXML $ReportFileName		
		
		incr openedGroups -1
		incr groupCounter
	}
	

# I think this is not needed!	
#--------------------------------------------------------------------------------------------------------------
# XML: close TestGroups
#--------------------------------------------------------------------------------------------------------------
#	for {set dummy 0} {$openedGroups > 0} {incr openedGroups -1} {
#		puts $ReportFileId "\t</testgroup>"
#	}


	CloseTestModuleXML $ReportFileName

	file delete -force "../../../HIL-Automation/Test_Report_Scripts/TestGroupStatus_TEMP.txt"
	file delete -force "../../../HIL-Automation/Test_Report_Scripts/TestModuleStatus_TEMP.txt"
	
 #--------------------------------------------------------------------------------------------------------------
 # CANape
 #--------------------------------------------------------------------------------------------------------------
	if {$UseCANape == 1} {
		$CANapeApp QuitNonModal
	}
}

proc getRepositoryPath {}  {

	set script_path [ file dirname [ file normalize [ info script ] ] ]

	Log "Tickle Path: %s" $script_path
	
	return $script_path
}


proc PrepareCanape {UseCANape MDFStorageDir CANapeProjectDir StartDateTime}  {
 #--------------------------------------------------------------------------------------------------------------
 # CANape
 #--------------------------------------------------------------------------------------------------------------
	set dir "../../../HIL-Automation/Data/TestRun/Script/tcom"
	#pkg_mkIndex $dir
	package ifneeded tcom 3.9 [list load [file join $dir tcom.dll]]
	package require tcom 
	#set MdfFolder [format "%s/SimOutput/rt1/CANapeMeasurement" $ProjectFolder]
	set MdfFolder [format "%s" $MDFStorageDir]
	set MeasurementSubFolder $StartDateTime
	if {$UseCANape == 1} {
		if {[info exists CANapeApp] == 1} {
			unset CANapeApp
		}
		Log "Creating COM-object canape.Application..."
		set CANapeApp [::tcom::ref createobject "canape.Application"]
		#if { [catch [set CANapeApp [::tcom::ref createobject "canape.Application"]]] } {
		#	Log "Error while creating COM-object canape.Application"
		#	#$CANapeApp QuitNonModal
		#} else {
		#	Log "Successfully created COM-object canape.Application"
		#}
		Log "Opening CANape project..."
		if { [catch [$CANapeApp Open $CANapeProjectDir 1]] } {
			Log "Error while opening CANape project in %s" $CANapeProjectDir
			set UseCANape 0
			set CANapeMeasurement 0
			$CANapeApp QuitNonModal
		} else {
			Log "Successfully set CANape project directory to %s" $CANapeProjectDir
			Log "Creating COM-object CANape Measurement..."
			if { [catch [set CANapeMeasurement [$CANapeApp Measurement]]] } {
				Log "Created COM-object CANape Measurement but caught error though. Proceeding..."
				#set UseCANape 0
				#set CANapeMeasurement 0
				#$CANapeApp QuitNonModal
			} else {
				Log "Successfully created COM-object CANape Measurement"
			}
		}
		
		
		#$CANapeApp Open $CANapeProjectDir 1
		
		
		
		#set MdfFolderRel2Prj
		
		
	} else {
		set CANapeMeasurement 0
	}
	Log "UseCANape = %d" $UseCANape
	#Log $CANapeMeasurement
	
	return $CANapeMeasurement

}

proc WriteXmlHeader {ReportFileName UserName}    {

	set repoPath [getRepositoryPath]
	Log "RepositoryPath: %s" $repoPath
	
	set ReportFileNameXml [format "%sHIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName]
	#Log "ReportFileName: %s" $ReportFileNameXml
	set ReportFileId [open $ReportFileNameXml "w"]
	fconfigure $ReportFileId -blocking 0
	puts $ReportFileId "<?xml version='1.0' encoding='ISO-8859-1' standalone='yes' ?>"
	set data [format "<testmodule starttime='%s' timestamp='0.000000'>" [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]]
	puts $ReportFileId $data
	puts $ReportFileId "<engineer>"
	puts $ReportFileId "\t<xinfo>"
	set user_name [file tail $UserName]
	set data [format "\t\t<name>%s</name>" $user_name]
	puts $ReportFileId $data
	puts $ReportFileId "\t\t<description></description>"
	puts $ReportFileId "\t</xinfo>"
	puts $ReportFileId "</engineer>"
	puts $ReportFileId "<testsetup>"
	puts $ReportFileId "\t<xinfo>"
	puts $ReportFileId "\t\t<name>Testtyp</name>"
	puts $ReportFileId "\t\t<description>HIL</description>"
	puts $ReportFileId "\t</xinfo>"
	puts $ReportFileId "</testsetup>"
	
	close $ReportFileId

}

proc WriteNewTestGroupXML {ReportFileName TestRunName}  {

	set repoPath [getRepositoryPath]
	set ReportFileNameXml [format "%sHIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName]
	set ReportFileId [open $ReportFileNameXml "a"]
	fconfigure $ReportFileId -blocking 0

	puts $ReportFileId "\t<testgroup>"
	puts $ReportFileId [format "\t\t<title>%s</title>" $TestRunName]
	
	close $ReportFileId
}

proc CloseTestGroupXML {ReportFileName}    {
		global Qu
		
	set repoPath [getRepositoryPath]
	
	set ReportFileNameXml [format "%sHIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName]
	set ReportFileId [open $ReportFileNameXml "a"]
	fconfigure $ReportFileId -blocking 0

	set dateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]
	#read TestGroup Status
	# set TgsId [open "../../../HIL-Automation/Test_Report_Scripts/TestGroupStatus_TEMP.txt" "r"]
	# set TestGroupStatus [read -nonewline $TgsId]
	# close $TgsId
	# set data [format "\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $TestGroupStatus]
	# puts $ReportFileId $data
	puts $ReportFileId "\t</testgroup>"
	
	close $ReportFileId

}

proc CloseTestModuleXML {ReportFileName}   {
	global Qu
	
	set repoPath [getRepositoryPath]
	
	set ReportFileNameXml [format "%sHIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName]
    set ReportFileNameXmlRelative [format "%s/HIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName] 

	set ReportFileId [open $ReportFileNameXmlRelative "a"]
	fconfigure $ReportFileId -blocking 0

	#--------------------------------------------------------------------------------------------------------------
	# XML: close TestModule
	#--------------------------------------------------------------------------------------------------------------
	set dateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]
	#read TestModule Status
	set TestModuleStatus_TEMP [format "%sHIL-Automation/Test_Report_Scripts/TestModuleStatus_TEMP.txt" $repoPath]
	set TmsId [open $TestModuleStatus_TEMP "r"]
	set TestModuleStatus [read -nonewline $TmsId]
	close $TmsId
	set data [format "\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $TestModuleStatus]
	puts $ReportFileId $data
	puts $ReportFileId "</testmodule>"
	close $ReportFileId
	#create HTML from XML via XSLT
	set ReportFileNameHtml [format "%sHIL-Automation/TestReports/%s.html" $repoPath $ReportFileName]
	set msxsl [format "%sHIL-Automation/util/msxsl.exe" $repoPath]
	set onepage [format "%sHIL-Automation/util/onepage.xslt" $repoPath]
	exec $msxsl $ReportFileNameXmlRelative $onepage -o $ReportFileNameHtml

}

proc closeOneTestModuleXML {ReportFileName}   {
	global Qu
	
	set repoPath [getRepositoryPath]
	
	set ReportFileNameXml [format "%sHIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName]
    set ReportFileNameXmlRelative [format "%s/HIL-Automation/TestReports/%s.xml" $repoPath $ReportFileName] 

	set ReportFileId [open $ReportFileNameXmlRelative "a"]
	fconfigure $ReportFileId -blocking 0

	#--------------------------------------------------------------------------------------------------------------
	# XML: close TestModule
	#--------------------------------------------------------------------------------------------------------------
	set dateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]
	set data [format "\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='-' />" $dateTime $Qu(Time) $dateTime $Qu(Time) ]
	puts $ReportFileId $data

	puts $ReportFileId "</testmodule>"
	close $ReportFileId
	#create HTML from XML via XSLT
	set ReportFileNameHtml [format "%sHIL-Automation/TestReports/%s.html" $repoPath $ReportFileName]
	set msxsl [format "%sHIL-Automation/util/msxsl.exe" $repoPath]
	set onepage [format "%sHIL-Automation/util/onepage.xslt" $repoPath]
	exec $msxsl $ReportFileNameXmlRelative $onepage -o $ReportFileNameHtml

}


proc WriteTestRunPreconditionReport {ReportFileName TestRunName }    {
	global result_precond
	set result_precond "pass"

    set TestrunFileName [file tail $TestRunName]
    
    set ReportFileNameXml [format "%s" $ReportFileName]
    set ReportFileId [open $ReportFileNameXml "a"]
    fconfigure $ReportFileId -blocking 0


    set data [format "\t\t<testgroup starttime='%s' timestamp='0.000000'>" [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]]
    puts $ReportFileId $data
    puts $ReportFileId [format "\t\t\t<title>%s</title>" $TestrunFileName]
    
    set data [format "\t\t\t<testcase starttime='%s' timestamp='0.000000'>" [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]]
    puts $ReportFileId $data
    puts $ReportFileId [format "\t\t\t<title>%s</title>" $TestrunFileName]
    
    close $ReportFileId
}

proc WriteTestRunPostconditionReport {ReportFileName} {
    global result_precond
    global result_teststeps
    global Qu

    set result_postcond "pass"
    set dateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]
    set ReportFileNameXml [format "%s" $ReportFileName]
    set ReportFileId [open $ReportFileNameXml "a"]
    fconfigure $ReportFileId -blocking 0
    

    if {
        $result_precond == "fail"
        || $result_postcond == "fail"
    } then {set result_teststeps "fail"}
    
    set data [format "\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $result_teststeps]
    puts $ReportFileId $data
    puts $ReportFileId "\t\t\t</testcase>"
    set data [format "\t\t<verdict result='%s' />" $result_teststeps]
    puts $ReportFileId $data
    puts $ReportFileId "\t\t</testgroup>"
    
    close $ReportFileId
	set repoPath [getRepositoryPath]
	set TestStepResultId [open [format "%seach_testrun_result.txt" $repoPath] "w"]
	puts $TestStepResultId $result_teststeps
	close $TestStepResultId

}

proc LoadAndRunTestRun {ReportFileName TestRunName}  {
	
	set repoPath [getRepositoryPath]
	
	set ReportFileNameXml [format "%s" $ReportFileName]
	set ReportFileId [open $ReportFileNameXml "a"]
	fconfigure $ReportFileId -blocking 0

	Log "Start TestRun  in group %s."  $TestRunName
	set loadTestRunResult [LoadTestRun $TestRunName]


	if {[string compare $loadTestRunResult ""] == 0} {
		# make variation
		set TestRunVariationSring [format " " ]
			
		set TestRunStatus [StartLoadedTestRunWithReport $ReportFileId $TestRunName $TestRunVariationSring ]
		
		flush $ReportFileId
			
		if {$TestRunStatus == 0} {
			set TestRunStatusString "pass"
		} else {
			if  { $TestRunStatus > 0 } {
				set TestRunStatusString "warn"
				set TestGroupStatusId [open [format "%sTestGroupStatus_TEMP.txt" $repoPath] "w"]
				puts $TestGroupStatusId "warn"
				close $TestGroupStatusId
				set TestModuleStatusId [open [format "%sTestModuleStatus_TEMP.txt" $repoPath] "w"]
				puts $TestModuleStatusId "warn"
				close $TestModuleStatusId
			} else {
				set TestRunStatusString "fail"
				set TestGroupStatusId [open [format "%sTestGroupStatus_TEMP.txt" $repoPath] "w"]
				puts $TestGroupStatusId "fail"
				close $TestGroupStatusId
				set TestModuleStatusId [open [format "%sTestModuleStatus_TEMP.txt" $repoPath] "w"]
				puts $TestModuleStatusId "fail"
				close $TestModuleStatusId
			}
		}
		Log "Completed TestRun: %s\n"  $TestRunStatusString
	} else {
		Log "Could not load this TestRun."
		set TestGroupStatusId [open [format "%sTestGroupStatus_TEMP.txt" $repoPath] "w"]
		puts $TestGroupStatusId "fail"
		close $TestGroupStatusId
		set TestModuleStatusId [open [format "%sTestModuleStatus_TEMP.txt" $repoPath] "w"]
		puts $TestModuleStatusId "fail"
		close $TestModuleStatusId
	}
	
	close $ReportFileId
}


proc StartLoadedTestRunWithReport { ReportFileId testRunName variationString } {
    global result_teststeps
	global Qu
	# TestRun parameters
	#set TestRunName "Testrun_automatic"
	set TestRunVariation $variationString
	set TestRunName $testRunName
	#set ReportFileId $reportFileFid
	set dateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]
	#--------------------------------------------------------------------------------------------------------------
	# read maneuver info before starting the TestRun cause it takes quite a while
	#--------------------------------------------------------------------------------------------------------------
	set tempString "DrivMan.nDMan"
	set DMnDMan [IFileRead TestRun $tempString -1]
	array unset DrivMan*
	for {set k 0} {$k < $DMnDMan} {incr k 1} {
		set tempString [format "DrivMan.%d.Info" $k]
		set DrivMan($k,Info) [IFileRead TestRun $tempString "(no description)"]
		set tempString [format "DrivMan.%d.Label" $k]
		set DrivMan($k,Label) [IFileRead TestRun $tempString "(no label)"]
		set tempString [format "DrivMan.%d.Label" $k]
		set DrivMan($k,Label) [IFileRead TestRun $tempString "(no label)"]
		set tempString [format "DrivMan.%d.TimeLimit" $k]
		set DrivMan($k,TimeLimit) [IFileRead TestRun $tempString -1]
		set tempString [format "DrivMan.%d.DistLimit" $k]
		set DrivMan($k,DistLimit) [IFileRead TestRun $tempString -1]
		set tempString [format "DrivMan.%d.EndCondition" $k]
		set DrivMan($k,EndCondition) [IFileRead TestRun $tempString -1]
		set tempString [format "DrivMan.%d.Cmds" $k]
		set DrivMan($k,Cmds) [IFileRead TestRun $tempString ""]
	}

	#--------------------------------------------------------------------------------------------------------------------
	# store erg-file when test failed
	#--------------------------------------------------------------------------------------------------------------------
	SaveMode collect
	#--------------------------------------------------------------------------------------------------------------
	# start the TestRun
	#--------------------------------------------------------------------------------------------------------------
	QuantSubscribe {}
	sleep 10000
	StartSim
	Log "Now start Sim Test Run "
	
	#--------------------------------------------------------------------------------------------------------------
	# XML: open TestRun
	#--------------------------------------------------------------------------------------------------------------
	set ManeuverMonitoring 1
	set ManeuverMonitoringStatus 0
	set SimulationStatus 0
	
	set OverallTestRunStatus 0
	set warn 0
	QuantSubscribe { Time DM.ManNo DM.ManStartTime DM.ManStartDist}
	WaitForStatus running
	Log [format "TestRun %s - %s started." $TestRunName $TestRunVariation]
	
	
	set lastManNo -1
	set lastManDist -1
	set lastManTime -1
	set lastManStartTime 0
	set lastManStartDist 0
	set TestFeature 0 
	#--------------------------------------------------------------------------------------------------------------
	# react on maneuver changes
	#--------------------------------------------------------------------------------------------------------------
	while {[SimStatus] >= 0} {
		if {$SimulationStatus != 1} {
			Log "I am here"
			set data [format "\t\t\t\t<simstatus timestamp='%f' result='na'>SimulationStatus = %d</simstatus>" $Qu(Time) [SimStatus]]
			puts $ReportFileId $data
			flush $ReportFileId
			set SimulationStatus 1
				}
		if {$lastManNo != $Qu(DM.ManNo) && $Qu(DM.ManNo) >= 0} {
			set CurrentManNo $Qu(DM.ManNo)
			if {$lastManNo != -1} {
				if {$ManeuverMonitoring != 1} {
					puts $ReportFileId [format "\t\t\t<testcase starttime='%s' timestamp='%s'>" $dateTime $Qu(Time)]
					puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
					set ManeuverMonitoring 1
				}
				#--------------------------------------------------------------------------------------------------------------
				# determine reason for maneuver change
				#--------------------------------------------------------------------------------------------------------------
				QuantSubscribe { DM.ManStartTime DM.ManStartDist }
				set ManEndDueToDistOrTime 0
				#--------------------------------------------------------------------------------------------------------------
				# TimeLimit reached?
				#--------------------------------------------------------------------------------------------------------------
				if {$DrivMan($lastManNo,TimeLimit) != -1 } {	
					if {$Qu(DM.ManStartTime) >= [expr $DrivMan($lastManNo,TimeLimit) + $lastManStartTime]} {
						Log "ManChange due to Time"
						incr ManEndDueToDistOrTime
						set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>ManChange due to TimeLimit: %.2fs</teststep>" $Qu(Time) $DrivMan($lastManNo,TimeLimit)]
						puts $ReportFileId $data
					}
				}
				#--------------------------------------------------------------------------------------------------------------
				# DistanceLimit reached?
				#--------------------------------------------------------------------------------------------------------------
				if {$DrivMan($lastManNo,DistLimit)!= -1 } {
					if { [expr $Qu(DM.ManStartDist)] >= [expr $DrivMan($lastManNo,DistLimit) + $lastManStartDist]} {
						Log "ManChange due to Distance"
						incr ManEndDueToDistOrTime
						set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>ManChange due to DistanceLimit: %.2fm</teststep>" $Qu(Time) $DrivMan($lastManNo,DistLimit)]
						puts $ReportFileId $data
					}
				}
				#--------------------------------------------------------------------------------------------------------------
				# assume Condition met when not Time- and not DistanceLimit
				#--------------------------------------------------------------------------------------------------------------
				if {$DrivMan($lastManNo,EndCondition) != -1 && $ManEndDueToDistOrTime == 0} {
					Log "ManChange due to End Condition"
					#regsub -all {[>]} "&gt;" $ManEndCondition
					#regsub -all {[<]} "&lt;" $ManEndCondition
					set ManEndCondition [string map {"<" "&lt;" ">" "&gt;" "&" "&amp;"} $DrivMan($lastManNo,EndCondition)]
					set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>ManChange due to End Condition: %s</teststep>" $Qu(Time) $ManEndCondition]
					puts $ReportFileId $data
				}
				#--------------------------------------------------------------------------------------------------------------
				# warn when not Time- and not DistanceLimit and no Condition is specified
				#--------------------------------------------------------------------------------------------------------------
				#if {$DrivMan($lastManNo,EndCondition) == -1 && $ManEndDueToDistOrTime == 0} {
					#set LogString "Unknown reason for maneuver change."
					#if {$DrivMan($lastManNo,TimeLimit) != -1 } {	
					#	set LogString [format "%s Time = %f; TimeLimit = %f" $LogString [expr $Qu(DM.ManStartTime) - $lastManStartTime] $DrivMan($lastManNo,TimeLimit)]
					#}
					#if {$DrivMan($lastManNo,DistLimit) != -1 } {	
					#	set LogString [format "%s Dist = %f; DistLimit = %f" $LogString [expr $Qu(DM.ManStartDist) - $lastManStartDist] $DrivMan($lastManNo,DistLimit)]
					#}
					#Log $LogString 
					#set data [format "\t\t\t\t<teststep timestamp='%f' result='warn'>%s</teststep>" $Qu(Time) $LogString]
					#puts $ReportFileId $data
				#}
				set lastManStartTime $Qu(DM.ManStartTime)
				set lastManStartDist $Qu(DM.ManStartDist)
			}
			#--------------------------------------------------------------------------------------------------------------
			# print maneuver info
			#--------------------------------------------------------------------------------------------------------------
			set LogString [format "ManNo %d: %s - %s" $Qu(DM.ManNo) $DrivMan($CurrentManNo,Label) $DrivMan($CurrentManNo,Info)]
			Log $LogString
			set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>%s</teststep>" $Qu(Time) $LogString]
			puts $ReportFileId $data
			set lastManNo $CurrentManNo
			#--------------------------------------------------------------------------------------------------
			# parse TestEvents Keyword
			#--------------------------------------------------------------------------------------------------
			set ValidCmdNames [list "\{#TestEvent\}" "\{#TE\}" "\{#TEA\}" "\{#TestFeature\}" "\{#TF\}" "\{#TFA\}"]
			set FeatureCmds [list "\{#TestFeature\}" "\{#TF\}" "\{#TFA\}"]
			set AbortCmds [list "\{#TEA\}" "\{#TFA\}"]
			set TestFeature 0
			set numWordsInCmd [llength $DrivMan($CurrentManNo,Cmds)]
			for {set k 0} {$k < $numWordsInCmd} {incr k 1} {
				#get current commands of current mini maneuver
				set CmdName [lrange $DrivMan($CurrentManNo,Cmds) $k $k]
				# check if there is a Bosch test manager command within (e.g. #TestEvent)
				if { [lsearch -exact $ValidCmdNames $CmdName] >= 0 } {
					#is test event mapped to a feature? (e.g. #TestFeature)
					if {[lsearch -exact $FeatureCmds $CmdName] >= 0} {
						if {$ManeuverMonitoringStatus == 0} {
								set ManeuverMonitoringStatusString "pass"
						} else {
							if {$ManeuverMonitoringStatus < 0} {
								set ManeuverMonitoringStatusString "fail"
							} else {
								set ManeuverMonitoringStatusString "warn"
							}
						}
						if {$ManeuverMonitoring == 1} {
							#close testcase "maneuver monitoring" when necessary	
							puts $ReportFileId [format "\t\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s'/>" $dateTime $Qu(Time) $dateTime $Qu(Time) $ManeuverMonitoringStatusString]
							# puts $ReportFileId "\t\t\t</testcase>"
							set ManeuverMonitoring 0
						}
						puts $ReportFileId [format "\t\t\t<testcase starttime='%s' timestamp='%s'>" $dateTime $Qu(Time)]
						set TestFeature 1
					} else {
						# no testcases/testfeature-commands in this maneuver
						if {$ManeuverMonitoring == 0} {
							set ManeuverMonitoringStatus 0
							puts $ReportFileId "\t\t\t<testcase>"
							puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
							set ManeuverMonitoring 1
						}
						set TestFeature 0
					}
					# shall CarMakerRun be aborted on test-failure?
					if {[lsearch -exact $AbortCmds $CmdName] >= 0} {
						set TestAbort 1
					} else {
						set TestAbort 0
					}
					set KeyWordIdx [expr $k + 1]
					set ArgIdx [expr $KeyWordIdx + 1]
					set KeyWord [lrange $DrivMan($CurrentManNo,Cmds) $KeyWordIdx $KeyWordIdx]
					switch $KeyWord {
						"QL" { set KeyWord "QuantLog" }
						"QCE" { set KeyWord "QuantCheckEqual" }
						"QCNE" { set KeyWord "QuantCheckNotEqual" }
						"QCEQ" { set KeyWord "QuantCheckEqualQuant" }
						"QCNEQ" { set KeyWord "QuantCheckNotEqualQuant" }
						"QCLT" { set KeyWord "QuantCheckLessThan" }
						"QCGT" { set KeyWord "QuantCheckGreaterThan" }	
						"QCR" { set KeyWord "QuantCheckRange" }			
						"QCRW" { set KeyWord "QuantCheckRangeWithWarning" }						
					}
					set KeyWordValid 1
					switch $KeyWord {
						"TargetPos" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Evaluate Target Position
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [EvalTargetPos $ReportFileId]
							incr ArgIdx -1
						}
						
						"TargetPos_cPSC" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Evaluate Target Position
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [EvalTargetPos_cPSC $ReportFileId]
							incr ArgIdx -1
						}
						
						"Scene" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Evaluate Target Position
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [CheckDesiredSceneAttributes $ReportFileId]
							incr ArgIdx -1
						}
						
						"FinishingStraightMove" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent FinishingStraightMove
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [CheckDistanceFrontRear $ReportFileId]
							incr ArgIdx -1
						}
						
						"FrontWheelAngle" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent FinishingStraightMove
							#--------------------------------------------------------------------------------------------------
							set KeyWordResult [CheckFrontWheelAngle $ReportFileId]
							incr ArgIdx -1
						}
					
						"Comment" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Comment
							#--------------------------------------------------------------------------------------------------
							set LogString [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
						}
						
						"QuantLog" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent IO_CAN.signal
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							# dummy start
							# no functional background, we just need to have some dummy operations before we access the subscribed quantity
							incr ArgIdx
							set dummy_joc $ArgIdx
							# dummy end
							set LogString [format "Log value condition: %s = %f" $QuantName $Qu($QuantName)]
							Log $LogString
							set KeyWordResult "na"
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckEqual" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) == $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; expected = %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckNotEqual" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) != $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; not allowed = %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckLessThan" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) < $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; must be less than %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckGreaterThan" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) > $ReferenceValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; must be greater than %f" $QuantName $Qu($QuantName) $ReferenceValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckRange" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceMinValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMaxValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) >= $ReferenceMinValue && $Qu($QuantName) <= $ReferenceMaxValue} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; must be in range %f &lt; x &lt; %f " $QuantName $Qu($QuantName) $ReferenceMinValue $ReferenceMaxValue]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckRangeWithWarning" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity
							#--------------------------------------------------------------------------------------------------
							set QuantName [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe $QuantName
							incr ArgIdx
							set ReferenceMinValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMinValueWarn [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMaxValueWarn [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set ReferenceMaxValue [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {$Qu($QuantName) >= $ReferenceMinValue && $Qu($QuantName) < $ReferenceMinValueWarn} {
								set KeyWordResult "warn"
								incr warn
							} elseif {$Qu($QuantName) > $ReferenceMaxValueWarn && $Qu($QuantName) <= $ReferenceMaxValue} {
								set KeyWordResult "warn"
								incr warn
							} elseif {$Qu($QuantName) >= $ReferenceMinValueWarn && $Qu($QuantName) <= $ReferenceMaxValueWarn} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							if {$KeyWordResult == "fail"} {
								set LogString [format "Check value condition: %s = %f; must be in range %f &lt; x &lt; %f " $QuantName $Qu($QuantName) $ReferenceMinValue $ReferenceMaxValue]
							} else {
								set LogString [format "Check value condition: %s = %f; should be in range %f &lt; x &lt; %f " $QuantName $Qu($QuantName) $ReferenceMinValueWarn $ReferenceMaxValueWarn]
							}
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"QuantCheckEqualQuant" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity vs. Quantity
							#--------------------------------------------------------------------------------------------------
							set Quant1Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Quant2Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe { $Quant1Name $Quant2Name }
							if {$Qu($Quant1Name) == $Qu($Quant2Name)} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; expected = %f" $Quant1Name $Qu($Quant1Name) $Qu($Quant2Name)]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}

						
						"QuantCheckNotEqualQuant" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Quantity vs. Quantity
							#--------------------------------------------------------------------------------------------------
							set Quant1Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Quant2Name [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							QuantSubscribe { $Quant1Name $Quant2Name }
							if {$Qu($Quant1Name) != $Qu($Quant2Name)} {
								set KeyWordResult "pass"
							} else {
								set KeyWordResult "fail"
								incr OverallTestRunStatus -1
							}
							set LogString [format "Check value condition: %s = %f; not allowed = %f" $Quant1Name $Qu($Quant1Name) $Qu($Quant2Name)]
							Log "%s: %s" $LogString $KeyWordResult
							set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>&#160;&#160;&#160;&#160;%s</teststep>" $Qu(Time) $KeyWordResult $LogString]
							puts $ReportFileId $data
						}
						
						"Blink" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Blink 
							#--------------------------------------------------------------------------------------------------
							set BlinkString [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							if {[string compare $BlinkString "Left"] == 0} {
								DVAWrite RB.indicator.en.r 0 1
								DVAWrite RB.indicator.en.l 1 1
							}
							if {[string compare $BlinkString "Right"] == 0} {
								DVAWrite RB.indicator.en.l 0 1
								DVAWrite RB.indicator.en.r 1 1
							}
							if {[string compare $BlinkString "Both"] == 0} {
								DVAWrite RB.indicator.en.l 1 1
								DVAWrite RB.indicator.en.r 1 1
							}
							if {[string compare $BlinkString "Off"] == 0} {
								DVAWrite RB.indicator.en.l 0 1
								DVAWrite RB.indicator.en.r 0 1
							}
							set LogString [format "Set Blinker: %s" $BlinkString]
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
						}
						
						"PushParkButton" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Push Park-Button 
							#--------------------------------------------------------------------------------------------------
							set PushTime [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							DVAWrite RB.ParkButton.ActualState 1 1
							DVAWrite RB.ParkButton.EndState 0 1
							DVAWrite RB.ParkButton.EndTimeMs $PushTime 1
							set LogString [format "Push ParkButton, hold for %d ms" $PushTime]
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
							incr ArgIdx -1
						}
						
						"ActivatePsxViaHmiMenu" {
							#--------------------------------------------------------------------------------------------------
							# TestEvent Activate PSX via HMI
							#--------------------------------------------------------------------------------------------------
							set PushTime [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							DVAWrite RB.ParkCmds.ActivatePsxViaHmiMenu 1 1
							set LogString "Activate PSX via HMI menu."
							Log $LogString
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordResult "na"
						}
						
						"DoorsLink" {
							set LinkTitle [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Link [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							set Link [string map {"&" "&amp;amp;"} $Link]
							Log "DoorsLink %s %s" $LinkTitle $Link
							set data [format "\t\t\t\t<externalref type='doors' title='%s'>%s</externalref>" $LinkTitle $Link]
							puts $ReportFileId $data
							set KeyWordResult "na"
							incr ArgIdx -1
						}
						
						"ALMLink" {
							set LinkTitle [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							incr ArgIdx
							set Link [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							set Link [string map {"&" "&amp;"} $Link]
							Log "ALMLink %s %s" $LinkTitle $Link
							set data [format "\t\t\t\t<externalref type='alm' title='%s'>%s</externalref>" $LinkTitle $Link]
							puts $ReportFileId $data
							set KeyWordResult "na"
							incr ArgIdx -1
						}
						
						
						default {
							Log "Unknown TestEvent keyword: %s" [lrange $DrivMan($CurrentManNo,Cmds) $KeyWordIdx $KeyWordIdx] 
							set data [format "\t\t\t\t<teststep timestamp='%f' result='na'>&#160;&#160;&#160;&#160;%s </teststep>" $Qu(Time) $LogString]
							puts $ReportFileId $data
							set KeyWordValid 0
						}
					}
					#--------------------------------------------------------------------------------------------------
					# was test event mapped to a feature?
					#--------------------------------------------------------------------------------------------------
					if {$TestFeature == 1} {
						#TestFeature
						if {$KeyWordValid == 1} {
							puts $ReportFileId [format "\t\t\t\t<title>%s %s</title>" "Test feature" $KeyWord]
							puts $ReportFileId [format "\t\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s'/>" $dateTime $Qu(Time) $dateTime $Qu(Time) $KeyWordResult]
							incr ArgIdx
							set DoorsLink [lrange $DrivMan($CurrentManNo,Cmds) $ArgIdx $ArgIdx]
							set DoorsLink [string map {"&" "&amp;amp;"} $DoorsLink]
							Log "%s" $DoorsLink
							
							if {[string first "doors" $DoorsLink] == 0} {
								puts $ReportFileId [format "\t\t\t\t<externalref type='doors' title='%s'>%s</externalref>" $KeyWord $DoorsLink]
								
							} elseif {[string first "https" $DoorsLink] == 0} {
								puts $ReportFileId [format "\t\t\t\t<externalref type='url' title='%s'>%s</externalref>" $KeyWord $DoorsLink]
							}
						} else {
							puts $ReportFileId [format "\t\t\t\t<title>Unknown KeyWord: %s %s</title>" $CmdName $KeyWord]
							puts $ReportFileId "\t\t\t\t<verdict result='na'/>"
						}
						# puts $ReportFileId "\t\t\t</testcase>"
					} else {
						#TestEvent
						if {[string compare $KeyWordResult "fail"] == 0} {
							incr ManeuverMonitoringStatus -1
						}
					}
					if {[string compare $KeyWordResult "fail"] == 0} {
						incr OverallTestRunStatus -1
					} else {
						if { [string compare $KeyWordResult "warn"] == 0 } {
							incr warn
						}
					}
					if {$TestAbort == 1 && [string compare $KeyWordResult "fail"]==0} {
						StopSim
					}
				}
			}
		}
		sleep 1
	}
	if {$ManeuverMonitoring == 0} {
		# If a testfeature was processed in the last iteration
		# there is already a close tag for the test case		
		# if {$TestFeature != 1} {
			# puts $ReportFileId "\t\t\t</testcase>"
		# }
		puts $ReportFileId "\t\t\t<testcase>"
		puts $ReportFileId "\t\t\t\t<title>Maneuver monitoring</title>"
		set ManeuverMonitoring 1
		set ManeuverMonitoringStatus 0
	}
	#--------------------------------------------------------------------------------------------------------------
	# determine reason for TestRunEnd
	#--------------------------------------------------------------------------------------------------------------
	set testStepStatus "pass"
	set tempString [format "DrivMan.%d.Label" $lastManNo]
	set lastManLabel [IFileRead TestRun $tempString "(no label)"]
	if {[string compare $lastManLabel "EndPP"] != 0} {
		set testStepStatus "fail"
		incr ManeuverMonitoringStatus -1
		incr OverallTestRunStatus -1
	}
	set LogString [format "TestRun ended in maneuver %d with label &apos;%s&apos;; expected label = &apos;EndPP&apos;" $lastManNo $lastManLabel]
	Log [format "%s : %s" $LogString $testStepStatus]
	set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>%s</teststep>" $Qu(Time) $testStepStatus $LogString]
	puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------------
	# check if collision happened
	#--------------------------------------------------------------------------------------------------------------------
	#QuantSubscribe RB.TargetPos.Collisions
	#set testStepStatus "pass"
	#if {$Qu(RB.TargetPos.Collisions) > 0} {
	#	set testStepStatus "fail"
	#	incr ManeuverMonitoringStatus -1
	#	incr OverallTestRunStatus -1
	#}
	#set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>Check value condition: detected collisions = %d; expected = 0</teststep>" $Qu(Time) $testStepStatus $Qu(RB.TargetPos.Collisions)]
	#puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------------
	# stop CANape measurement
	#--------------------------------------------------------------------------------------------------------------------
	#--------------------------------------------------------------------------------------------------------------------
	# manage erg-file (e.g. delete erg-file when test was successful, otherwise put link into report-file)
	#--------------------------------------------------------------------------------------------------------------------
	#if {$OverallTestRunStatus == 0} {
	#	file delete [GetLastResultFName]
	#	file delete [format "%s.info" [GetLastResultFName]]
	#} else {
	#	set data [format "\t\t\t\t<externalref type='url' title='Erg-file to re-render video'>%s</externalref>" [GetLastResultFName]]
	#	puts $ReportFileId $data
	#}
	#--------------------------------------------------------------------------------------------------------------------
	# check if an error with the test setup occurred
	#--------------------------------------------------------------------------------------------------------------------
	#QuantSubscribe RB.TargetPos.TestError
	#set testStepStatus "pass"
	#if {$Qu(RB.TargetPos.TestError) > 0} {
	#	set testStepStatus "fail"
	#	incr ManeuverMonitoringStatus -1
	#	incr OverallTestRunStatus -1
	#}
	#set data [format "\t\t\t\t<teststep timestamp='%f' result='%s'>Check value condition: errors with test-setup = %d; expected = 0</teststep>" $Qu(Time) $testStepStatus $Qu(RB.TargetPos.TestError)]
	# puts $ReportFileId $data
	#--------------------------------------------------------------------------------------------------------------
	# XML: write result and end testcase
	#--------------------------------------------------------------------------------------------------------------
	set dateTime [clock format [clock seconds] -timezone +0800 -format {%Y-%m-%d %H:%M.%S}]
	if {$OverallTestRunStatus == 0 && $warn > 0} {
		set OverallTestRunStatus 1
		set OverallTestRunStatusString "warn"
	}
	if {$OverallTestRunStatus == 0 && $warn == 0} {
		set OverallTestRunStatusString "pass"
	}
	if {$OverallTestRunStatus < 0} {
			set OverallTestRunStatusString "fail"
	}
	
	if {$ManeuverMonitoringStatus >= 0} {
		if {$warn > 0} {
			set ManeuverMonitoringStatusString "warn"
		} else {
			set ManeuverMonitoringStatusString "pass"
		}
	} else {
		set ManeuverMonitoringStatusString "fail"
	}
	
	
	# if {$ManeuverMonitoring == 1} {
		# set data [format "\t\t\t\t<verdict time='%s' timestamp='%f' endtime='%s' endtimestamp='%f' result='%s' />" $dateTime $Qu(Time) $dateTime $Qu(Time) $ManeuverMonitoringStatusString]
		# puts $ReportFileId $data
		# puts $ReportFileId "\t\t\t</testcase>"
	# }
	
	#set data [format "\t\t\t\t<externalref type='url' title='Erg-file to re-render video'>%s</externalref>" [GetLastResultFName]]
	# puts $ReportFileId $data
	
	# puts $ReportFileId "\t\t</testgroup>"
	set LogString  [format "TestRun %s entered postprocessing." $TestRunName]
	Log $LogString
	flush $ReportFileId
	
	WaitForStatus idle
	set LogString  [format "TestRun %s ended." $TestRunName]
	Log $LogString
	#
	# END TestRun-specific
	#
    if {$ManeuverMonitoring == 1} {
        set result_teststeps $ManeuverMonitoringStatusString
    } else {
        set result_teststeps "na"
    }
	return $OverallTestRunStatus
}

#=================================================================================================
# Revision history
# $Log: rb_reportFunctions.tcl  $
# Revision 1.5 2016/09/06 09:20:44CEST Staack Jochen (PS-PE/ESW1) (sj82rt) 
# added a line in the scriptso that the puts command is not working as blocking for the file it writes to anymore
# Revision 1.4 2015/11/03 12:33:03MEZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# removed targetpos.testerror evaluation (outdated)
# Revision 1.3 2015/10/06 09:39:38MESZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# support of TF as test event to enable direct links between checks and RQM testcases
# Revision 1.2 2015/09/22 10:59:13MESZ FIXED-TERM Sarikurt Batikaan (CC-DA/EAU3) (SAB7LR) 
# added new keyword ALMLink
# Revision 1.1 2015/01/12 08:35:07MEZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# Initial revision
# Member added to project d:/MKS_Data/Projects/TST/PP_GEN6/PF_SIP6/Tools/CarMaker/PP6_Common/script/script.pj
# Revision 1.6 2014/08/15 08:34:32MESZ Staack Jochen (CC-DA/EAU3) (sj82rt) 
# update for TestEvent Quantlog for stability with ScriptControl
# Revision 1.5 2014/01/24 15:22:20MEZ Staack Jochen (AE-BE/EPM4) (sj82rt) 
# added input variable MDFStorageDir
# 
# Testmanager script has to take care about this by executing the test manager like this
# # Executing the test manager
# ExecuteTestManager $UseCANape $CANapeProjectDir $TestRunArray $UserName $MDFStorageDir
# 
# also at the top of the TestManager (below path to canape) you have to define the storage path like this:
#  # Path to CANape measurement storage folder
#  set MDFStorageDir "D:/CANapeMeasurement"
# Revision 1.4 2013/07/30 17:08:38MESZ Stuehmer Stephan (AE-BE/EPM1) (STT1LR) 
# - changed "error" message
# Revision 1.2 2013/07/30 15:44:12MESZ Stuehmer Stephan (AE-BE/EPM1) (STT1LR) 
# - added error handling for CANape communication
# Revision 1.1 2013/05/21 11:09:22MESZ Stuehmer Stephan (AE-BE/EPM1) (STT1LR) 
# Initial revision
# Member added to project d:/MKS_Data/Projects/Tools/PP-Tools/pp_CarMaker/common/script/common_script.pj
# Revision 1.11 2013/02/26 08:29:01MEZ Staack Jochen (AE-BE/EPM4) (sj82rt) 
# fixed bug with warnings handling
# Revision 1.10 2013/02/25 15:21:17MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# added evaluation of warnings in testreport
# Revision 1.9 2013/01/31 15:11:20MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# fixed a bug with a doors link reference
# Revision 1.8 2013/01/09 11:27:49MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# - added test event QCRW "QuantityCheckRangeWarning"
# syntax #TE QCRW <fail_low> <warn_low> <warn_high> <fail_high>
# Revision 1.7 2012/11/08 13:17:05MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# - added file counter in filename for mdf files 
# - removed automatic collision check
# Revision 1.6 2012/11/05 14:51:14MEZ Staack Jochen (AE-BE/EPM5) (sj82rt) 
# -removed DVAWrite RB.TargetPos.EnableTesting 1 1 (has to be set in project specific TestManager.tcl)
# Revision 1.4 2012/06/05 18:13:31MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# - Support for CANape improved
# Revision 1.3 2012/05/23 16:00:12MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# - Support for triggering CANape measurement in maneuver control: TE TriggerCANape
# Revision 1.2 2012/04/27 11:07:26MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# - BugFix: TestFeature verdict for "Scene" is set correctly if TestStep results contain "warn" and "fail"
# Revision 1.1 2012/04/27 10:40:14MESZ Henke David (AE-BE/EPM5) (HEN1LR) 
# Initial revision
# Member added to project d:/MKS_Data/Projects/Tools/internal-tools/it_EPS5-AuxiliaryTools/PP_HIL/HIL_and_ValueHIL/GM/K2xx/Data/TestRun/Script/testrun_script.pj
#======================================== end of file ============================================

