import datetime
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, ForeignKey

Base = declarative_base()

class Testcase(Base):
    __tablename__ = "testcases"
    id = Column(Integer, primary_key=True, index=True)
    function = Column(String, nullable=False)
    degradation = Column(String, nullable=False)
    testrun_path = Column(String)
    owner = Column(String)
    cat_level = Column(String)
    safety_relevance = Column(Boolean, default=False)
    ready_for_test = Column(Boolean, default=False)
    req_id = Column(String)
    is_enabled = Column(Boolean, default=True)
    updated_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))

    tasks = relationship("Task", back_populates="testcase")  # 关联任务

class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    testcase_id = Column(Integer, ForeignKey("testcases.id"), nullable=False)
    batch_id = Column(String)
    priority = Column(Integer)
    status = Column(String)
    created_at = Column(DateTime, default=datetime.datetime.now(datetime.timezone.utc))
    retry_count = Column(Integer)
    record_type = Column(String)
    creator = Column(String)
    log_path = Column(String)
    result_summary = Column(String)
    done_by = Column(String)
    test_result = Column(String)
    start_at = Column(DateTime)
    done_at = Column(DateTime)

    testcase = relationship("Testcase", back_populates="tasks")  # 关联用例信息

    def to_dict(self):
        """返回字典形式的任务对象（可用于 JSON 序列化、打印）"""
        return {
            "id": self.id,
            "testcase_id": self.testcase_id,
            "batch_id": self.batch_id,
            "priority": self.priority,
            "status": self.status,
            "created_at": self.created_at,
            "retry_count": self.retry_count,
            "record_type": self.record_type,
            "creator": self.creator,
            "log_path": self.log_path,
            "result_summary": self.result_summary,
            "test_result": self.test_result,
            "done_by": self.done_by,
            "start_at": self.start_at,
            "done_at": self.done_at,
        }

    def __repr__(self):
        """用于开发调试时更友好的打印信息"""
        return f"<Task id={self.id}, testcase_id={self.testcase_id}, status={self.status}, creator={self.creator}>"