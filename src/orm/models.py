from datetime import datetime
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy import Column, Integer, String, DateTime

Base = declarative_base()

class Task(Base):
    __tablename__ = "tasks"

    id = Column(Integer, primary_key=True, autoincrement=True)
    testcase_id = Column(String)
    batch_id = Column(String)
    priority = Column(Integer)
    status = Column(String)
    created_at = Column(DateTime, default=datetime.utcnow)
    retry_count = Column(Integer)
    record_type = Column(String)
    creator = Column(String)
    log_path = Column(String)
    error_message = Column(String)
    done_by = Column(String)
    start_at = Column(DateTime)
    done_at = Column(DateTime)

    def to_dict(self):
        """返回字典形式的任务对象（可用于 JSON 序列化、打印）"""
        return {
            "id": self.id,
            "testcase_id": self.testcase_id,
            "batch_id": self.batch_id,
            "priority": self.priority,
            "status": self.status,
            "created_at": self.created_at,
            "retry_count": self.retry_count,
            "record_type": self.record_type,
            "creator": self.creator,
            "log_path": self.log_path,
            "error_message": self.error_message,
            "done_by": self.done_by,
            "start_at": self.start_at,
            "done_at": self.done_at,
        }

    def __repr__(self):
        """用于开发调试时更友好的打印信息"""
        return f"<Task id={self.id}, testcase_id={self.testcase_id}, status={self.status}, creator={self.creator}>"