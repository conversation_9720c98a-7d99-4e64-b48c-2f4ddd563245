import os

from contextlib import contextmanager
from urllib.parse import quote_plus
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import NullPool


class SingletonHolder(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(SingletonHolder, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class SessionManager(metaclass=SingletonHolder):
    def __init__(self, db_path: str = "sqlite.db"):
        db_url = f"sqlite:///{db_path}"
        self.engine = create_engine(
            db_url,
            echo=False,
            connect_args={"check_same_thread": False},  # 多线程场景需要
            poolclass=NullPool
        )
        self._Session = sessionmaker(bind=self.engine)

    @contextmanager
    def get_session(self) -> Session:
        """提供带上下文管理的 Session"""
        session = self._Session()
        try:
            yield session
        finally:
            session.close()

    def get_engine(self):
        return self.engine

    def dispose(self):
        """主动销毁数据库连接（在应用退出或不再使用时调用）"""
        self.engine.dispose()