import os

from sqlalchemy.exc import SQLAlchemyError

from src.orm.session_manager import SessionManager
from sqlalchemy.orm import joinedload
from src.orm.models import Task
from src.config import conf


class TaskDAO:
    def __init__(self):
        db_path = conf.get_config_value("sqlite_path")
        self.session_mgr = SessionManager(db_path)

    def create_task(self, **kwargs):
        with self.session_mgr.get_session() as session:
            try:
                task = Task(**kwargs)
                session.add(task)
                session.commit()
                return task
            except SQLAlchemyError as e:
                session.rollback()
                raise e

    def get_task_by_id(self, task_id: int): 
        with self.session_mgr.get_session() as session:
            return session.query(Task).options(joinedload(Task.testcase)).filter_by(id=task_id).first()

    def update_task_by_id(self, task_id: int, **kwargs):
        with self.session_mgr.get_session() as session:
            try:
                rows = session.query(Task).filter_by(id=task_id).update(kwargs)
                session.commit()
                return rows > 0
            except SQLAlchemyError as e:
                session.rollback()
                raise e

    def delete_task_by_id(self, task_id: int):
        with self.session_mgr.get_session() as session:
            try:
                rows = session.query(Task).filter_by(id=task_id).delete()
                session.commit()
                return rows > 0
            except SQLAlchemyError as e:
                session.rollback()
                raise e

    def query_tasks(self, **filters):
        with self.session_mgr.get_session() as session:
            query = session.query(Task)
            for k, v in filters.items():
                if hasattr(Task, k):
                    query = query.filter(getattr(Task, k) == v)
            return query.all()

    def close(self):
        """手动释放底层数据库连接池"""
        self.session_mgr.dispose()

    def __del__(self):
        """析构时自动释放资源"""
        try:
            self.close()
        except Exception:
            pass

if __name__ == "__main__":
    # db_path = r"C:\Users\<USER>\Projects\HiL\Jetour_自动化\hil_test_platform\back_end\tasks.db"
    dao = TaskDAO()

    task_id = 1
    # 查询任务
    task = dao.get_task_by_id(task_id)
    print(task)
    print(task.testcase.testrun_path)
    print(type(task.record_type))

    # 更新任务
    dao.update_task_by_id(task_id, status="running", retry_count=3)

    # 条件查询
    running_tasks = dao.query_tasks(status="running")
    print("Running tasks:", [t.id for t in running_tasks])

    # 删除任务
    # dao.delete_task_by_id(task_id)