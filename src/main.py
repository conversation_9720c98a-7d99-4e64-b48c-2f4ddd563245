import paramiko
from src.client.carmaker_client import CarMaker<PERSON>lient
from src.executor.carmaker_executor import CarMakerTestExecutor
from src.utils.ssh_helper import SSHClient
from src.config import conf
from src.client.J6E_client import J6EClient

# 设置参数
machine_port_dict = {
    "RT_Primary": 16660,
    "RT_Radar": 16661
}
task_name_mapping = {
    "RT_Primary": "RT_Primary",
    "RT_Radar": "RT_Radar",
}
project_path = "src_arch9.1.3_DBC2.3.5_SIP2.3.1_MyUAQ/CarMaker.xeno"
# testrun_name = "Degrade_E2E_0709/ALC_DRIVING_ALC_2TJAICA_DIR/ALC_DRIVING_ALC_2TJAICA_FAULT_RLCR_STATUS_CRC_E2E_ERROR"
testrun_name = "yaliceshi"
tester_name = "<PERSON>ekun"
ssh_info = {
    "host": "************",
    "port": 22,
    "username": "admin",
    "password": "abc.123"
}

# 1. 建立 SSH 连接
# ssh = paramiko.SSHClient()
# ssh.set_missing_host_key_policy(paramiko.AutoAddPolicy())
# ssh.connect(
#     hostname=ssh_info["host"],
#     port=ssh_info["port"],
#     username=ssh_info["username"],
#     password=ssh_info["password"]
# )
windows_pc_info = conf.get_config_value("windows_pc_info")
host = windows_pc_info["ip"]
port = windows_pc_info["port"]
user = windows_pc_info["username"]
password = windows_pc_info["password"]
ssh = SSHClient(host, user, password)
ssh.connect()
print(ssh.is_connected())

master_ecu_conf = conf.get_config_value("master_ecu_conf")
host = master_ecu_conf["IP"]
user = master_ecu_conf["username"]
password = master_ecu_conf["password"]

client = J6EClient(host, user, password)

try:


    # 2. 初始化 CarMakerClient
    cmk = CarMakerClient(machine_port_dict)

    # # 3. 启动所有 RTM 程序（Windows计划任务）
    # cmk.start_carmaker(ssh_client=ssh, task_name_mapping=task_name_mapping)

    # # 4. 启动所有 RTM 应用（发送 connect/start 等命令）
    # cmk.start_all_applications(project_path=project_path, timeout=15)

    # #加载testrun
    # cmk.send_message("RT_Primary",f"LoadTestRun {testrun_name}\r")

    # cmk.send_message("RT_Primary","Movie start\r")    

    # cmk.send_message("RT_Primary","StartSim\r")
    print(cmk.is_alive())


    # 5. 创建执行器并运行测试用例
    # executor = CarMakerTestExecutor(cmk)
    # executor.run_test_sequence(testrun_name=testrun_name, tester_name=tester_name)

    # 6. 可选保存视频
    # cmk.save_testrun_video(f"../TestReports/movie_{testrun_name}.mp4")

finally:
    # 7. 清理资源（关闭 socket，终止进程）
    # cmk.close_carmaker(ssh)
    ssh.disconnect()
    cmk.close()