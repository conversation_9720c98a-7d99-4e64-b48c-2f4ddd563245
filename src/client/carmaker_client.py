import os
import re
import time
import psutil
import socket

from typing import Dict

from src.utils.logger import log

class RTMachineClient:
    def __init__(self, name, port):
        self.name = name
        self.port = port
        self.sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        self.connected = False

    def start(self, ssh_client, task_name) -> None:
        """Starts the CarMaker RT module via Windows Task Scheduler over SSH.

        Args:
            ssh_client: An active Paramiko SSHClient object.
        """
        try:
            cmd = f'schtasks /Run /TN "{task_name}"'
            log.info(f"{self.name} 启动计划任务命令: {cmd}")
            stdout, stderr = ssh_client.exec_command(cmd)

            if stdout:
                log.info(f"{self.name} 启动输出:{stdout}")
            if stderr:
                log.warning(f"{self.name} 启动错误:{stderr}\n")

            time.sleep(3 if self.name != "RT_Primary" else 10)

        except Exception as error:
            log.error(f"{self.name} 启动失败: {error}")
            raise

    def connect(self, host='************'):
        try:
            self.sock.connect((host, self.port))
            self.connected = True
            log.info(f"[{self.name}] Connected.")
        except socket.error:
            log.warning(f"[{self.name}] First connect failed, retrying after 7s...")
            time.sleep(7)
            self.sock.connect((host, self.port))
            self.connected = True
            log.info(f"[{self.name}] Connected on retry.")

    def send(self, message: str):
        if self.connected:
            self.sock.send(message.encode('utf-8'))
        else:
            raise RuntimeError(f"[{self.name}] Socket not connected")

    def recv(self, buffer_size=4096) -> bytes:
        return self.sock.recv(buffer_size)
    
    def is_alive(self) -> bool:
        """
        判断 RTMachine 是否仍然在线并连接正常。

        Returns:
            bool: True 表示 socket 已连接 且 RT 应用未返回断开状态。
        """
        if not self.connected:
            return False

        try:
            self.send("Application isconnected \r")
            time.sleep(0.3)
            resp = self.recv()
            return b'O0' not in resp  # 'O0' 表示断开，其他返回视为正常
        except (socket.error, OSError) as e:
            log.warning(f"[{self.name}] Socket check failed: {e}")
            return False

    def check_status(self) -> bool:
        self.send("Application isconnected \r")
        time.sleep(0.5)
        resp = self.recv()
        return b'O0' in resp  # True means disconnected

    def stop(self):
        try:
            self.send("Application stop\r")
        except Exception as e:
            log.warning(f"[{self.name}] Error sending stop command: {e}")
        self.sock.close()
        self.connected = False

    def connect_application(self) -> None:
        """Send Application connect command."""
        self.send("Application connect\r")
        time.sleep(1)

    def stop_application(self, timeout: float = 15.0) -> None:
        """Stop old RT Application and wait until not running (-99)."""
        self._send_and_await_sim_status("Application stop\r", expected_status=b'-99', timeout=timeout)

    def start_application(self, timeout: float = 15.0) -> None:
        """Start new RT Application and wait until idle (-2)."""
        self._send_and_await_sim_status("Application start\r", expected_status=b'-2', timeout=timeout)

    def _send_and_await_sim_status(self, command: str, expected_status: bytes, timeout: float) -> None:
        """Send command and poll SimStatus until expected value."""
        self.send(command)
        time.sleep(0.5)
        self.send("SimStatus \r")
        response = self.recv()
        time_passed = 0.0

        while expected_status not in response and time_passed < timeout:
            time.sleep(0.5)
            time_passed += 0.5
            self.send("SimStatus \r")
            response = self.recv()
            log.debug(f"[{self.name}] SimStatus response: {response}")

        if time_passed >= timeout:
            log.error(f"[{self.name}] SimStatus timeout exceeded.")
            raise RuntimeError(f"[{self.name}] Application did not reach expected SimStatus: {expected_status}")
    

class PrimaryClient(RTMachineClient):
    def __init__(self, port, project_path=None, executable=None):
        super().__init__("RT_Primary", port)
        self.project_path = project_path
        self.executable = executable

    def save_test_run_video(self, file_name: str):
        self.send("Movie camera select \"MyDefault\" -window 0 -view 0\r")
        self.recv()
        self.send(
            f"Movie export window {file_name} 0 -width 1280 -height 960 "
            "-start start -end end -format mpeg4 -framerate 10 -overwrite -quality 1\r"
        )
        self.recv()

        log.info("Exporting IPG Movie Video...")
        timeout = 0
        while timeout < 20:
            self.send("Movie export status\r")
            resp = self.recv()
            if b'0' in resp:
                break
            time.sleep(0.5)
            timeout += 0.5
        log.info("Exporting IPG Movie Video Done.")

    def start_ipg_movie(self):
        self.send("Movie start\r")
    
    def quit_ipg_moive(self):
        self.send("Movie quit\r")

    def load_project_and_start(self, project_path: str, timeout: float = 15.0) -> None:
        """下发 CarMaker xeno 可执行路径并启动 RT."""
        self.send(f"Application exe {project_path} \r")
        self.start_application(timeout=timeout)

    # def _get_carmaker_version(self) -> int:
    #     pattern = re.compile(r'carmaker.*-([0-9]+)\.([0-9]).*CM(_HIL)?.exe')
    #     match = pattern.search(self.executable)
    #     if match:
    #         return int(match.group(1) + match.group(2))
    #     return -1
    

class CarMakerClient:
    """Manages CarMaker real-time modules such as Primary, Radar, Lidar, and USS."""

    def __init__(self, machine_port_dict):
        """Initializes the CarMaker client with all module instances.

        Args:
            machine_port_dict: Dict[str, int], machine name is key, tcp port is value
        """
        self.modules: Dict[str, RTMachineClient] = {}
        for machine, port in machine_port_dict.items():
            if machine == "RT_Primary":
                self.modules[machine] = PrimaryClient(port=port)
            else:
                self.modules[machine] = RTMachineClient(machine,port=port)

    def start_carmaker(self, ssh_client, task_name_mapping) -> None:
        """Starts and connects all CarMaker RT modules.

        Args:
            ssh_client: A Paramiko SSH client to execute Windows scheduled task.
            task_name_mapping: Dict[str, str], mapping of RT module name to its schtasks name.
        """
        for name, module in self.modules.items():
            log.info(f"Starting module {name}")
            module.start(ssh_client, task_name_mapping[name])
            module.connect()
            time.sleep(10 if name == "RT_Primary" else 3)
    
    def start_all_applications(self, project_path: str, timeout: int) -> None:
        """连接并启动所有 RT 模块的 Application。

        Args:
            project_path: 主模块的执行路径。
            timeout: 每个模块的超时时间（秒）。
        """
        for name, module in self.modules.items():
            try:
                # 1) 先确认 socket 是否连接
                if module.connected:
                    # 2) 已连接 → 判断应用是否存活
                    if module.is_alive():
                        log.info(f"[{name}] socket已连接且应用存活，跳过启动。")
                        continue
                    else:
                        log.info(f"[{name}] socket已连接但应用不存活，尝试重连应用...")
                        # 连接并启动应用
                        log.info(f"[{name}] 开始连接 Application...")
                        module.connect_application()
                        log.info(f"[{name}] 尝试终止旧的应用...")
                        module.stop_application(timeout=timeout)
                        if name == "RT_Primary" and isinstance(module, PrimaryClient):
                            log.info(f"[{name}] 下发执行路径并启动: {project_path}")
                            module.load_project_and_start(project_path, timeout=timeout)
                        else:
                            log.info(f"[{name}] 启动实时模块应用...")
                            module.start_application(timeout=timeout)
                        log.info(f"[{name}] 启动完成。")
                else:
                    # 3) 未连接 → 先尝试socket连接
                    log.info(f"[{name}] socket未连接，尝试连接...")
                    try:
                        module.connect()
                    except Exception as ce:
                        log.warning(f"[{name}] socket 连接失败: {ce}")
                        # socket未连上，无法进行应用操作
                        continue

                    # 连接后再判断应用存活
                    if module.is_alive():
                        log.info(f"[{name}] socket已连接且应用存活，跳过启动。")
                        continue

                    # 应用不存活 → 执行连接应用和启动
                    log.info(f"[{name}] 开始连接 Application...")
                    module.connect_application()
                    log.info(f"[{name}] 尝试终止旧的应用...")
                    module.stop_application(timeout=timeout)
                    if name == "RT_Primary" and isinstance(module, PrimaryClient):
                        log.info(f"[{name}] 下发执行路径并启动: {project_path}")
                        module.load_project_and_start(project_path, timeout=timeout)
                    else:
                        log.info(f"[{name}] 启动实时模块应用...")
                        module.start_application(timeout=timeout)
                    log.info(f"[{name}] 启动完成。")

            except Exception as e:
                log.error(f"[{name}] 启动失败: {e}")
                log.warning(f"[{name}] 建议检查执行路径或重启 XPack4。")

    def start_primary_application(self, timeout) -> None:
        """仅启动 Primary 实时模块。"""
        module = self.modules.get("RT_Primary")
        if not isinstance(module, PrimaryClient):
            log.error("[RT_Primary] 未正确初始化或类型错误。")
            return

        try:
            module.connect_application()
            log.info("[RT_Primary] 尝试终止旧应用...")
            module.stop_application(timeout=timeout)
            log.info("[RT_Primary] 启动实时模块...")
            module.start_application(timeout=timeout)
        except Exception as e:
            log.error(f"[RT_Primary] 启动失败: {e}")
            log.info("建议重启 XPack4")

    def send_message(self, name: str, message: str) -> None:
        """Sends a message to the specified RT module.

        Args:
            name: Module name.
            message: Message string to send.
        """
        log.debug(f"name:{name},message:{message}")
        self.modules[name].send(message)

    def get_response(self, name: str) -> bytes:
        """Receives a response from the specified RT module.

        Args:
            name: Module name.

        Returns:
            Response in bytes.
        """
        return self.modules[name].recv()

    def check_rt_machine_status(self, name: str) -> bool:
        """Checks whether the given RT module is connected.

        Args:
            name: Module name.

        Returns:
            True if disconnected, False if already connected.
        """
        return self.modules[name].check_status()

    def save_testrun_video(self, file_name: str) -> None:
        """Saves the testrun video via the PrimaryClient.

        Args:
            file_name: Output file name.
        """
        primary = self.modules.get("RT_Primary")
        if isinstance(primary, PrimaryClient):
            primary.save_test_run_video(file_name)
        
    @staticmethod
    def kill_processes(ssh_client) -> None:
        """远程关闭与 CarMaker 相关的进程（通过 SSH 执行 tasklist + taskkill）"""
        process_name_list = [
            "HIL.exe", "RTM.exe", "MovieNX.exe", "Movie.exe",
            "main_socket_v2.exe", "ipg-control.exe", "CM_HIL_debug.exe"
        ]

        for proc in process_name_list:
            check_cmd = f'tasklist /FI "IMAGENAME eq {proc}"'
            kill_cmd = f'taskkill /F /IM {proc}'

            # 1. 检查是否运行
            output, error = ssh_client.exec_command(check_cmd)
            log.info(f"output:{output}")

            if proc in output:
                log.info(f"{proc} 正在运行，尝试关闭...")
                out, err = ssh_client.exec_command(kill_cmd)
                if out.strip():
                    log.info(f"[{proc}] 输出: {out.strip()}")
                if err.strip():
                    log.warning(f"[{proc}] 错误: {err.strip()}")
            else:
                log.info(f"{proc} 未在运行，跳过。")

    def is_alive(self):
        for name, module in self.modules.items():
            if not module.is_alive():
                return False
        return True

    def close_carmaker(self, ssh_client) -> None:
        """Stops all RT modules and kills HIL/RTM processes."""
        for name, module in self.modules.items():
            try:
                module.stop()
            except Exception as error:
                log.warning("Failed to stop %s: %s", name, error)

        # Clean up OS processes
        CarMakerClient.kill_processes(ssh_client)

    def close(self) -> None:
        """Closes all socket connections."""
        for module in self.modules.values():
            if module.connected:
                module.sock.close()


    def __del__(self):
        self.close()


if __name__ == "__main__":
    pass