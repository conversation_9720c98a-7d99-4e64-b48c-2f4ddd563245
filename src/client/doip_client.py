import re
import time
import binascii

from src.utils.logger import log
from src.doipdiagnostic.tester_base import TesterBase

class UDSClient:
    def __init__(self, testerIP, targetIP, testerAddress, targetAddress, port):
        self.testerIP = testerIP
        self.targetIP = targetIP
        self.testerAddress = testerAddress
        self.targetAddress = targetAddress
        self.port = port
        self.tester = None

    def initialize(self):
        """
        Creates a TesterBase instance and establishes a UDS communication route to the target.
        """
        self.tester = TesterBase(
            self.testerIP, self.targetIP, self.testerAddress, self.targetAddress, self.port
        )
        self.tester.connectWithRoute()

    def shutdown(self):
        """
        Disconnects the tester and releases resources if an active connection exists.
        """
        if self.tester:
            self.tester.disconnect()
            self.tester = None

    def execute_request(self, req: list):
        """
        Sends a single UDS request and waits for the response.

        Args:
            req (list[str] or str): UDS request message as a hexadecimal string or list of characters.

        Returns:
            str or None: The hexadecimal string representation of the UDS response, or None if the input is invalid.
        """
        if isinstance(req, list):
            req = "".join(req)
        req, cmmnt = self.split_cmd_comment(req)

        try:
            req_bytes = bytearray.fromhex(req)
        except ValueError as e:
            log.error(f"Invalid hex input: {req}, error: {e}")
            return None

        log.info(f"[UDS] Sending: {req}")
        self.tester.sendUds(req_bytes)
        response = self.tester.expectUds()

        hex_resp = binascii.hexlify(response.data).decode()
        ascii_resp = self.safe_ba2ascii(response.data)
        log.info(f"[UDS] Response: {hex_resp} ({ascii_resp})")

        return hex_resp

    def send_uds_sequence(self, req_sequence):
        """
        Sends a sequence of UDS requests, introducing a delay between each request.

        Args:
            req_sequence (list[str]): A list of hexadecimal UDS request strings.

        Returns:
            list[str]: A list of alternating request and response strings in the form 
                       [req1, resp1, req2, resp2, ...].
        """
        results = []
        for msg in req_sequence:
            results.append(str(msg))
            result = self.execute_request(msg)
            results.append(result)
            time.sleep(0.5)
        time.sleep(2)
        return results

    @staticmethod
    def safe_ba2ascii(byte_str):
        """
        Converts a byte string to a readable ASCII string, replacing non-printable characters with spaces.

        Args:
            byte_str (bytes): The byte array to be converted.

        Returns:
            str: ASCII string containing printable characters only.
        """
        return re.sub(r'[^\x20-\x7e]', r' ', byte_str.decode('ascii', errors='ignore'))

    @staticmethod
    def split_cmd_comment(cmd):
        """
        Splits a command string into its main command and an optional comment (starting with '#').

        Args:
            cmd (str): A command string that may contain an inline comment.

        Returns:
            Tuple[str, Optional[str]]: A tuple containing the cleaned command string and the comment (if present).
        """
        cmmnt = None
        cmmntpos = cmd.find("#")
        if cmmntpos >= 0:
            cmmnt = cmd[cmmntpos:].strip()
            cmd = cmd[:cmmntpos].strip()
        return cmd, cmmnt
    
if __name__ == "__main__":
    from src.config import conf

    uds_info = conf.get_config_value("uds_info")
    client = UDSClient(uds_info.get("test_ip", "************"), uds_info.get("target_ip", "*************"),
                       uds_info.get("test_address", "0x0E80"), uds_info.get("target_address", "0x0400"),
                       uds_info.get("port", 13400))
    client.initialize()
    # time.sleep(20)

    execute_open_DiD = ["1060", "2761", "2762" + "11" * 450, "2EFD0B00"]
    client.send_uds_sequence(execute_open_DiD)
