import socket

from src.utils.logger import log
from src.config import conf


class RemoteControlPower:
    """Class to control a remote programmable power supply over TCP socket."""

    BUFFER_SIZE = 128
    TIMEOUT_SECONDS = 10
    MAX_VOLT = 18
    MAX_CUR = 30
    VALID_SRC_LIST = ["front", "web", "seq", "eth", "slot1", "slot2", "slot3", "slot4", "loc", "rem"]

    def __init__(self):
        self.supply_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)

        programme_power_info = conf.get_config_value("programme_power_info")
        self.supply_ip = programme_power_info.get("ip", "***********")
        self.supply_port = programme_power_info.get("port", 8462)
        self.supply_socket.connect((self.supply_ip, self.supply_port))

        self.supply_socket.settimeout(self.TIMEOUT_SECONDS)

    def send_and_receive(self, msg: str) -> str:
        """Send a command and wait for the response."""
        self.supply_socket.sendall((msg + "\n").encode("utf-8"))
        return self.supply_socket.recv(self.BUFFER_SIZE).decode("utf-8").rstrip()

    def send_command(self, msg: str) -> None:
        """Send a command without waiting for response."""
        self.supply_socket.sendall((msg + "\n").encode("utf-8"))

    def set_remote_shutdown(self, state: bool) -> None:
        """Enable or disable remote shutdown."""
        self.send_command("SYST:RSD 1" if state else "SYST:RSD 0")

    def set_voltage(self, volt: float) -> int:
        """Set output voltage."""
        if 0 < volt <= self.MAX_VOLT:
            self.send_command(f"SOUR:VOLT {volt}")
            return 0
        return -1

    def set_current(self, cur: float) -> int:
        """Set output current."""
        if 0 < cur <= self.MAX_CUR:
            self.send_command(f"SOUR:CUR {cur}")
            return 0
        return -1

    def read_voltage(self) -> str:
        """Read configured voltage setting."""
        return self.send_and_receive("SOUR:VOLT?")

    def read_current(self) -> str:
        """Read configured current setting."""
        return self.send_and_receive("SOUR:CUR?")

    def measure_voltage(self) -> str:
        """Measure actual output voltage."""
        return self.send_and_receive("MEAS:VOLT?")

    def measure_current(self) -> str:
        """Measure actual output current."""
        return self.send_and_receive("MEAS:CURR?")

    def read_output_state(self) -> str:
        """Read whether the output is currently enabled."""
        return self.send_and_receive("OUTP?")

    def set_voltage_source(self, src: str) -> int:
        """Set programmable source for voltage."""
        if src.lower() in self.VALID_SRC_LIST:
            self.send_command(f"SYST:REM:CV {src}")
            return 0
        return -1

    def set_current_source(self, src: str) -> int:
        """Set programmable source for current."""
        if src.lower() in self.VALID_SRC_LIST:
            self.send_command(f"SYST:REM:CC {src}")
            return 0
        return -1

    def set_output_state(self, state: bool) -> None:
        """Enable or disable power output."""
        self.send_command("OUTPUT 1" if state else "OUTPUT 0")

    def power_on(self) -> None:
        """Power on the supply with default voltage and current."""
        self.set_voltage(13.00)
        self.set_current(30.00)
        self.set_remote_shutdown(False)
        log.info("Now set power on!")

    def power_off(self) -> None:
        """Power off the supply by setting voltage and current to 0."""
        self.set_voltage(0.00)
        self.set_current(0.00)
        self.set_remote_shutdown(True)
        log.info("Now set power off!")

    def __del__(self):
        if self.supply_socket is not None:
            self.supply_socket.close()
            log.info("Release supply socket!")

if __name__ == "__main__":
    from time import sleep

    try:
        power = RemoteControlPower()

        print("Powering ON...")
        power.power_on()
        sleep(1)

        print("Reading configured voltage and current...")
        print("Set Voltage:", power.read_voltage())
        print("Set Current:", power.read_current())

        print("Measuring actual voltage and current...")
        print("Measured Voltage:", power.measure_voltage())
        print("Measured Current:", power.measure_current())

        print("Reading output state...")
        print("Output State:", power.read_output_state())

        print("Setting output OFF for 3 seconds...")
        power.set_output_state(False)
        sleep(10)

        print("Turning output back ON...")
        power.set_output_state(True)
        sleep(10)

        print("Powering OFF...")
        power.power_off()

    except Exception as e:
        print("Error during power supply test:", e)
    finally:
        del power  # Ensures socket is released