import re
import time
import json
from typing import List, Tu<PERSON>, Dict, Optional, Union
import os
import subprocess
import tempfile
import hashlib

from src.config import conf
from src.utils.logger import log
from src.utils.ssh_helper import SSHClient


class J6EClient:
    def __init__(self, host, user, password):
        self.host = host
        self.user = user
        self.password = password
        self.ssh_client = SSHClient(host, user, password)
        if not self.ssh_client.connect():
            raise Exception(f"连接到 {host} 失败")

    def __del__(self):
        if self.ssh_client:
            self.ssh_client.disconnect()

    def is_connection_alive(self):
        return self.ssh_client.is_connected()
    
    def connect(self):
        if not self.ssh_client.connect():
            raise Exception(f"连接到 {self.host} 失败")

    def check_fps(self):
        try:
            log.info("检查FPS...")
            cmd = "cat /sys/class/vps/flow/fps | grep pym"
            output, error = self.ssh_client.exec_command(cmd)
            log.debug(f"命令执行结果: \n{output}")
            if error:
                log.warning(f"命令执行错误: {error}")
            values = re.findall(r'och0\s*(\d+)', output)
            values = [int(v) for v in values]
            log.debug(f"och0值: {values}")
            all_30 = all(v == 30 for v in values) if values else False
            log.info(f"所有och0值均为30: {all_30}")
            return all_30, values
        except Exception as e:
            log.error(f"检查FPS失败: {e} ({type(e).__name__})")
            return False, []

    def check_processes(self):
        try:
            log.info("检查进程...")
            cmd = "ps -ef | grep /asw/bin"
            output, error = self.ssh_client.exec_command(cmd)

            log.debug(f"命令执行结果: \n{output}")
            if error:
                log.warning(f"命令执行错误: {error}")
            required_processes = [
                "/asw/bin/esme",
                "/asw/bin/esmeif_gateway",
                "/asw/bin/MiddleTrim_carma_0_22_deploy_roudi_combined",
                "/asw/bin/MasterOrin_parameter_gateway",
                "/asw/bin/AraComGateway_gateway",
                "/asw/bin/camera_center",
                "/asw/bin/SomeipGateway_gateway",
                "/asw/bin/rbp-vis-jetour",
                "/asw/bin/RbpPmpPsdCcp_activity",
                "/asw/bin/RbpMca_activity",
                "/asw/bin/NetComGateway_gateway",
                "/asw/bin/RbpEnvPer_activity",
                "/asw/bin/RbpApaFct_activity",
                "/asw/bin/RbpMotCoreCn_activity",
                "/asw/bin/RaPathRecorder_gateway",
                "/asw/bin/Viper_activity",
                "/asw/bin/Fct_activity",
                "/asw/bin/DynamicFusion_activity",
                "/asw/bin/MotionControl_activity",
                "/asw/bin/Planning_activity",
                "/asw/bin/LocalMsf_activity",
                "/asw/bin/HttpGateway_gateway",
                "/asw/bin/RbpInfra_activity",
                "/asw/bin/RbpCoolMaster_gateway",
                "/asw/bin/MapEnhancement_activity",
                "/asw/bin/map_loc_provider",
                "/asw/bin/LocalMappingGateway_gateway",
                "/asw/bin/OnboardMapping_gateway",
                "/asw/bin/Mapsrv_activity",
                "/asw/bin/PlanningDriving_activity",
                "/asw/bin/T10_activity",
                "/asw/bin/T20_activity",
                "/asw/bin/EnvModel1v_activity",
                "/asw/bin/Sit_activity",
                "/asw/bin/Parameter_activity",
                "/asw/bin/ActivitySpp_activity",
                "/asw/bin/GwPdm_gateway",
                "/asw/bin/PrmGatewayMaster_gateway",
                "/asw/bin/RecordGateway_gateway",
                "/asw/bin/DataHubGateway_gateway",
                "/asw/bin/OnlineCalibration_activity"
            ]
            missing_processes = []
            running_processes = []
            for process in required_processes:
                if process in output:
                    running_processes.append(process)
                    log.debug(f"✅ 进程运行中: {process}")
                else:
                    missing_processes.append(process)
                    log.warning(f"❌ 进程缺失: {process}")
            total_processes = len(required_processes)
            running_count = len(running_processes)
            missing_count = len(missing_processes)
            log.info("--- 进程检查统计 ---")
            log.info(f"总进程数: {total_processes}")
            log.info(f"运行中: {running_count}")
            log.info(f"缺失: {missing_count}")
            all_running = missing_count == 0
            if all_running:
                log.info("✅ 所有必需进程都在运行。")
            else:
                log.warning(f"❌ {missing_count} 个进程缺失。")
            return all_running, running_processes, missing_processes
        except Exception as e:
            log.error(f"检查进程失败: {e} ({type(e).__name__})")
            return False, [], []

    def restart(self, max_wait=120, check_interval=20):
        try:
            log.info("正在重启J6E...")
            full_cmd = (
                "cd /opt/app/lcm_tester/bin/ && "
                "export LD_LIBRARY_PATH=/opt/vrte/lib:/opt/app/lib/:/usr/lib:/lib:/proc/boot:/opt/vrte/usr/lib && "
                "export ECUCFG_ENV_VAR_ROOTFOLDER=/opt/app/etc/ecu-cfg && "
                "export PROCESSIDENTIFIER=lcm_tester && "
                "./lcm_tester 40"
            )
            output, error = self.ssh_client.exec_command(full_cmd)
            log.debug(f"lcm_tester输出: {output}")
            if error:
                log.warning(f"lcm_tester错误: {error}")

            log.info("等待J6E重启...")
            waited = 0
            while waited < max_wait:
                log.info(f"等待{check_interval}秒后检查J6E状态...")
                time.sleep(check_interval)
                waited += check_interval
                # Re-check connection before checking status
                if not self.ssh_client.check_conection_and_retry():
                    log.warning("连接丢失，无法检查状态。继续等待...")
                    continue

                fps_ok, _ = self.check_fps()
                proc_ok, _, missing = self.check_processes()
                if fps_ok and proc_ok:
                    log.info("✅ J6E重启成功，FPS和进程检查通过！")
                    return True
                else:
                    log.info(f"检查未通过，继续等待... (fps_ok={fps_ok}, proc_ok={proc_ok})")
                    if not proc_ok:
                        log.warning(f"缺失的进程: {missing}")
            log.error(f"❌ 重启超时，最大等待{max_wait}秒。")
            return False
        except Exception as e:
            log.error(f"重启失败: {e}")
            return False

    def check_hil_mode(self):
        try:
            log.info("检查HIL模式...")
            full_cmd = (
                "cd /opt/app/lcm_tester/bin/ && "
                "export LD_LIBRARY_PATH=/opt/vrte/lib:/opt/app/lib/:/usr/lib:/lib:/proc/boot:/opt/vrte/usr/lib && "
                "export ECUCFG_ENV_VAR_ROOTFOLDER=/opt/app/etc/ecu-cfg && "
                "export PROCESSIDENTIFIER=lcm_tester && "
                "./lcm_tester 36"
            )
            output, error = self.ssh_client.exec_command(full_cmd)
            log.debug(f"lcm_tester 36 输出:\n{output}")
            if error:
                log.warning(f"lcm_tester 36 错误: {error}")
            hlappfg_match = re.search(r'\[Lcm_Core\] HlAppFg in \[(\w+)\]', output)
            socmode_match = re.search(r'soc mode:\s*(\w+)', output)
            hlappfg_ok = False
            socmode_ok = False
            hlappfg_val = None
            socmode_val = None
            if hlappfg_match:
                hlappfg_val = hlappfg_match.group(1)
                log.debug(f"HlAppFg方括号内容: {hlappfg_val}")
                if hlappfg_val == "HIL":
                    log.info("✅ HlAppFg 为 HIL")
                    hlappfg_ok = True
                else:
                    log.warning(f"❌ HlAppFg 不为 HIL，当前为: {hlappfg_val}")
            else:
                log.warning("未找到 [Lcm_Core] HlAppFg in [...] 字段")
            if socmode_match:
                socmode_val = socmode_match.group(1)
                log.debug(f"soc mode值: {socmode_val}")
                if socmode_val == "HIL":
                    log.info("✅ soc mode 为 HIL")
                    socmode_ok = True
                else:
                    log.warning(f"❌ soc mode 不为 HIL，当前为: {socmode_val}")
            else:
                log.warning("未找到 soc mode: 字段")
            all_ok = hlappfg_ok or socmode_ok
            return all_ok, {"HlAppFg": hlappfg_val, "soc_mode": socmode_val}, output
        except Exception as e:
            log.error(f"执行lcm_tester 36失败: {e}")
            return False, None, ""

    def switch_to_hil_mode(self):
        try:
            log.info("正在切换到HIL模式...")
            full_cmd = (
                "cd /opt/app/lcm_tester/bin/ && "
                "export LD_LIBRARY_PATH=/opt/vrte/lib:/opt/app/lib/:/usr/lib:/lib:/proc/boot:/opt/vrte/usr/lib && "
                "export ECUCFG_ENV_VAR_ROOTFOLDER=/opt/app/etc/ecu-cfg && "
                "export PROCESSIDENTIFIER=lcm_tester && "
                "./lcm_tester 6"
            )
            output, error = self.ssh_client.exec_command(full_cmd)
            log.debug(f"lcm_tester 6 输出:\n{output}")
            if error:
                log.warning(f"lcm_tester 6 错误: {error}")
            # Check if 'Set SysMode : HIL ::' is followed by 'success'
            match = re.search(r'Set SysMode : HIL ::\s*(\w+)', output)
            success_flag = bool(match and match.group(1).lower() == 'success')
            return success_flag, output
        except Exception as e:
            log.error(f"执行lcm_tester 6失败: {e}")
            return False, None

    def start_getk_process(self, max_retries=3):
        """
        检查并启动getk进程。
        """
        GETK_PROCESS_NAME = "./mta_etas_getkp_gateway -c mta_gateway_vgetk.json"
        CHECK_CMD = "ps -ef | grep getk"
        START_CMD = "cd /asw && ./scripts/setup_runtime_env.sh && ./scripts/start_record.sh -m getk"

        log.info("开始检查并启动getk进程...")

        # 首次检查，看进程是否已在运行
        output, _ = self.ssh_client.exec_command(CHECK_CMD)
        if GETK_PROCESS_NAME in output:
            log.info("✅ getk进程已在运行。")
            return True

        # 如果进程不存在，则尝试启动，并重试
        for attempt in range(max_retries):
            log.info(f"getk进程不存在，第 {attempt + 1}/{max_retries} 次尝试启动...")
            
            start_output, start_error = self.ssh_client.exec_command(START_CMD)
            log.debug(f"启动脚本输出: {start_output}")
            if start_error:
                log.warning(f"启动脚本错误: {start_error}")
            
            log.info("等待5秒让进程初始化...")
            time.sleep(5)

            # 再次检查进程是否存在
            output, _ = self.ssh_client.exec_command(CHECK_CMD)
            if GETK_PROCESS_NAME in output:
                log.info(f"✅ 在第 {attempt + 1} 次尝试后，getk进程成功启动。")
                return True

        log.error(f"❌ 经过 {max_retries} 次尝试后，无法启动getk进程。")
        return False

    def clear_logs(self):
        """
        清空日志文件
        执行以下命令：
        1. cd /log/coredump/ ; rm -rf *core*
        2. cd /log/ASW/ ; rm -rf *.dlt
        3. cd /log/BSW/ ; rm -rf *.dlt

        Returns:
            bool: 清空操作是否成功
        """
        try:
            log.info("开始清空日志文件...")

            # 清空 coredump 文件
            cmd1 = "cd /log/coredump/ && rm -rf *core*"
            _, error1 = self.ssh_client.exec_command(cmd1)
            log.debug(f"清空coredump文件命令: {cmd1}")
            if error1:
                log.warning(f"清空coredump文件时出现错误: {error1}")
                return False
            else:
                log.info("✅ 已清空 /log/coredump/ 目录下的 *core* 文件")

            # 清空 ASW 日志文件
            cmd2 = "cd /log/ASW/ && rm -rf *.dlt"
            _, error2 = self.ssh_client.exec_command(cmd2)
            log.debug(f"清空ASW日志文件命令: {cmd2}")
            if error2:
                log.warning(f"清空ASW日志文件时出现错误: {error2}")
                return False
            else:
                log.info("✅ 已清空 /log/ASW/ 目录下的 *.dlt 文件")

            # 清空 BSW 日志文件
            cmd3 = "cd /log/BSW/ && rm -rf *.dlt"
            _, error3 = self.ssh_client.exec_command(cmd3)
            log.debug(f"清空BSW日志文件命令: {cmd3}")
            if error3:
                log.warning(f"清空BSW日志文件时出现错误: {error3}")
                return False
            else:
                log.info("✅ 已清空 /log/BSW/ 目录下的 *.dlt 文件")

            # 如果所有命令都没有严重错误，认为成功
            success = True
            log.info("✅ 日志清空操作完成")
            return success

        except Exception as e:
            log.error(f"清空日志失败: {e} ({type(e).__name__})")
            return False

    def read_coredump(self):
        """
        读取coredump文件列表
        执行命令: cd /log/coredump/ ; find -maxdepth 1 -type f -name '*core*' 2>/dev/null

        Returns:
            list: coredump文件列表，如果出错返回空列表
        """
        try:
            log.info("读取coredump文件列表...")
            cmd = "cd /log/coredump/ && find . -maxdepth 1 -type f -name '*core*' 2>/dev/null"
            output, error = self.ssh_client.exec_command(cmd)
            log.debug(f"读取coredump文件命令: {cmd}")

            if error:
                log.warning(f"读取coredump时出现错误: {error}")

            # 解析输出，获取文件列表
            if output.strip():
                files = [line.strip() for line in output.strip().split('\n') if line.strip()]
                log.info(f"找到 {len(files)} 个coredump文件: {files}")
                return files
            else:
                log.info("未找到coredump文件")
                return []

        except Exception as e:
            log.error(f"读取coredump失败: {e} ({type(e).__name__})")
            return []

    def copy_logs(self, local_path):
        """
        拷贝日志文件到本地
        执行以下操作：
        1. cd /log ; tar -czvf /tmp/log.tar.gz /log
        2. 在本地执行 scp user@host:/tmp/log.tar.gz local_path

        Args:
            local_path (str): 本地保存路径

        Returns:
            bool: 拷贝操作是否成功
        """
        try:
            log.info(f"开始拷贝日志文件到本地路径: {local_path}")
            # 步骤0：将version_info.json和buildinfo.txt拷贝到/log目录下
            log.info("将version_info.json 和 buildinfo.txt 拷贝到 /log 目录...")
            copy_cmd = "cp /asw/version_info.json /log/ 2>/dev/null && cp /opt/buildinfo.txt /log/ 2>/dev/null"
            output, error = self.ssh_client.exec_command(copy_cmd)
            log.debug(f"拷贝文件命令: {copy_cmd}")
            if error:
                log.warning(f"拷贝文件时出现警告: {error}")
            
            log.info("将/ota/bsw 目录拷贝到 /log/ota_bsw ...")
            copy_dir_cmd = "cp -r /ota/bsw /log/ota_bsw 2>/dev/null"
            output, error = self.ssh_client.exec_command(copy_dir_cmd)
            log.debug(f"拷贝目录命令: {copy_dir_cmd}")
            if error:
                log.warning(f"拷贝目录时出现警告: {error}")
            
            # 第一步：在远程服务器上打包日志文件
            log.info("正在远程服务器上打包日志文件...")
            tar_cmd = "tar -czvf /map/log.tar.gz /log"
            output, error = self.ssh_client.exec_command(tar_cmd)
            log.debug(f"打包日志命令: {tar_cmd}")
            if error and "tar:" not in error:  # tar命令可能会有一些警告信息，不算错误
                log.warning(f"打包日志时出现错误: {error}")
                return False

            log.info("✅ 日志文件打包完成")
            log.debug(f"打包输出: {output}")

            # 第二步：使用scp拷贝文件到本地
            log.info("正在从远程服务器拷贝日志文件...")
            import subprocess
            import os

            # 确保本地目录存在
            os.makedirs(os.path.dirname(local_path), exist_ok=True)

            # 构建scp命令
            scp_cmd = [
                "scp",
                f"{self.user}@{self.host}:/map/log.tar.gz",
                local_path
            ]

            # 执行scp命令
            result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=300)
            log.debug(f"将log.tar.gz拷贝到指定路径的scp命令: {' '.join(scp_cmd)}")
            if result.returncode == 0:
                log.info(f"✅ 日志文件成功拷贝到: {local_path}")

                # 清理远程临时文件
                cleanup_cmd = "rm -f /map/log.tar.gz"
                self.ssh_client.exec_command(cleanup_cmd)
                log.info("✅ 已清理远程临时文件")
                return True
            else:
                log.error(f"scp拷贝失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            log.error("scp拷贝超时（5分钟）")
            return False
        except Exception as e:
            log.error(f"拷贝日志失败: {e} ({type(e).__name__})")
            return False
    
    def check_forbidden_mode(self) -> bool:
        """
        检查/userdata/data/esme/MiddleTrim_esme.json文件的第一行是否包含"Forbidden Mode"

        Returns:
            bool: 包含则返回True，否则返回False
        """
        try:
            file_path = "/userdata/data/esme/MiddleTrim_esme.json"
            cmd = f"head -n 1 {file_path}"
            stdout, stderr = self.ssh_client.exec_command(cmd)

            if stderr.strip():
                log.warning(f"检查Forbidden Mode时出现错误: {stderr.strip()}")

            first_line = stdout.strip()
            log.debug(f"{file_path} 第一行内容: {first_line}")

            if "Forbidden Mode" in first_line:
                log.info(f"{file_path} 文件第一行包含 'Forbidden Mode'")
                return True
            else:
                log.info(f"{file_path} 文件第一行不包含 'Forbidden Mode'")
                return False

        except Exception as e:
            log.error(f"检查Forbidden Mode失败: {e} ({type(e).__name__})")
            return False
        
    def disable_esme_autostart(self) -> bool:
        """
        关闭 ESME 自启功能

        执行命令: cd /asw/ && ./scripts/esme_switch.sh -f

        Returns:
            bool: 执行成功返回 True，否则返回 False
        """
        try:
            cmd = "cd /asw/ && ./scripts/esme_switch.sh -f"
            stdout, stderr = self.ssh_client.exec_command(cmd)
            log.debug(f"关闭自启命令输出 stdout: {stdout.strip()}, stderr: {stderr.strip()}")

            if stderr.strip():
                log.warning(f"关闭ESME自启时出现警告: {stderr.strip()}")
                return False

            time.sleep(1)  # 等待1秒让文件更新

            if self.check_forbidden_mode():
                log.info("Forbidden Mode 已关闭，关闭自启成功")
                return True
            else:
                log.error("Forbidden Mode 仍然存在，关闭自启失败")
                return False

        except Exception as e:
            log.error(f"关闭ESME自启失败: {e} ({type(e).__name__})")
            return False
        
    def enable_esme_autostart(self) -> bool:
        """
        打开 ESME 自启功能

        执行命令: cd /asw/ && ./scripts/esme_switch.sh -r

        Returns:
            bool: 执行成功返回 True，否则返回 False
        """
        try:
            cmd = "cd /asw/ && ./scripts/esme_switch.sh -r"
            stdout, stderr = self.ssh_client.exec_command(cmd)
            log.debug(f"打开自启命令输出 stdout: {stdout.strip()}, stderr: {stderr.strip()}")

            if stderr.strip():
                log.warning(f"打开ESME自启时出现警告: {stderr.strip()}")
                return False

            time.sleep(1)  # 等待1秒让文件更新

            if not self.check_forbidden_mode():
                log.info("Forbidden Mode 已打开，打开自启成功")
                return True
            else:
                log.error("Forbidden Mode 仍然存在，打开自启失败")
                return False

        except Exception as e:
            log.error(f"打开ESME自启失败: {e} ({type(e).__name__})")
            return False
    
    def get_asw_bsw_mcu_version(self) -> Tuple[str, str, str]:
        """
        获取ASW、BSW和MCU版本信息
        1. 读取 /asw/version_info.json 文件，获取 prj-mt-j6e-root 下的 package_name。
        2. 读取 /opt/buildinfo.txt 文件，获取 RCORE_TAG 和 VARIANT_BUILD_ID 对应的值。
        
        Returns:
            Tuple[str, str, str]: (ASW版本, BSW版本, MCU版本)
        """
        asw_version = "Unknown"
        bsw_version = "Unknown"
        mcu_version = "Unknown"

        try:
            # 获取ASW版本 (/asw/version_info.json)
            cmd = "cat /asw/version_info.json"
            output, error = self.ssh_client.exec_command(cmd)
            if error:
                log.warning(f"读取version_info.json失败: {error}")
            else:
                try:
                    data = json.loads(output)
                    asw_version = data.get("prj-mt-j6e-root", {}).get("package_name", "Unknown")
                    log.info(f"ASW Version: {asw_version}")
                except Exception as e:
                    log.error(f"解析version_info.json失败: {e}")

            # 获取BSW和MCU版本 (/opt/buildinfo.txt)
            cmd = "cat /opt/buildinfo.txt"
            output, error = self.ssh_client.exec_command(cmd)
            if error:
                log.warning(f"读取buildinfo.txt失败: {error}")
            else:
                for line in output.splitlines():
                    if line.startswith("RCORE_TAG="):
                        mcu_version = line.split("=", 1)[1].strip()
                    elif line.startswith("VARIANT_BUILD_ID="):
                        bsw_version = line.split("=", 1)[1].strip()
                log.info(f"BSW Version: {bsw_version}")
                log.info(f"MCU Version: {mcu_version}")

        except Exception as e:
            log.error(f"获取ASW/BSW/MCU版本失败: {e}")

        return asw_version, bsw_version, mcu_version

    def upload_ota_db(self) -> bool:
        try:
            # 通过__file__反推到项目根目录
            module_path = os.path.abspath(__file__)
            project_root = os.path.dirname(os.path.dirname(module_path))
            local_ota_db = os.path.join(project_root, "config/ota/ota.db")

            if not os.path.isfile(local_ota_db):
                log.error(f"本地ota.db文件不存在: {local_ota_db}")
                return False

            log.info(f"开始上传本地文件: {local_ota_db} 到远程 /ota/bsw/persist/ 目录")

            scp_cmd = [
                "scp",
                local_ota_db,
                f"{self.user}@{self.host}:/ota/bsw/persist/"
            ]

            result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=120)

            if result.returncode == 0:
                log.info("✅ ota.db 文件上传成功")
                return True
            else:
                log.error(f"SCP上传失败: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            log.error("scp上传超时（2分钟）")
            return False
        except Exception as e:
            log.error(f"上传ota.db文件失败: {e} ({type(e).__name__})")
            return False

    def update_shadow_config_mode_to_bosch_mt(self) -> bool:
        """
        确保 /userdata/data/ShadowConfigMode.json 中 SHADOW_CONFIG_MODE 的值是 'bosch_mt'，
        如果不是则修改。
        """
        try:
            remote_file = "/userdata/data/ShadowConfigMode.json"

            # 先检查当前值
            check_cmd = f"grep '\"SHADOW_CONFIG_MODE\"' {remote_file}"
            output, error = self.ssh_client.exec_command(check_cmd)

            if error.strip():
                log.error(f"检查SHADOW_CONFIG_MODE时出错: {error.strip()}")
                return False

            log.info(f"当前SHADOW_CONFIG_MODE行: {output.strip()}")

            if '"bosch_mt"' in output:
                log.info("SHADOW_CONFIG_MODE 已经是 bosch_mt，无需修改")
                return True

            # 需要修改
            sed_cmd = f"sed -i 's/\"SHADOW_CONFIG_MODE\"[ ]*:[ ]*\"[^\"]*\"/\"SHADOW_CONFIG_MODE\": \"bosch_mt\"/' {remote_file}"
            log.info(f"正在修改 SHADOW_CONFIG_MODE 为 bosch_mt: {sed_cmd}")
            _, error = self.ssh_client.exec_command(sed_cmd)

            if error.strip():
                log.error(f"修改过程中出现错误: {error.strip()}")
                return False

            log.info("✅ SHADOW_CONFIG_MODE 修改成功为 bosch_mt")
            return True

        except Exception as e:
            log.error(f"修改ShadowConfigMode失败: {e} ({type(e).__name__})")
            return False
    
    def update_camera_socket_addr(self) -> Tuple[bool, str]:
        """
        修改 /userdata/data/camera/camera_global_asample_jetour_normal_j6e_group.json
        中紧邻 "hol_eol" 字段上一行的 socket_addr 为 "***********"

        Returns:
            Tuple[bool, str]: (成功状态, 信息)
        """
        try:
            remote_file = "/userdata/data/camera/camera_global_asample_jetour_normal_j6e_group.json"

            # 1. 找到含 "hol_eol" 的行号
            grep_hol_cmd = f"grep -n '\"hol_eol\"' {remote_file} | head -n 1 | cut -d':' -f1"
            output, error = self.ssh_client.exec_command(grep_hol_cmd)

            if error.strip():
                error_msg = f"查找 hol_eol 行时出错: {error.strip()}"
                log.error(error_msg)
                return False, error_msg

            line_hol = output.strip()
            if not line_hol.isdigit():
                success_msg = "未找到 hol_eol 字段，不进行修改"
                log.info(success_msg)
                return True, success_msg

            line_hol = int(line_hol)
            log.info(f"hol_eol 行号: {line_hol}")

            # 2. 向上查找包含 socket_addr 的行号（从 hol_eol 行往上10行以内查找）
            # 用tail和head命令获取 hol_eol 上方10行内容
            start_line = max(line_hol - 10, 1)
            sed_range = f"{start_line},{line_hol - 1}p"
            sed_cmd = f"sed -n '{sed_range}' {remote_file}"
            block_content, error = self.ssh_client.exec_command(sed_cmd)

            if error.strip():
                error_msg = f"读取 hol_eol 上方内容时出错: {error.strip()}"
                log.error(error_msg)
                return False, error_msg

            lines = block_content.splitlines()
            socket_line_offset = None
            for idx in reversed(range(len(lines))):  # 反向遍历，找最靠近 hol_eol 上面的 socket_addr
                if '"socket_addr"' in lines[idx]:
                    socket_line_offset = idx
                    break

            if socket_line_offset is None:
                success_msg = "hol_eol 上方未找到 socket_addr 行，不修改"
                log.info(success_msg)
                return True, success_msg

            line_socket = start_line + socket_line_offset
            log.info(f"找到 hol_eol 上方最近的 socket_addr 行号: {line_socket}")

            # 3. 读取该行内容，判断是否已经是目标值
            check_cmd = f"sed -n '{line_socket}p' {remote_file}"
            line_content, error = self.ssh_client.exec_command(check_cmd)
            if error.strip():
                error_msg = f"读取 socket_addr 行内容时出错: {error.strip()}"
                log.error(error_msg)
                return False, error_msg

            log.info(f"当前 socket_addr 行内容: {line_content.strip()}")

            if '"socket_addr": "***********"' in line_content:
                success_msg = "hol_eol 上方的 socket_addr 已经是 ***********，无需修改"
                log.info(success_msg)
                return True, success_msg

            # 4. 执行替换
            sed_replace_cmd = (
                f"sed -i '{line_socket}s/\"socket_addr\"[ ]*:[ ]*\"[^\"]*\"/\"socket_addr\": \"***********\"/' {remote_file}"
            )
            log.info(f"执行修改命令: {sed_replace_cmd}")
            _, error = self.ssh_client.exec_command(sed_replace_cmd)

            if error.strip():
                error_msg = f"修改 socket_addr 时出错: {error.strip()}"
                log.error(error_msg)
                return False, error_msg

            success_msg = "✅ 修改成功：hol_eol 上方的 socket_addr 已改为 ***********"
            log.info(success_msg)
            return True, success_msg

        except Exception as e:
            error_msg = f"修改 socket_addr 失败: {e} ({type(e).__name__})"
            log.error(error_msg)
            return False, error_msg

    def upload_asw_route(self) -> Tuple[bool, str]:
        try:
            # 远程挂载 /asw/ 目录为读写
            remount_cmd = "mount -o remount,rw /asw/"
            _, error = self.ssh_client.exec_command(remount_cmd)
            if error.strip():
                error_msg = f"挂载 /asw/ 读写失败: {error.strip()}"
                log.error(error_msg)
                return False, error_msg

            # 上传 ASW_Route 目录下的文件到 /asw/scripts/
            local_asw_route_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config/HWE2E_Config/ASW_Route")
            asw_file_names = [f for f in os.listdir(local_asw_route_dir) if os.path.isfile(os.path.join(local_asw_route_dir, f))]
            if not asw_file_names:
                error_msg = f"{local_asw_route_dir} 目录下无文件可上传"
                log.error(error_msg)
                return False, error_msg

            for file_name in asw_file_names:
                local_path = os.path.join(local_asw_route_dir, file_name)
                remote_path = f"{self.user}@{self.host}:/asw/scripts/{file_name}"

                scp_cmd = ["scp", local_path, remote_path]
                result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=120)
                if result.returncode != 0:
                    error_msg = f"上传文件 {file_name} 失败: {result.stderr.strip()}"
                    log.error(error_msg)
                    return False, error_msg

                # 上传成功后给文件赋权限
                chmod_cmd = f"chmod +x /asw/scripts/{file_name}"
                _, error = self.ssh_client.exec_command(chmod_cmd)
                if error.strip():
                    error_msg = f"chmod 文件 {file_name} 失败: {error.strip()}"
                    log.error(error_msg)
                    return False, error_msg

            log.info("✅ ASW_Route 文件上传并赋权限成功")

            # 确保 /map/nilite/route/ 目录存在，不存在则创建
            mkdir_cmd = "mkdir -p /map/nilite/route/"
            _, error = self.ssh_client.exec_command(mkdir_cmd)
            if error.strip():
                error_msg = f"创建 /map/nilite/route/ 目录失败: {error.strip()}"
                log.error(error_msg)
                return False, error_msg

            # 上传 route 目录下的文件到 /map/nilite/route/
            local_route_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "config/HWE2E_Config/route")
            route_file_names = [f for f in os.listdir(local_route_dir) if os.path.isfile(os.path.join(local_route_dir, f))]
            if not route_file_names:
                error_msg = f"{local_route_dir} 目录下无文件可上传"
                log.error(error_msg)
                return False, error_msg

            for file_name in route_file_names:
                local_path = os.path.join(local_route_dir, file_name)
                remote_path = f"{self.user}@{self.host}:/map/nilite/route/{file_name}"

                scp_cmd = ["scp", local_path, remote_path]
                result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=120)
                if result.returncode != 0:
                    error_msg = f"上传文件 {file_name} 失败: {result.stderr.strip()}"
                    log.error(error_msg)
                    return False, error_msg

            success_msg = "✅ ASW_Route 和 route 文件上传完成"
            log.info(success_msg)
            return True, success_msg

        except Exception as e:
            error_msg = f"上传 ASW_Route/route 文件失败: {e} ({type(e).__name__})"
            log.error(error_msg)
            return False, error_msg

    def check_if_esme_forbidden(self):
        """
        check esme start mode, if self start is off, return True. other cases return False.
        """
        status = self.ssh_client.check_conection_and_retry()
        if not status:
            log.error("ecu ssh connection is not available")
            return False, "ecu connection failed"

        cmd_check_esme = "cat /data/esme/MiddleTrim_esme.json"
        stdout, stderr = self.ssh_client.exec_command(cmd_check_esme)
        if not stdout.startswith("Forbidden"):
            log.error(f"self start enabled, {stdout}")
            return False, f"self start enabled."
        return True, ""
    
    def execute_navi_script(self, script_name: str) -> Tuple[bool, str]:
        """
        进入 /asw/ 目录并执行指定的导航脚本，根据输出判断是否成功。
        
        Args:
            script_name (str): 要执行的脚本名称 (如 navi_merge.sh, navi_urban.sh, navi.sh)
            
        Returns:
            Tuple[bool, str, str]: (是否成功, 标准输出, 错误输出)
        """
        valid_scripts = ["navi_merge.sh", "navi_urban.sh", "navi.sh"]

        if script_name not in valid_scripts:
            log.error(f"非法脚本名称: {script_name}，仅允许: {valid_scripts}")
            return False, "", f"非法脚本名称: {script_name}"

        stdout = ""
        stderr = ""
        success = False

        try:
            # 切换到 /asw/ 目录并执行脚本
            cmd = f"cd /asw/ && ./scripts/{script_name}"
            log.info(f"执行命令: {cmd}")
            stdout, stderr = self.ssh_client.exec_command(cmd)

            # if stderr:
            #     log.warning(f"脚本执行时有错误输出: {stdout}")
            # else:
            #     # log.info(f"脚本执行成功，输出: {stdout}")
            #     pass #do nothing

            # 判断是否成功
            success_keywords = ["success"]
            if any(keyword in stdout.lower() for keyword in success_keywords):
                success = True
                log.info(f"脚本 {script_name} 执行成功")
                log.debug(f"脚本执行成功，输出: {stdout}")
            else:
                log.warning(f"脚本 {script_name} 执行后未检测到成功关键字")

        except Exception as e:
            log.error(f"执行脚本 {script_name} 失败: {e}")
            stderr = str(e)
            success = False

        return success, stdout
    
    def check_xcalib_transform(self) -> Tuple[bool, str]:
        """
        检查远端 /opt/app/variantServer/bin/xcalib_cli -r 输出中 parameters 和 transform_world 的值是否存在100个以上的非零项。
        
        步骤：
        1. 设置 LD_LIBRARY_PATH
        2. 进入目录 /opt/app/variantServer/bin
        3. 执行 ./xcalib_cli -r
        4. 解析结果，若 parameters 或 transform_world 对应的值中非零项超过100个，则返回 True

        Returns:
            Tuple[bool, str]: (是否存在超过100个非零值, 执行输出内容)
        """
        success = False
        output = ""

        try:
            # 设置环境变量并执行命令
            cmd = (
                "export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/vrte/lib/:/opt/app/:"
                "/usr/local/lib3rd/usr/lib/:/opt/app/variantServer/bin:/asw/lib_w3/perception/:/asw/lib_shared && "
                "cd /opt/app/variantServer/bin && "
                "./xcalib_cli -r"
            )
            output, error = self.ssh_client.exec_command(cmd)
            if error:
                log.warning(f"xcalib_cli 执行失败: {error}")
                return False, error

            log.info("xcalib_cli 执行成功，开始解析结果")

            lines = output.splitlines()
            count_non_zero = 0

            for i, line in enumerate(lines):
                if line.strip() in ("parameters:", "transform_world:"):
                    if i + 1 < len(lines):
                        value_line = lines[i + 1].strip()
                        try:
                            values = list(map(float, value_line.split()))
                            non_zero_count = sum(1 for v in values if abs(v) > 1e-6)
                            count_non_zero += non_zero_count
                            log.info(f"{line.strip()} 非零值数量: {non_zero_count}")
                        except Exception as e:
                            log.warning(f"无法解析数值行: {value_line}, 错误: {e}")

            if count_non_zero == 229:
                success = True
                log.info(f"总非零值数量: {count_non_zero}，满足条件")

            else:
                log.info(f"总非零值数量: {count_non_zero}，不等于229，返回 False")

        except Exception as e:
            log.error(f"check_variantserver_transform 失败: {e}")
            return False, str(e)

        return success

    def write_xcalib_calib_data(self) -> Tuple[bool, str]:
        """
        在远程设备上执行 xcalib_cli 写入校准数据。

        执行命令：
            export LD_LIBRARY_PATH=... && cd /opt/app/variantServer/bin && ./xcalib_cli -w /data/calib

        Returns:
            Tuple[bool, str]: (执行是否成功, 命令输出内容)
        """
        success = False
        output = ""

        try:
            cmd = (
                "export LD_LIBRARY_PATH=$LD_LIBRARY_PATH:/opt/vrte/lib/:/opt/app/:"
                "/usr/local/lib3rd/usr/lib/:/opt/app/variantServer/bin:/asw/lib_w3/perception/:/asw/lib_shared && "
                "cd /opt/app/variantServer/bin && "
                "./xcalib_cli -w /data/calib"
            )
            output, error = self.ssh_client.exec_command(cmd)
            if error:
                log.warning(f"xcalib_cli 写入校准数据失败: {error}")
                return False, error

            success = True
            log.info("xcalib_cli 写入校准数据成功")

        except Exception as e:
            log.error(f"write_variantserver_calib 执行异常: {e}")
            return False, str(e)

        return success, output

    def upload_bin_files_with_backup(self, file_list: List[str]) -> Tuple[bool, str]:
        """
        本地文件bin替换到/asw/bin
        """    
        try:
            for local_path in file_list:
                if not os.path.isfile(local_path):
                    error_msg = f"本地文件不存在: {local_path}"
                    log.error(error_msg)
                    return False, error_msg

                file_name = os.path.basename(local_path)
                remote_path = f"/asw/bin/{file_name}"
                backup_path = f"/asw/bin/{file_name}.bak"

                # 挂载 /asw 为读写
                remount_cmd = "mount -o remount,rw /asw/"
                _, error = self.ssh_client.exec_command(remount_cmd)
                if error.strip():
                    error_msg = f"挂载 /asw/ 为读写失败: {error.strip()}"
                    log.error(error_msg)
                    return False, error_msg

                # 备份远程同名文件（如果存在）
                backup_cmd = f"[ -f {remote_path} ] && mv {remote_path} {backup_path} || true"
                _, error = self.ssh_client.exec_command(backup_cmd)
                if error.strip():
                    error_msg = f"备份远程文件 {file_name} 失败: {error.strip()}"
                    log.error(error_msg)
                    return False, error_msg

                # 执行 scp 拷贝
                remote_target = f"{self.user}@{self.host}:{remote_path}"
                scp_cmd = ["scp", local_path, remote_target]
                result = subprocess.run(scp_cmd, capture_output=True, text=True, timeout=120)
                if result.returncode != 0:
                    error_msg = f"上传文件 {file_name} 失败: {result.stderr.strip()}"
                    log.error(error_msg)
                    return False, error_msg

                # 赋执行权限
                chmod_cmd = f"chmod +x {remote_path}"
                _, error = self.ssh_client.exec_command(chmod_cmd)
                if error.strip():
                    error_msg = f"chmod 文件 {file_name} 失败: {error.strip()}"
                    log.error(error_msg)
                    return False, error_msg

                log.info(f"✅ 文件 {file_name} 上传成功并赋予执行权限")

            success_msg = "✅ 所有文件上传成功、赋权并挂载可写"
            log.info(success_msg)
            return True, success_msg

        except Exception as e:
            error_msg = f"上传 /asw/bin 文件失败: {e} ({type(e).__name__})"
            log.error(error_msg)
            return False, error_msg


    def update_middletrim_activity_levels(
        self,
        updates: Dict[str, Dict[str, Union[int, str]]],
        remote_file: str = "/userdata/data/esme/MiddleTrim_esme.json",
    ) -> Tuple[bool, str]:
        """
        修改目标 JSON（默认 /userdata/data/esme/MiddleTrim_esme.json）中的 activity 日志参数：
        -a: {DEBUG:1, INFO:2, WARN:3, ERROR:4}
        -s: {JSON:2, DLT:16}
        """
        try:
            if not self.ssh_client.check_conection_and_retry():
                return False, "SSH 连接不可用"

            level_map_a = {"DEBUG": 1, "INFO": 2, "WARN": 3, "ERROR": 4}
            sink_map_s = {"JSON": 2, "DLT": 16}

            def _normalize(val: Optional[Union[int, str]], mapping: Dict[str, int]) -> Optional[int]:
                if val is None:
                    return None
                if isinstance(val, int):
                    return val
                if isinstance(val, str):
                    if val.isdigit():
                        return int(val)
                    key = val.strip().upper()
                    if key in mapping:
                        return mapping[key]
                raise ValueError(f"无法解析值: {val}")

            # 打印输入与规范化映射
            log.info(f"update_middletrim_activity_levels: remote_file={remote_file}")
            log.info(f"requested activities: {list(updates.keys())}")

            normalized: Dict[str, Dict[str, Optional[int]]] = {}
            for act, params in updates.items():
                raw_a = params.get("-a")
                raw_s = params.get("-s")
                try:
                    a_val = _normalize(raw_a, level_map_a)
                    s_val = _normalize(raw_s, sink_map_s)
                except ValueError as ve:
                    log.error(f"normalize failed for activity={act}, -a={raw_a}, -s={raw_s}, err={ve}")
                    return False, f"normalize failed for {act}: {ve}"
                log.info(f"normalize activity={act}: -a {raw_a} -> {a_val}, -s {raw_s} -> {s_val}")
                normalized[act] = {"-a": a_val, "-s": s_val}

            # 下载远端文件，本地修改后上传
            sftp = None
            with tempfile.NamedTemporaryFile(mode="w+b", suffix=".json", delete=False) as tmpf:
                local_tmp = tmpf.name
            try:
                sftp = self.ssh_client.ssh_client.open_sftp()
                sftp.get(remote_file, local_tmp)
            finally:
                if sftp:
                    sftp.close()
            try:
                file_size = os.path.getsize(local_tmp)
            except Exception:
                file_size = -1
            log.info(f"downloaded remote json to temp: {local_tmp}, size={file_size}")
            # 记录原始文件 hash
            try:
                with open(local_tmp, 'rb') as f:
                    before_hash = hashlib.sha256(f.read()).hexdigest()
                log.info(f"remote before sha256: {before_hash}")
            except Exception as e:
                log.warning(f"calc before sha256 failed: {e}")

            # 本地解析并修改
            try:
                with open(local_tmp, "r", encoding="utf-8") as f:
                    data = json.load(f)
                log.info("loaded JSON successfully")
            except Exception as e:
                log.error(f"failed to parse JSON {local_tmp}: {e}")
                try:
                    os.remove(local_tmp)
                except Exception:
                    pass
                return False, f"parse JSON failed: {e}"

            def _update_arguments_array(activity_name: str, arguments: List[Union[str, int]], new_a: Optional[int], new_s: Optional[int]) -> List[Union[str, int]]:
                def replace_flag_value(args: List[Union[str, int]], flag: str, new_val: int) -> None:
                    try:
                        idx = args.index(flag)
                        if idx + 1 < len(args):
                            old_val = args[idx + 1]
                            args[idx + 1] = str(new_val)
                            log.info(f"activity={activity_name} update {flag}: {old_val} -> {new_val}")
                    except ValueError:
                        log.warning(f"activity={activity_name} flag {flag} not found in arguments, skip")

                args_copy: List[Union[str, int]] = list(arguments)
                if new_a is not None:
                    replace_flag_value(args_copy, "-a", new_a)
                if new_s is not None:
                    replace_flag_value(args_copy, "-s", new_s)
                return args_copy

            def _walk_and_update(obj, activity: str, new_a: Optional[int], new_s: Optional[int]) -> bool:
                updated = False
                if isinstance(obj, dict):
                    if obj.get("executable_id") == activity and isinstance(obj.get("arguments"), list):
                        obj["arguments"] = _update_arguments_array(activity, obj["arguments"], new_a, new_s)
                        return True
                    for v in obj.values():
                        if _walk_and_update(v, activity, new_a, new_s):
                            updated = True
                elif isinstance(obj, list):
                    for item in obj:
                        if _walk_and_update(item, activity, new_a, new_s):
                            updated = True
                return updated

            any_updated = False
            failed_acts: List[str] = []
            updated_count = 0
            for act, params in normalized.items():
                if not _walk_and_update(data, act, params.get("-a"), params.get("-s")):
                    failed_acts.append(act)
                else:
                    any_updated = True
                    updated_count += 1

            if not any_updated:
                try:
                    os.remove(local_tmp)
                except Exception:
                    pass
                return False, f"未在 {remote_file} 中找到指定 activity: {failed_acts}"
            log.info(f"activities updated count: {updated_count}, failed: {failed_acts}")

            with open(local_tmp, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            log.info("wrote updated JSON to temp file")

            sftp = None
            try:
                sftp = self.ssh_client.ssh_client.open_sftp()
                sftp.put(local_tmp, remote_file)
            finally:
                if sftp:
                    sftp.close()
                log.info(f"uploaded updated JSON to remote: {remote_file}")
            # 验证：重新下载并校验内容
            with tempfile.NamedTemporaryFile(mode="w+b", suffix=".json", delete=False) as tmpf2:
                local_verify = tmpf2.name
            try:
                sftp = self.ssh_client.ssh_client.open_sftp()
                sftp.get(remote_file, local_verify)
            finally:
                if sftp:
                    sftp.close()
            try:
                with open(local_verify, 'rb') as f:
                    after_hash = hashlib.sha256(f.read()).hexdigest()
                log.info(f"remote after sha256: {after_hash}")
            except Exception as e:
                log.warning(f"calc after sha256 failed: {e}")

            # 解析验证具体值
            try:
                with open(local_verify, 'r', encoding='utf-8') as f:
                    verified = json.load(f)
                def _get_arg_val(obj, flag):
                    try:
                        idx = obj['arguments'].index(flag)
                        if idx + 1 < len(obj['arguments']):
                            return obj['arguments'][idx+1]
                    except Exception:
                        return None
                    return None
                def _walk_collect(o, name, res):
                    if isinstance(o, dict):
                        if o.get('executable_id') == name and isinstance(o.get('arguments'), list):
                            res.append((_get_arg_val(o, '-a'), _get_arg_val(o, '-s')))
                        for v in o.values():
                            _walk_collect(v, name, res)
                    elif isinstance(o, list):
                        for it in o:
                            _walk_collect(it, name, res)
                for act, params in normalized.items():
                    checks = []
                    _walk_collect(verified, act, checks)
                    if not checks:
                        log.warning(f"post-verify: activity {act} not found in remote JSON")
                    else:
                        for (a_val, s_val) in checks:
                            log.info(f"post-verify {act}: -a={a_val}, -s={s_val}")
            except Exception as e:
                log.warning(f"post-verify parse failed: {e}")

            # 清理临时文件
            try:
                os.remove(local_tmp)
                os.remove(local_verify)
            except Exception:
                pass

            msg = f"已更新 {remote_file}"
            if failed_acts:
                msg += f"；未更新的 activity: {failed_acts}"
            return True, msg
        except Exception as e:
            log.error(f"update_middletrim_activity_levels failed: {e}")
            return False, f"更新失败: {e}"


if __name__ == "__main__":
    # 从配置文件读取参数
    master_ecu_conf = conf.get_config_value("master_ecu_conf")
    host = master_ecu_conf["IP"]
    user = master_ecu_conf["username"]
    password = master_ecu_conf["password"]

    client = J6EClient(host, user, password)

    # log.info("\n--- restart ---")
    # restart_ok = client.restart()
    # log.info(f"restart_ok: {restart_ok}")

    # log.info("\n--- check_fps ---")
    # fps_ok, fps_values = client.check_fps()
    # log.info(f"fps_ok: {fps_ok}, fps_values: {fps_values}")

    # log.info("\n--- check_processes ---")
    # proc_ok, running, missing = client.check_processes()
    # log.info(f"proc_ok: {proc_ok}, running_count: {len(running)}, missing_count: {len(missing)}")


    # log.info("\n--- switch_to_hil_mode ---")
    # success, output = client.switch_to_hil_mode()
    # log.info(f"success: {success}")
    # log.debug(f"output: {output}")

    log.info("\n--- check_hil_mode ---")
    hil_ok, details, output = client.check_hil_mode()
    log.info(f"hil_ok: {hil_ok}, details: {details}")
    log.debug(f"output: {output}")

    # log.info("\n--- start_getk_process ---")
    # getk_ok = client.start_getk_process()
    # log.info(f"getk_ok: {getk_ok}")

    # # 测试新增的三个函数
    # log.info("\n--- read_coredump ---")
    # coredump_files = client.read_coredump()
    # log.info(f"coredump_files: {coredump_files}")

    # log.info("\n--- clear_logs ---")
    # clear_ok = client.clear_logs()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- copy_logs ---")
    # copy_ok = client.copy_logs("/tmp/14142C/soc/auto/downloaded_logs.tar.gz")
    # log.info(f"copy_ok: {copy_ok}")

    # log.info("\n--- disable_esme_autostart ---")
    # clear_ok = client.disable_esme_autostart()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- enable_esme_autostart ---")
    # clear_ok = client.enable_esme_autostart()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- check_forbidden_mode ---")
    # clear_ok = client.check_forbidden_mode()
    # log.info(f"clear_ok: {clear_ok}")
    
    # log.info("\n--- get_asw_bsw_mcu_version ---")
    # clear_ok = client.get_asw_bsw_mcu_version()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- upload_ota_db ---")
    # clear_ok = client.upload_ota_db()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- update_shadow_config_mode_to_bosch_mt ---")
    # clear_ok = client.update_shadow_config_mode_to_bosch_mt()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- update_camera_socket_addr ---")
    # clear_ok = client.update_camera_socket_addr()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- upload_asw_route ---")
    # clear_ok = client.upload_asw_route()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- execute_navi_script ---")
    # clear_ok = client.execute_navi_script("navi.sh")
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- write_xcalib_calib_data ---")
    # clear_ok = client.write_xcalib_calib_data()
    # log.info(f"clear_ok: {clear_ok}")

    # log.info("\n--- check_xcalib_transform ---")
    # clear_ok = client.check_xcalib_transform()
    # log.info(f"clear_ok: {clear_ok}")

    # file_list = [
    # "/home/<USER>/Documents/LCY/DataHubGateway_gateway"
    # ]
    # log.info("\n--- upload_bin_files_with_backup ---")
    # clear_ok = client.upload_bin_files_with_backup(file_list)
    # log.info(f"clear_ok: {clear_ok}")

    # file_list = {
    # "Viper_activity": {"-a": 4, "-s": 16},
    # "Planning_activity": {"-a": 4, "-s": 16},
    # }
    # log.info("\n--- update_middletrim_activity_levels ---")
    # clear_ok = client.update_middletrim_activity_levels(file_list)
    # log.info(f"clear_ok: {clear_ok}")

    client.__del__()

