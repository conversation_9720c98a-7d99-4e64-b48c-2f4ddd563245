import re
import time

from src.config import conf
from src.utils.logger import log
from src.utils.ssh_helper import SSHClient


class J6EClient:
    def __init__(self, host, user, password):
        self.host = host
        self.user = user
        self.password = password
        self.ssh_client = SSHClient(host, user, password)
        if not self.ssh_client.connect():
            raise Exception(f"连接到 {host} 失败")

    def __del__(self):
        if self.ssh_client:
            self.ssh_client.disconnect()

    def check_fps(self):
        try:
            log.info("检查FPS...")
            cmd = "cat /sys/class/vps/flow/fps | grep pym"
            output, error = self.ssh_client.exec_command(cmd)
            log.debug(f"命令执行结果: \n{output}")
            if error:
                log.warning(f"命令执行错误: {error}")
            values = re.findall(r'och0\s*(\d+)', output)
            values = [int(v) for v in values]
            log.debug(f"och0值: {values}")
            all_30 = all(v == 30 for v in values) if values else False
            log.info(f"所有och0值均为30: {all_30}")
            return all_30, values
        except Exception as e:
            log.error(f"检查FPS失败: {e} ({type(e).__name__})")
            return False, []

    def check_processes(self):
        try:
            log.info("检查进程...")
            cmd = "ps -ef | grep /asw/bin"
            output, error = self.ssh_client.exec_command(cmd)

            log.debug(f"命令执行结果: \n{output}")
            if error:
                log.warning(f"命令执行错误: {error}")
            required_processes = [
                "/asw/bin/esme",
                "/asw/bin/esmeif_gateway",
                "/asw/bin/MiddleTrim_carma_0_22_deploy_roudi_combined",
                "/asw/bin/MasterOrin_parameter_gateway",
                "/asw/bin/AraComGateway_gateway",
                "/asw/bin/camera_center",
                "/asw/bin/SomeipGateway_gateway",
                "/asw/bin/rbp-vis-jetour",
                "/asw/bin/RbpPmpPsdCcp_activity",
                "/asw/bin/RbpMca_activity",
                "/asw/bin/NetComGateway_gateway",
                "/asw/bin/RbpEnvPer_activity",
                "/asw/bin/RbpApaFct_activity",
                "/asw/bin/RbpMotCoreCn_activity",
                "/asw/bin/RaPathRecorder_gateway",
                "/asw/bin/Viper_activity",
                "/asw/bin/Fct_activity",
                "/asw/bin/DynamicFusion_activity",
                "/asw/bin/MotionControl_activity",
                "/asw/bin/Planning_activity",
                "/asw/bin/LocalMsf_activity",
                "/asw/bin/HttpGateway_gateway",
                "/asw/bin/RbpInfra_activity",
                "/asw/bin/RbpCoolMaster_gateway",
                "/asw/bin/MapEnhancement_activity",
                "/asw/bin/map_loc_provider",
                "/asw/bin/LocalMappingGateway_gateway",
                "/asw/bin/OnboardMapping_gateway",
                "/asw/bin/Mapsrv_activity",
                "/asw/bin/PlanningDriving_activity",
                "/asw/bin/T10_activity",
                "/asw/bin/T20_activity",
                "/asw/bin/EnvModel1v_activity",
                "/asw/bin/Sit_activity",
                "/asw/bin/Parameter_activity",
                "/asw/bin/ActivitySpp_activity",
                "/asw/bin/GwPdm_gateway",
                "/asw/bin/PrmGatewayMaster_gateway",
                "/asw/bin/RecordGateway_gateway",
                "/asw/bin/DataHubGateway_gateway",
                "/asw/bin/OnlineCalibration_activity"
            ]
            missing_processes = []
            running_processes = []
            for process in required_processes:
                if process in output:
                    running_processes.append(process)
                    log.debug(f"✅ 进程运行中: {process}")
                else:
                    missing_processes.append(process)
                    log.warning(f"❌ 进程缺失: {process}")
            total_processes = len(required_processes)
            running_count = len(running_processes)
            missing_count = len(missing_processes)
            log.info("--- 进程检查统计 ---")
            log.info(f"总进程数: {total_processes}")
            log.info(f"运行中: {running_count}")
            log.info(f"缺失: {missing_count}")
            all_running = missing_count == 0
            if all_running:
                log.info("✅ 所有必需进程都在运行。")
            else:
                log.warning(f"❌ {missing_count} 个进程缺失。")
            return all_running, running_processes, missing_processes
        except Exception as e:
            log.error(f"检查进程失败: {e} ({type(e).__name__})")
            return False, [], []

    def restart(self, max_wait=120, check_interval=5):
        try:
            log.info("正在重启J6E...")
            full_cmd = (
                "cd /opt/app/lcm_tester/bin/ && "
                "export LD_LIBRARY_PATH=/opt/vrte/lib:/opt/app/lib/:/usr/lib:/lib:/proc/boot:/opt/vrte/usr/lib && "
                "export ECUCFG_ENV_VAR_ROOTFOLDER=/opt/app/etc/ecu-cfg && "
                "export PROCESSIDENTIFIER=lcm_tester && "
                "./lcm_tester 40"
            )
            output, error = self.ssh_client.exec_command(full_cmd)
            log.debug(f"lcm_tester输出: {output}")
            if error:
                log.warning(f"lcm_tester错误: {error}")

            log.info("等待J6E重启...")
            waited = 0
            while waited < max_wait:
                log.info(f"等待{check_interval}秒后检查J6E状态...")
                time.sleep(check_interval)
                waited += check_interval
                # Re-check connection before checking status
                if not self.ssh_client.check_conection_and_retry():
                    log.warning("连接丢失，无法检查状态。继续等待...")
                    continue

                fps_ok, _ = self.check_fps()
                proc_ok, _, missing = self.check_processes()
                if fps_ok and proc_ok:
                    log.info("✅ J6E重启成功，FPS和进程检查通过！")
                    return True
                else:
                    log.info(f"检查未通过，继续等待... (fps_ok={fps_ok}, proc_ok={proc_ok})")
                    if not proc_ok:
                        log.warning(f"缺失的进程: {missing}")
            log.error(f"❌ 重启超时，最大等待{max_wait}秒。")
            return False
        except Exception as e:
            log.error(f"重启失败: {e}")
            return False

    def check_hil_mode(self):
        try:
            log.info("检查HIL模式...")
            full_cmd = (
                "cd /opt/app/lcm_tester/bin/ && "
                "export LD_LIBRARY_PATH=/opt/vrte/lib:/opt/app/lib/:/usr/lib:/lib:/proc/boot:/opt/vrte/usr/lib && "
                "export ECUCFG_ENV_VAR_ROOTFOLDER=/opt/app/etc/ecu-cfg && "
                "export PROCESSIDENTIFIER=lcm_tester && "
                "./lcm_tester 36"
            )
            output, error = self.ssh_client.exec_command(full_cmd)
            log.debug(f"lcm_tester 36 输出:\n{output}")
            if error:
                log.warning(f"lcm_tester 36 错误: {error}")
            hlappfg_match = re.search(r'\[Lcm_Core\] HlAppFg in \[(\w+)\]', output)
            socmode_match = re.search(r'soc mode:\s*(\w+)', output)
            hlappfg_ok = False
            socmode_ok = False
            hlappfg_val = None
            socmode_val = None
            if hlappfg_match:
                hlappfg_val = hlappfg_match.group(1)
                log.debug(f"HlAppFg方括号内容: {hlappfg_val}")
                if hlappfg_val == "HIL":
                    log.info("✅ HlAppFg 为 HIL")
                    hlappfg_ok = True
                else:
                    log.warning(f"❌ HlAppFg 不为 HIL，当前为: {hlappfg_val}")
            else:
                log.warning("未找到 [Lcm_Core] HlAppFg in [...] 字段")
            if socmode_match:
                socmode_val = socmode_match.group(1)
                log.debug(f"soc mode值: {socmode_val}")
                if socmode_val == "HIL":
                    log.info("✅ soc mode 为 HIL")
                    socmode_ok = True
                else:
                    log.warning(f"❌ soc mode 不为 HIL，当前为: {socmode_val}")
            else:
                log.warning("未找到 soc mode: 字段")
            all_ok = hlappfg_ok and socmode_ok
            return all_ok, {"HlAppFg": hlappfg_val, "soc_mode": socmode_val}, output
        except Exception as e:
            log.error(f"执行lcm_tester 36失败: {e}")
            return False, None, ""

    def switch_to_hil_mode(self):
        try:
            log.info("正在切换到HIL模式...")
            full_cmd = (
                "cd /opt/app/lcm_tester/bin/ && "
                "export LD_LIBRARY_PATH=/opt/vrte/lib:/opt/app/lib/:/usr/lib:/lib:/proc/boot:/opt/vrte/usr/lib && "
                "export ECUCFG_ENV_VAR_ROOTFOLDER=/opt/app/etc/ecu-cfg && "
                "export PROCESSIDENTIFIER=lcm_tester && "
                "./lcm_tester 6"
            )
            output, error = self.ssh_client.exec_command(full_cmd)
            log.debug(f"lcm_tester 6 输出:\n{output}")
            if error:
                log.warning(f"lcm_tester 6 错误: {error}")
            # Check if 'Set SysMode : HIL ::' is followed by 'success'
            match = re.search(r'Set SysMode : HIL ::\s*(\w+)', output)
            success_flag = bool(match and match.group(1).lower() == 'success')
            return success_flag, output
        except Exception as e:
            log.error(f"执行lcm_tester 6失败: {e}")
            return False, None

    def start_getk_process(self, max_retries=3):
        """
        检查并启动getk进程。
        """
        GETK_PROCESS_NAME = "./mta_etas_getkp_gateway -c mta_gateway_vgetk.json"
        CHECK_CMD = "ps -ef | grep getk"
        START_CMD = "cd /asw && ./scripts/setup_runtime_env.sh && ./scripts/start_record.sh -m getk"

        log.info("开始检查并启动getk进程...")

        # 首次检查，看进程是否已在运行
        output, _ = self.ssh_client.exec_command(CHECK_CMD)
        if GETK_PROCESS_NAME in output:
            log.info("✅ getk进程已在运行。")
            return True

        # 如果进程不存在，则尝试启动，并重试
        for attempt in range(max_retries):
            log.info(f"getk进程不存在，第 {attempt + 1}/{max_retries} 次尝试启动...")
            
            start_output, start_error = self.ssh_client.exec_command(START_CMD)
            log.debug(f"启动脚本输出: {start_output}")
            if start_error:
                log.warning(f"启动脚本错误: {start_error}")
            
            log.info("等待5秒让进程初始化...")
            time.sleep(5)

            # 再次检查进程是否存在
            output, _ = self.ssh_client.exec_command(CHECK_CMD)
            if GETK_PROCESS_NAME in output:
                log.info(f"✅ 在第 {attempt + 1} 次尝试后，getk进程成功启动。")
                return True

        log.error(f"❌ 经过 {max_retries} 次尝试后，无法启动getk进程。")
        return False


if __name__ == "__main__":
    # 从配置文件读取参数
    master_ecu_conf = conf.get_config_value("master_ecu_conf")
    host = master_ecu_conf["IP"]
    user = master_ecu_conf["username"]
    password = master_ecu_conf["password"]

    client = J6EClient(host, user, password)

    log.info("\n--- check_fps ---")
    fps_ok, fps_values = client.check_fps()
    log.info(f"fps_ok: {fps_ok}, fps_values: {fps_values}")

    log.info("\n--- check_processes ---")
    proc_ok, running, missing = client.check_processes()
    log.info(f"proc_ok: {proc_ok}, running_count: {len(running)}, missing_count: {len(missing)}")

    # log.info("\n--- restart ---")
    # restart_ok = client.restart()
    # log.info(f"restart_ok: {restart_ok}")

    log.info("\n--- switch_to_hil_mode ---")
    success, output = client.switch_to_hil_mode()
    log.info(f"success: {success}")
    log.debug(f"output: {output}")

    log.info("\n--- check_hil_mode ---")
    hil_ok, details, output = client.check_hil_mode()
    log.info(f"hil_ok: {hil_ok}, details: {details}")
    log.debug(f"output: {output}")

    log.info("\n--- start_getk_process ---")
    getk_ok = client.start_getk_process()
    log.info(f"getk_ok: {getk_ok}")

